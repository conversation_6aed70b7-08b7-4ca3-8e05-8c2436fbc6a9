# This sets the default behaviour, overriding core.autocrlf
* text=auto

# All source files should have unix line-endings in the repository,
# but convert to native line-endings on checkout
*.cpp text
*.h text
*.hpp text

# Windows specific files should retain windows line-endings
*.sln text eol=crlf

# Keep executable scripts with LFs so they can be run after being
# checked out on Windows
*.py text eol=lf


# Keep the single include header with LFs to make sure it is uploaded,
# hashed etc with LF
single_include/**/*.hpp eol=lf
# Also keep the LICENCE file with LFs for the same reason
LICENCE.txt eol=lf
