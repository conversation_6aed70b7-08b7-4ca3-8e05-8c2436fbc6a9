cmake_minimum_required(VERSION 3.10)
project(FactorEngine CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置vcpkg工具链文件路径, 请根据您的实际路径修改
# set(CMAKE_TOOLCHAIN_FILE "E:/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file")

# 添加头文件目录
include_directories(E:/BaseLibrary)
include_directories(E:/BaseLibrary/concurrentqueue)
include_directories(E:/BaseLibrary/boost_1_85_0)

# 使用vcpkg查找依赖项
find_package(Boost REQUIRED)
find_package(moodycamel-concurrentqueue REQUIRED)


# 添加可执行文件
add_executable(FactorEngine
    main.cpp
    DataTypes.h
    ThreadPool_Enhanced.h
    RollingWindow_SIMD.h
    FactorEngine_Enhanced.h
)

# 链接库
target_link_libraries(FactorEngine
    PRIVATE
        Boost::pool
        moodycamel::concurrentqueue
)
