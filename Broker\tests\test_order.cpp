#include "orders/Order.h"
#include "simple_tests.cpp"

using namespace RoboQuant::Broker;

TEST(OrderRequestValidation) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Limit;
    request.quantity = 1000;
    request.price = 10.50;
    request.account_id = "test_account";
    
    ASSERT_TRUE(request.is_valid());
    ASSERT_EQ("", request.validation_error());
}

TEST(OrderRequestInvalidAssetId) {
    OrderRequest request;
    request.asset_id = "";  // Invalid
    request.side = OrderSide::Buy;
    request.type = OrderType::Market;
    request.quantity = 1000;
    request.account_id = "test_account";
    
    ASSERT_FALSE(request.is_valid());
    ASSERT_EQ("Asset ID is required", request.validation_error());
}

TEST(OrderRequestInvalidQuantity) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Market;
    request.quantity = 0;  // Invalid
    request.account_id = "test_account";
    
    ASSERT_FALSE(request.is_valid());
    ASSERT_EQ("Quantity must be positive", request.validation_error());
}

TEST(OrderRequestLimitOrderWithoutPrice) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Limit;
    request.quantity = 1000;
    // Missing price for limit order
    request.account_id = "test_account";
    
    ASSERT_FALSE(request.is_valid());
    ASSERT_EQ("Limit orders require a price", request.validation_error());
}

TEST(OrderCreationFromRequest) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Limit;
    request.quantity = 1000;
    request.price = 10.50;
    request.account_id = "test_account";
    request.strategy_id = "test_strategy";
    
    Order order(request);
    
    ASSERT_FALSE(order.order_id().empty());
    ASSERT_EQ("000001.SZ", order.asset_id());
    ASSERT_EQ(OrderSide::Buy, order.side());
    ASSERT_EQ(OrderType::Limit, order.type());
    ASSERT_EQ(1000, order.quantity());
    ASSERT_EQ(0, order.filled_quantity());
    ASSERT_EQ(1000, order.remaining_quantity());
    ASSERT_TRUE(order.price().has_value());
    ASSERT_EQ(10.50, *order.price());
    ASSERT_EQ(OrderStatus::PendingNew, order.status());
    ASSERT_EQ("test_account", order.account_id());
    ASSERT_EQ("test_strategy", order.strategy_id());
    ASSERT_TRUE(order.is_valid());
}

TEST(OrderStatusUpdate) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Market;
    request.quantity = 1000;
    request.account_id = "test_account";
    
    Order order(request);
    ASSERT_EQ(OrderStatus::PendingNew, order.status());
    
    order.set_status(OrderStatus::New);
    ASSERT_EQ(OrderStatus::New, order.status());
    
    order.set_status(OrderStatus::Filled);
    ASSERT_EQ(OrderStatus::Filled, order.status());
}

TEST(OrderFillManagement) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Limit;
    request.quantity = 1000;
    request.price = 10.50;
    request.account_id = "test_account";
    
    Order order(request);
    order.set_status(OrderStatus::New);
    
    // Partial fill
    order.add_fill(300, 10.48);
    ASSERT_EQ(300, order.filled_quantity());
    ASSERT_EQ(700, order.remaining_quantity());
    ASSERT_EQ(OrderStatus::PartiallyFilled, order.status());
    ASSERT_EQ(10.48, order.average_fill_price());
    ASSERT_TRUE(order.is_partially_filled());
    ASSERT_FALSE(order.is_filled());
    ASSERT_TRUE(order.is_active());
    
    // Complete fill
    order.add_fill(700, 10.52);
    ASSERT_EQ(1000, order.filled_quantity());
    ASSERT_EQ(0, order.remaining_quantity());
    ASSERT_EQ(OrderStatus::Filled, order.status());
    ASSERT_TRUE(order.is_filled());
    ASSERT_FALSE(order.is_active());
    ASSERT_TRUE(order.is_terminal());
    
    // Check average fill price calculation
    double expected_avg = (300 * 10.48 + 700 * 10.52) / 1000;
    ASSERT_EQ(expected_avg, order.average_fill_price());
}

TEST(OrderErrorHandling) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Market;
    request.quantity = 1000;
    request.account_id = "test_account";
    
    Order order(request);
    
    ASSERT_EQ("", order.last_error());
    
    order.set_error("Test error message");
    ASSERT_EQ("Test error message", order.last_error());
}

TEST(OrderActiveStatus) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Market;
    request.quantity = 1000;
    request.account_id = "test_account";
    
    Order order(request);
    
    // Active statuses
    order.set_status(OrderStatus::PendingNew);
    ASSERT_TRUE(order.is_active());
    ASSERT_FALSE(order.is_terminal());
    
    order.set_status(OrderStatus::New);
    ASSERT_TRUE(order.is_active());
    ASSERT_FALSE(order.is_terminal());
    
    order.set_status(OrderStatus::PartiallyFilled);
    ASSERT_TRUE(order.is_active());
    ASSERT_FALSE(order.is_terminal());
    
    order.set_status(OrderStatus::PendingCancel);
    ASSERT_TRUE(order.is_active());
    ASSERT_FALSE(order.is_terminal());
    
    // Terminal statuses
    order.set_status(OrderStatus::Filled);
    ASSERT_FALSE(order.is_active());
    ASSERT_TRUE(order.is_terminal());
    
    order.set_status(OrderStatus::Cancelled);
    ASSERT_FALSE(order.is_active());
    ASSERT_TRUE(order.is_terminal());
    
    order.set_status(OrderStatus::Rejected);
    ASSERT_FALSE(order.is_active());
    ASSERT_TRUE(order.is_terminal());
    
    order.set_status(OrderStatus::Expired);
    ASSERT_FALSE(order.is_active());
    ASSERT_TRUE(order.is_terminal());
}

TEST(OrderJSONSerialization) {
    OrderRequest request;
    request.asset_id = "000001.SZ";
    request.side = OrderSide::Buy;
    request.type = OrderType::Limit;
    request.quantity = 1000;
    request.price = 10.50;
    request.account_id = "test_account";
    request.strategy_id = "test_strategy";
    request.metadata["key1"] = "value1";
    request.metadata["key2"] = "value2";
    
    // Test OrderRequest serialization
    auto request_json = request.to_json();
    ASSERT_EQ("000001.SZ", request_json["asset_id"]);
    ASSERT_EQ("Buy", request_json["side"]);
    ASSERT_EQ("Limit", request_json["type"]);
    ASSERT_EQ(1000, request_json["quantity"]);
    ASSERT_EQ(10.50, request_json["price"]);
    ASSERT_EQ("test_account", request_json["account_id"]);
    ASSERT_EQ("test_strategy", request_json["strategy_id"]);
    
    // Test OrderRequest deserialization
    auto restored_request = OrderRequest::from_json(request_json);
    ASSERT_EQ(request.asset_id, restored_request.asset_id);
    ASSERT_EQ(request.side, restored_request.side);
    ASSERT_EQ(request.type, restored_request.type);
    ASSERT_EQ(request.quantity, restored_request.quantity);
    ASSERT_EQ(request.price, restored_request.price);
    ASSERT_EQ(request.account_id, restored_request.account_id);
    ASSERT_EQ(request.strategy_id, restored_request.strategy_id);
    
    // Test Order serialization
    Order order(request);
    order.set_status(OrderStatus::New);
    order.add_fill(300, 10.48);
    
    auto order_json = order.to_json();
    ASSERT_FALSE(order_json["order_id"].get<std::string>().empty());
    ASSERT_EQ("000001.SZ", order_json["asset_id"]);
    ASSERT_EQ("PartiallyFilled", order_json["status"]);
    ASSERT_EQ(300, order_json["filled_quantity"]);
    ASSERT_EQ(10.48, order_json["average_fill_price"]);
    
    // Test Order deserialization
    auto restored_order = Order::from_json(order_json);
    ASSERT_EQ(order.order_id(), restored_order.order_id());
    ASSERT_EQ(order.asset_id(), restored_order.asset_id());
    ASSERT_EQ(order.status(), restored_order.status());
    ASSERT_EQ(order.filled_quantity(), restored_order.filled_quantity());
    ASSERT_EQ(order.average_fill_price(), restored_order.average_fill_price());
}

TEST(OrderUtilityFunctions) {
    OrderRequest buy_request;
    buy_request.asset_id = "000001.SZ";
    buy_request.side = OrderSide::Buy;
    buy_request.type = OrderType::Market;
    buy_request.quantity = 1000;
    buy_request.account_id = "test_account";
    
    OrderRequest sell_request;
    sell_request.asset_id = "000001.SZ";
    sell_request.side = OrderSide::Sell;
    sell_request.type = OrderType::Limit;
    sell_request.quantity = 1000;
    sell_request.price = 10.50;
    sell_request.account_id = "test_account";
    
    Order buy_order(buy_request);
    Order sell_order(sell_request);
    
    ASSERT_TRUE(is_buy_order(buy_order));
    ASSERT_FALSE(is_sell_order(buy_order));
    ASSERT_TRUE(is_market_order(buy_order));
    ASSERT_FALSE(is_limit_order(buy_order));
    
    ASSERT_FALSE(is_buy_order(sell_order));
    ASSERT_TRUE(is_sell_order(sell_order));
    ASSERT_FALSE(is_market_order(sell_order));
    ASSERT_TRUE(is_limit_order(sell_order));
}

TEST(OrderValidation) {
    OrderRequest valid_request;
    valid_request.asset_id = "000001.SZ";
    valid_request.side = OrderSide::Buy;
    valid_request.type = OrderType::Limit;
    valid_request.quantity = 1000;
    valid_request.price = 10.50;
    valid_request.account_id = "test_account";
    
    auto validation_result = validate_order_request(valid_request);
    ASSERT_TRUE(is_success(validation_result));
    
    Order valid_order(valid_request);
    auto order_validation_result = validate_order(valid_order);
    ASSERT_TRUE(is_success(order_validation_result));
    
    // Test invalid order
    OrderRequest invalid_request;
    invalid_request.asset_id = "";  // Invalid
    invalid_request.side = OrderSide::Buy;
    invalid_request.type = OrderType::Market;
    invalid_request.quantity = 1000;
    invalid_request.account_id = "test_account";
    
    auto invalid_validation_result = validate_order_request(invalid_request);
    ASSERT_TRUE(is_error(invalid_validation_result));
}
