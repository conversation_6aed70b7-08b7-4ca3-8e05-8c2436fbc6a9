
#pragma once

#include "ThreadPool.h"
#include "RollingWindow.h"
#include "DataTypes.h"
#include <string>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <vector>
#include <utility>
#include <iostream>

namespace QuantEngine {

// Represents a single security and all its factors
class Security {
public:
    Security(const std::string& ticker) : m_ticker(ticker) {}

    void addFactor(const std::string& factor_name, size_t window_size) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_factors.emplace(factor_name, RollingWindow(window_size));
    }

    // This is the thread-safe update method
    void updateFactor(const std::string& factor_name, double value) {
        std::lock_guard<std::mutex> lock(m_mutex);
        try {
            m_factors.at(factor_name).push(value);
        } catch (const std::out_of_range& e) {
            // In a real system, log this error
        }
    }
  
    // Get a standardized value on-demand
    double getStandardizedValue(const std::string& factor_name, double current_value) {
        std::lock_guard<std::mutex> lock(m_mutex);
        try {
            return m_factors.at(factor_name).standardize(current_value);
        } catch (const std::out_of_range& e) {
            return 0.0; // Or handle error appropriately
        }
    }

private:
    std::string m_ticker;
    std::unordered_map<std::string, RollingWindow> m_factors;
    std::mutex m_mutex;
};

// Represents a single market data update


// The main engine
class FactorEngine {
public:
    FactorEngine(size_t num_threads = std::thread::hardware_concurrency()) : m_pool(num_threads) {}

    void addSecurity(const std::string& ticker, const std::vector<std::pair<std::string, size_t>>& factor_configs) {
        std::lock_guard<std::mutex> lock(m_securities_mutex);
        auto security = std::make_shared<Security>(ticker);
        for (const auto& config : factor_configs) {
            security->addFactor(config.first, config.second);
        }
        m_securities[ticker] = security;
    }
  
    // Main entry point for incoming market data
    void processTick(const TickData& tick) {
        m_pool.enqueue([this, tick] {
            std::shared_ptr<Security> security;
            {
                std::lock_guard<std::mutex> lock(m_securities_mutex);
                auto it = m_securities.find(tick.ticker);
                if (it == m_securities.end()) {
                    return; // Security not found
                }
                security = it->second;
            }
            security->updateFactor(tick.factor_name, tick.value);
        });
    }

    // On-demand query for a standardized value
    double queryStandardizedValue(const std::string& ticker, const std::string& factor_name, double current_value) {
        std::lock_guard<std::mutex> lock(m_securities_mutex);
        auto it = m_securities.find(ticker);
        if (it != m_securities.end()) {
            return it->second->getStandardizedValue(factor_name, current_value);
        }
        return 0.0; // Not found
    }

private:
    ThreadPool m_pool;
    std::unordered_map<std::string, std::shared_ptr<Security>> m_securities;
    std::mutex m_securities_mutex;
};

} // namespace QuantEngine
