#pragma once

#include "core/Types.h"
#include "core/ErrorHandling.h"
#include <nlohmann/json.hpp>
#include <filesystem>
#include <functional>
#include <shared_mutex>
#include <atomic>
#include <thread>

namespace RoboQuant::Broker {

// Configuration value wrapper
class ConfigValue {
public:
    ConfigValue() = default;
    ConfigValue(nlohmann::json value) : value_(std::move(value)) {}
    
    template<typename T>
    ConfigValue(T&& value) : value_(std::forward<T>(value)) {}
    
    // Type-safe getters
    template<typename T>
    T get() const {
        return value_.get<T>();
    }
    
    template<typename T>
    T get_or(const T& default_value) const {
        try {
            return value_.get<T>();
        } catch (...) {
            return default_value;
        }
    }
    
    // Type checking
    bool is_null() const { return value_.is_null(); }
    bool is_boolean() const { return value_.is_boolean(); }
    bool is_number() const { return value_.is_number(); }
    bool is_string() const { return value_.is_string(); }
    bool is_array() const { return value_.is_array(); }
    bool is_object() const { return value_.is_object(); }
    
    // JSON access
    const nlohmann::json& json() const { return value_; }
    nlohmann::json& json() { return value_; }
    
    // Operators
    bool operator==(const ConfigValue& other) const { return value_ == other.value_; }
    bool operator!=(const ConfigValue& other) const { return value_ != other.value_; }

private:
    nlohmann::json value_;
};

// Configuration change notification
struct ConfigChange {
    std::string key;
    ConfigValue old_value;
    ConfigValue new_value;
    TimePoint timestamp{std::chrono::system_clock::now()};
    std::string source; // file, api, etc.
    
    nlohmann::json to_json() const;
    static ConfigChange from_json(const nlohmann::json& j);
};

using ConfigChangeCallback = std::function<void(const ConfigChange&)>;

// Configuration section
class ConfigSection {
public:
    ConfigSection() = default;
    explicit ConfigSection(const nlohmann::json& data) : data_(data) {}
    
    // Value access
    ConfigValue get(const std::string& key) const;
    ConfigValue get(const std::string& key, const ConfigValue& default_value) const;
    
    template<typename T>
    T get(const std::string& key) const {
        return get(key).get<T>();
    }
    
    template<typename T>
    T get(const std::string& key, const T& default_value) const {
        return get(key, ConfigValue(default_value)).get<T>();
    }
    
    // Value setting
    void set(const std::string& key, const ConfigValue& value);
    
    template<typename T>
    void set(const std::string& key, T&& value) {
        set(key, ConfigValue(std::forward<T>(value)));
    }
    
    // Key operations
    bool has(const std::string& key) const;
    void remove(const std::string& key);
    std::vector<std::string> keys() const;
    
    // Section operations
    ConfigSection get_section(const std::string& key) const;
    void set_section(const std::string& key, const ConfigSection& section);
    
    // JSON operations
    nlohmann::json to_json() const { return data_; }
    void from_json(const nlohmann::json& j) { data_ = j; }
    
    // Validation
    bool validate_schema(const nlohmann::json& schema) const;

private:
    nlohmann::json data_;
};

// Main configuration manager
class ConfigurationManager {
public:
    static ConfigurationManager& instance();
    
    // Configuration loading
    Result<void> load_from_file(const std::filesystem::path& file_path);
    Result<void> load_from_string(const std::string& json_str);
    Result<void> load_from_json(const nlohmann::json& json);
    
    // Configuration saving
    Result<void> save_to_file(const std::filesystem::path& file_path) const;
    std::string save_to_string() const;
    nlohmann::json save_to_json() const;
    
    // Value access
    ConfigValue get(const std::string& key) const;
    ConfigValue get(const std::string& key, const ConfigValue& default_value) const;
    
    template<typename T>
    T get(const std::string& key) const {
        return get(key).get<T>();
    }
    
    template<typename T>
    T get(const std::string& key, const T& default_value) const {
        return get(key, ConfigValue(default_value)).get<T>();
    }
    
    // Value setting
    void set(const std::string& key, const ConfigValue& value);
    
    template<typename T>
    void set(const std::string& key, T&& value) {
        set(key, ConfigValue(std::forward<T>(value)));
    }
    
    // Section operations
    ConfigSection get_section(const std::string& key) const;
    void set_section(const std::string& key, const ConfigSection& section);
    
    // Key operations
    bool has(const std::string& key) const;
    void remove(const std::string& key);
    std::vector<std::string> keys() const;
    std::vector<std::string> keys(const std::string& prefix) const;
    
    // Hot reload support
    void enable_hot_reload(const std::filesystem::path& file_path, 
                          Duration check_interval = std::chrono::seconds(5));
    void disable_hot_reload();
    bool is_hot_reload_enabled() const { return hot_reload_enabled_.load(); }
    
    // Change notifications
    void subscribe_to_changes(const std::string& key_pattern, ConfigChangeCallback callback);
    void unsubscribe_from_changes(const std::string& key_pattern);
    
    // Validation
    Result<void> set_schema(const nlohmann::json& schema);
    Result<void> validate() const;
    bool is_valid() const;
    
    // Environment variable support
    void enable_env_override(const std::string& prefix = "BROKER_");
    void disable_env_override();
    
    // Configuration profiles
    void set_active_profile(const std::string& profile);
    std::string get_active_profile() const;
    std::vector<std::string> get_available_profiles() const;
    
    // Backup and restore
    Result<void> create_backup(const std::string& backup_name = "");
    Result<void> restore_backup(const std::string& backup_name);
    std::vector<std::string> list_backups() const;
    
    // Statistics
    size_t total_keys() const;
    TimePoint last_modified() const { return last_modified_; }
    size_t change_count() const { return change_count_.load(); }

private:
    ConfigurationManager() = default;
    ~ConfigurationManager();
    
    // Non-copyable, non-movable
    ConfigurationManager(const ConfigurationManager&) = delete;
    ConfigurationManager& operator=(const ConfigurationManager&) = delete;
    
    void notify_change(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value);
    void hot_reload_worker();
    void apply_env_overrides();
    std::string resolve_key_with_profile(const std::string& key) const;
    bool matches_pattern(const std::string& key, const std::string& pattern) const;
    
    mutable std::shared_mutex config_mutex_;
    nlohmann::json config_data_;
    nlohmann::json schema_;
    
    // Hot reload
    std::atomic<bool> hot_reload_enabled_{false};
    std::filesystem::path hot_reload_file_;
    Duration hot_reload_interval_{std::chrono::seconds(5)};
    std::thread hot_reload_thread_;
    std::filesystem::file_time_type last_file_time_;
    
    // Change notifications
    mutable std::shared_mutex callbacks_mutex_;
    std::unordered_map<std::string, std::vector<ConfigChangeCallback>> change_callbacks_;
    
    // Environment variables
    bool env_override_enabled_{false};
    std::string env_prefix_;
    
    // Profiles
    std::string active_profile_;
    
    // Backups
    mutable std::shared_mutex backups_mutex_;
    std::unordered_map<std::string, nlohmann::json> backups_;
    
    // Statistics
    TimePoint last_modified_{std::chrono::system_clock::now()};
    std::atomic<size_t> change_count_{0};
};

// Configuration builder for fluent API
class ConfigBuilder {
public:
    ConfigBuilder& set(const std::string& key, const ConfigValue& value);
    
    template<typename T>
    ConfigBuilder& set(const std::string& key, T&& value) {
        return set(key, ConfigValue(std::forward<T>(value)));
    }
    
    ConfigBuilder& section(const std::string& key);
    ConfigBuilder& end_section();
    
    ConfigBuilder& from_file(const std::filesystem::path& file_path);
    ConfigBuilder& from_json(const nlohmann::json& json);
    
    Result<void> build();

private:
    nlohmann::json config_;
    std::vector<std::string> section_stack_;
};

// Predefined configuration schemas
namespace ConfigSchemas {
    extern const nlohmann::json BrokerConfig;
    extern const nlohmann::json OrderManagerConfig;
    extern const nlohmann::json AccountManagerConfig;
    extern const nlohmann::json EventSystemConfig;
    extern const nlohmann::json LoggingConfig;
}

// Configuration utilities
Result<nlohmann::json> load_json_file(const std::filesystem::path& file_path);
Result<void> save_json_file(const std::filesystem::path& file_path, const nlohmann::json& json);
Result<void> validate_json_schema(const nlohmann::json& data, const nlohmann::json& schema);

// Environment variable helpers
std::optional<std::string> get_env_var(const std::string& name);
std::string get_env_var(const std::string& name, const std::string& default_value);

// Configuration macros for convenience
#define CONFIG_GET(key, type) \
    ConfigurationManager::instance().get<type>(key)

#define CONFIG_GET_OR(key, type, default_val) \
    ConfigurationManager::instance().get<type>(key, default_val)

#define CONFIG_SET(key, value) \
    ConfigurationManager::instance().set(key, value)

#define CONFIG_SECTION(key) \
    ConfigurationManager::instance().get_section(key)

} // namespace RoboQuant::Broker
