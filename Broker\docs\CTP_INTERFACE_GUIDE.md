# CTP 接口使用指南

## 概述

CTP (Comprehensive Transaction Platform) 接口是上海期货信息技术有限公司开发的期货交易系统接口。本文档介绍如何使用 Broker_Modern 中的现代化 CTP 接口实现。

## 特性

### ? 核心功能
- **完整的 CTP API 封装**: 支持交易、查询、行情订阅等所有功能
- **现代 C++20 设计**: 使用智能指针、RAII、异常安全等现代特性
- **异步处理**: 基于事件驱动的异步处理架构
- **线程安全**: 全面的线程安全保护和无锁优化
- **错误恢复**: 智能的错误检测和自动恢复机制

### ?? 高级特性
- **配置管理**: 灵活的配置管理和热更新支持
- **性能优化**: 自适应请求间隔和连接池管理
- **监控告警**: 实时性能监控和告警机制
- **缓存优化**: 智能缓存减少不必要的查询请求
- **批量处理**: 请求批处理和优先级队列

## 快速开始

### 1. 基本设置

```cpp
#include "brokers/CtpBroker.h"
#include "brokers/CtpConfigManager.h"

// 注册 CTP 接口
register_ctp_broker();

// 创建配置
CtpConfig config;
config.front_address = "tcp://***************:10130";
config.broker_id = "9999";
config.user_id = "your_user_id";
config.password = "your_password";
config.app_id = "your_app_id";
config.auth_code = "your_auth_code";

// 创建 CTP 接口实例
auto ctp_broker = std::make_shared<CtpBroker>(config);
```

### 2. 连接和认证

```cpp
// 连接到服务器
ConnectionInfo conn_info(BrokerType::CTP, "***************", 10130);
auto connect_result = ctp_broker->connect(conn_info);
if (!connect_result) {
    std::cerr << "连接失败: " << connect_result.error().message << std::endl;
    return;
}

// 认证和登录
auto auth_result = ctp_broker->authenticate(config.user_id, config.password);
if (!auth_result) {
    std::cerr << "认证失败: " << auth_result.error().message << std::endl;
    return;
}
```

### 3. 设置回调函数

```cpp
// 订单回调
ctp_broker->set_order_callback([](const Order& order) {
    std::cout << "订单更新: " << order.order_id() 
              << " 状态: " << to_string(order.status()) << std::endl;
});

// 交易回调
ctp_broker->set_trade_callback([](const Trade& trade) {
    std::cout << "交易回报: " << trade.trade_id() 
              << " " << trade.quantity() << " @ " << trade.price() << std::endl;
});

// 账户回调
ctp_broker->set_account_callback([](const Account& account) {
    std::cout << "账户更新: " << account.account_id() 
              << " 余额: " << account.balance().total_balance << std::endl;
});
```

## 配置管理

### 配置管理器使用

```cpp
auto& config_manager = CtpConfigManager::instance();

// 添加服务器配置
CtpServerConfig server_config;
server_config.name = "Production_Server";
server_config.trade_front = "tcp://***************:10130";
server_config.md_front = "tcp://***************:10131";
server_config.broker_id = "9999";
server_config.environment = CtpEnvironment::Production;
config_manager.add_server_config(server_config);

// 添加账户配置
CtpAccountConfig account_config;
account_config.account_id = "test_account";
account_config.user_id = "123456";
account_config.password = "password";
account_config.server_name = "Production_Server";
config_manager.add_account_config(account_config);

// 生成 CTP 配置
auto ctp_config = config_manager.generate_ctp_config("test_account");
```

### 配置文件格式

```json
{
  "servers": [
    {
      "name": "Production_Server",
      "trade_front": "tcp://***************:10130",
      "md_front": "tcp://***************:10131",
      "broker_id": "9999",
      "environment": "Production",
      "is_active": true,
      "priority": 0
    }
  ],
  "accounts": [
    {
      "account_id": "test_account",
      "user_id": "123456",
      "password": "password",
      "app_id": "your_app_id",
      "auth_code": "your_auth_code",
      "server_name": "Production_Server",
      "is_active": true,
      "max_position_value": 1000000.0,
      "max_order_value": 100000.0,
      "max_orders_per_second": 10
    }
  ],
  "trading": {
    "request_interval_ms": 1000,
    "connect_timeout_ms": 10000,
    "heartbeat_interval_ms": 30000,
    "auto_reconnect": true,
    "max_reconnect_attempts": 5,
    "enable_order_validation": true,
    "enable_risk_check": true
  },
  "environment": "Production"
}
```

## 交易操作

### 提交订单

```cpp
// 创建订单请求
OrderRequest request;
request.asset_id = "rb2501";
request.side = OrderSide::Buy;
request.type = OrderType::Limit;
request.quantity = 1;
request.price = 3500.0;
request.account_id = "test_account";

// 创建订单
Order order(request);

// 提交订单
auto submit_result = ctp_broker->submit_order(order);
if (submit_result) {
    std::cout << "订单已提交: " << order.order_id() << std::endl;
} else {
    std::cerr << "订单提交失败: " << submit_result.error().message << std::endl;
}
```

### 撤销订单

```cpp
// 撤销订单
auto cancel_result = ctp_broker->cancel_order(order);
if (cancel_result) {
    std::cout << "撤单请求已发送" << std::endl;
} else {
    std::cerr << "撤单失败: " << cancel_result.error().message << std::endl;
}
```

### 查询操作

```cpp
// 查询账户
auto accounts_result = ctp_broker->query_accounts();
if (accounts_result) {
    for (const auto& account : accounts_result.value()) {
        std::cout << "账户: " << account.account_id() 
                  << " 余额: " << account.balance().total_balance << std::endl;
    }
}

// 查询持仓
auto positions_result = ctp_broker->query_positions();
if (positions_result) {
    for (const auto& position : positions_result.value()) {
        std::cout << "持仓: " << position.asset_id() 
                  << " 数量: " << position.quantity() << std::endl;
    }
}

// 查询订单
auto orders_result = ctp_broker->query_orders();
if (orders_result) {
    for (const auto& order : orders_result.value()) {
        std::cout << "订单: " << order.order_id() 
                  << " 状态: " << to_string(order.status()) << std::endl;
    }
}
```

## 行情订阅

```cpp
// 订阅行情
auto subscribe_result = ctp_broker->subscribe_market_data("rb2501");
if (subscribe_result) {
    std::cout << "行情订阅成功" << std::endl;
}

// 退订行情
auto unsubscribe_result = ctp_broker->unsubscribe_market_data("rb2501");
if (unsubscribe_result) {
    std::cout << "行情退订成功" << std::endl;
}
```

## 性能优化

### 使用优化器

```cpp
#include "brokers/CtpOptimizer.h"

// 创建优化器
auto optimizer = std::make_unique<CtpOptimizer>(ctp_broker);
optimizer->enable_optimization(true);

// 配置请求优化器
auto& request_optimizer = optimizer->get_request_optimizer();
// 自动调整请求间隔

// 配置错误恢复
auto& error_recovery = optimizer->get_error_recovery();
CtpErrorRecovery::RecoveryStrategy strategy;
strategy.max_retry_count = 3;
strategy.retry_interval = std::chrono::seconds(5);
strategy.auto_reconnect = true;
error_recovery.set_recovery_strategy(CtpErrorRecovery::ErrorType::NetworkError, strategy);

// 启用缓存
auto& cache_manager = optimizer->get_cache_manager();
// 自动缓存订单、账户、持仓等数据

// 启用监控
auto& monitor = optimizer->get_monitor();
monitor.set_alert_callback([](const std::string& alert_type, const std::string& message) {
    std::cout << "告警: [" << alert_type << "] " << message << std::endl;
});
monitor.start_monitoring(ctp_broker);
```

### 连接池使用

```cpp
// 创建连接池
CtpConnectionPool pool(3); // 3个连接

// 预连接
std::vector<CtpConfig> configs = {config1, config2, config3};
pool.preconnect(configs);

// 获取连接
auto broker = pool.acquire_connection(config);
// 使用连接进行交易操作
// ...

// 释放连接
pool.release_connection(broker);
```

## 错误处理

### 错误类型

CTP 接口定义了以下错误类型：
- `NetworkError`: 网络连接错误
- `AuthError`: 认证错误
- `ApiError`: API 调用错误
- `TimeoutError`: 超时错误
- `UnknownError`: 未知错误

### 错误恢复策略

```cpp
CtpErrorRecovery error_recovery;

// 设置网络错误恢复策略
CtpErrorRecovery::RecoveryStrategy network_strategy;
network_strategy.max_retry_count = 3;
network_strategy.retry_interval = std::chrono::seconds(5);
network_strategy.auto_reconnect = true;
network_strategy.reset_session = false;
error_recovery.set_recovery_strategy(CtpErrorRecovery::ErrorType::NetworkError, network_strategy);

// 处理错误
bool recovery_success = error_recovery.handle_error(
    CtpErrorRecovery::ErrorType::NetworkError, 
    "Connection lost", 
    ctp_broker
);
```

## 监控和告警

### 性能监控

```cpp
CtpMonitor monitor;

// 设置告警回调
monitor.set_alert_callback([](const std::string& alert_type, const std::string& message) {
    // 发送告警通知
    send_alert_notification(alert_type, message);
});

// 启动监控
monitor.start_monitoring(ctp_broker);

// 获取性能统计
auto stats = monitor.get_performance_stats();
std::cout << "成功率: " << stats.get_success_rate() << std::endl;
std::cout << "平均延迟: " << stats.avg_request_latency_ms.load() << "ms" << std::endl;

// 生成监控报告
auto report = monitor.generate_monitoring_report();
std::cout << report.dump(2) << std::endl;
```

### 性能基准测试

```cpp
// 连接基准测试
auto connection_benchmark = CtpBenchmark::run_connection_benchmark(config, 100);
std::cout << "连接平均延迟: " << connection_benchmark.avg_latency_ms << "ms" << std::endl;

// 订单基准测试
auto order_benchmark = CtpBenchmark::run_order_benchmark(ctp_broker, 100);
std::cout << "订单平均延迟: " << order_benchmark.avg_latency_ms << "ms" << std::endl;

// 完整基准测试
auto full_benchmark = CtpBenchmark::run_full_benchmark(config);
std::cout << "基准测试报告:\n" << full_benchmark.dump(2) << std::endl;
```

## 最佳实践

### 1. 连接管理
- 使用连接池管理多个连接
- 启用自动重连机制
- 设置合适的心跳间隔

### 2. 请求优化
- 使用自适应请求间隔
- 启用请求批处理
- 设置合理的重试策略

### 3. 错误处理
- 实现完善的错误恢复机制
- 记录详细的错误日志
- 设置告警通知

### 4. 性能监控
- 启用实时性能监控
- 设置性能阈值告警
- 定期生成性能报告

### 5. 配置管理
- 使用配置文件管理参数
- 支持环境变量覆盖
- 实现配置热更新

## 常见问题

### Q: 如何处理 CTP 连接断开？
A: 启用自动重连机制，设置合理的重连间隔和最大重连次数。

### Q: 如何优化订单提交性能？
A: 使用自适应请求间隔，启用请求批处理，合理设置请求优先级。

### Q: 如何处理 CTP 认证失败？
A: 检查用户凭据，确保 AppID 和 AuthCode 正确，实现认证重试机制。

### Q: 如何监控 CTP 接口性能？
A: 使用内置的性能监控器，设置告警阈值，定期生成性能报告。

### Q: 如何处理 CTP 流控限制？
A: 使用自适应请求间隔，根据服务器响应调整请求频率。

## 参考资料

- [CTP API 官方文档](http://www.sfit.com.cn/)
- [SimNow 仿真交易](http://www.simnow.com.cn/)
- [Broker_Modern 架构文档](./ARCHITECTURE.md)
- [性能优化指南](./PERFORMANCE_GUIDE.md)
