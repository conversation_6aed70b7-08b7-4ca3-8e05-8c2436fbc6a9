^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-MKDIR.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-DOWNLOAD.RULE
setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-PATCH.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-CONFIGURE.RULE
setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-BUILD.RULE
setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-INSTALL.RULE
setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\A96121C4600E7DDA73F761D6C6A1C5BC\FMT-POPULATE-TEST.RULE
setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\40477B9271A0934FCD99E6B323098173\FMT-POPULATE-COMPLETE.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E make_directory E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/Debug/fmt-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\C23AB31BC2DC3872CB399A1188E82F3F\FMT-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\EXPERIMENTS\BUILD\_DEPS\FMT-SUBBUILD\CMAKELISTS.TXT
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild -BE:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild --check-stamp-file E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
