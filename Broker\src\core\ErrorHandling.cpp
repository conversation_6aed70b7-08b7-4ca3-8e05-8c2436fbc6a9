#include "core/ErrorHandling.h"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace RoboQuant::Broker {

// ErrorInfo implementation
ErrorInfo::Severity ErrorInfo::get_severity() const {
    switch (code) {
        case ErrorCode::Success:
            return Severity::Info;
        case ErrorCode::InvalidParameter:
        case ErrorCode::OrderNotFound:
        case ErrorCode::AccountNotFound:
            return Severity::Warning;
        case ErrorCode::InsufficientFunds:
        case ErrorCode::OrderRejected:
        case ErrorCode::PermissionDenied:
            return Severity::Error;
        case ErrorCode::ConnectionError:
        case ErrorCode::AuthenticationError:
        case ErrorCode::BrokerError:
        case ErrorCode::NetworkError:
        case ErrorCode::TimeoutError:
        case ErrorCode::InternalError:
            return Severity::Critical;
        default:
            return Severity::Error;
    }
}

nlohmann::json ErrorInfo::to_json() const {
    nlohmann::json j;
    j["code"] = static_cast<uint32_t>(code);
    j["code_name"] = to_string(code);
    j["message"] = message;
    j["category"] = category;
    j["severity"] = static_cast<int>(get_severity());
    
    // Location information
    j["location"] = {
        {"file", location.file_name()},
        {"function", location.function_name()},
        {"line", location.line()},
        {"column", location.column()}
    };
    
    // Timestamp
    j["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()).count();
    
    // Context
    j["context"] = context;
    
    // Stack trace (if available)
    if (stack_trace) {
        j["stack_trace"] = nlohmann::json::array();
        for (const auto& entry : *stack_trace) {
            j["stack_trace"].push_back(entry.description());
        }
    }
    
    return j;
}

ErrorInfo ErrorInfo::from_json(const nlohmann::json& j) {
    ErrorInfo error;
    error.code = static_cast<ErrorCode>(j.at("code").get<uint32_t>());
    error.message = j.at("message").get<std::string>();
    error.category = j.value("category", "");
    error.context = j.value("context", std::unordered_map<std::string, std::string>{});
    
    if (j.contains("timestamp")) {
        auto ms = j["timestamp"].get<int64_t>();
        error.timestamp = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return error;
}

std::string ErrorInfo::to_string() const {
    std::ostringstream oss;
    oss << "[" << to_string(code) << "] " << message;
    
    if (!category.empty()) {
        oss << " (Category: " << category << ")";
    }
    
    if (!context.empty()) {
        oss << " Context: {";
        bool first = true;
        for (const auto& [key, value] : context) {
            if (!first) oss << ", ";
            oss << key << "=" << value;
            first = false;
        }
        oss << "}";
    }
    
    oss << " at " << location.file_name() << ":" << location.line();
    
    return oss.str();
}

// ErrorHandler implementation
ErrorHandler& ErrorHandler::instance() {
    static ErrorHandler handler;
    return handler;
}

void ErrorHandler::set_error_callback(ErrorCallback callback) {
    error_callback_ = std::move(callback);
}

void ErrorHandler::handle_error(const ErrorInfo& error) {
    // Update statistics
    total_errors_.fetch_add(1);
    
    {
        std::unique_lock<std::shared_mutex> lock(errors_mutex_);
        error_counts_[error.code]++;
        
        // Add to history
        error_history_.push_back(error);
        if (error_history_.size() > MAX_ERROR_HISTORY) {
            error_history_.erase(error_history_.begin());
        }
    }
    
    // Log the error
    log_error(error);
    
    // Try recovery
    if (try_recover(error)) {
        spdlog::info("Error recovery successful for: {}", to_string(error.code));
    }
    
    // Call user callback
    if (error_callback_) {
        try {
            error_callback_(error);
        } catch (const std::exception& e) {
            spdlog::error("Exception in error callback: {}", e.what());
        }
    }
}

void ErrorHandler::handle_error(ErrorCode code, const std::string& message, const std::string& category) {
    ErrorInfo error(code, message, category);
    handle_error(error);
}

size_t ErrorHandler::errors_by_code(ErrorCode code) const {
    std::shared_lock<std::shared_mutex> lock(errors_mutex_);
    auto it = error_counts_.find(code);
    return it != error_counts_.end() ? it->second : 0;
}

std::vector<ErrorInfo> ErrorHandler::recent_errors(size_t count) const {
    std::shared_lock<std::shared_mutex> lock(errors_mutex_);
    
    if (error_history_.size() <= count) {
        return error_history_;
    }
    
    return std::vector<ErrorInfo>(
        error_history_.end() - count,
        error_history_.end()
    );
}

void ErrorHandler::register_recovery_handler(ErrorCode code, std::function<bool(const ErrorInfo&)> handler) {
    std::unique_lock<std::shared_mutex> lock(errors_mutex_);
    recovery_handlers_[code] = std::move(handler);
}

bool ErrorHandler::try_recover(const ErrorInfo& error) {
    std::shared_lock<std::shared_mutex> lock(errors_mutex_);
    auto it = recovery_handlers_.find(error.code);
    if (it != recovery_handlers_.end()) {
        try {
            return it->second(error);
        } catch (const std::exception& e) {
            spdlog::error("Exception in recovery handler for {}: {}", 
                         to_string(error.code), e.what());
        }
    }
    return false;
}

// Utility functions
void log_error(const ErrorInfo& error) {
    auto severity = error.get_severity();
    
    switch (severity) {
        case ErrorInfo::Severity::Info:
            spdlog::info("Error: {}", error.to_string());
            break;
        case ErrorInfo::Severity::Warning:
            spdlog::warn("Error: {}", error.to_string());
            break;
        case ErrorInfo::Severity::Error:
            spdlog::error("Error: {}", error.to_string());
            break;
        case ErrorInfo::Severity::Critical:
            spdlog::critical("Critical Error: {}", error.to_string());
            break;
    }
}

void log_error(ErrorCode code, const std::string& message, const std::string& category) {
    ErrorInfo error(code, message, category);
    log_error(error);
}

// Error metrics
nlohmann::json ErrorMetrics::to_json() const {
    nlohmann::json j;
    j["total_errors"] = total_errors;
    j["error_rate"] = error_rate;
    
    j["error_counts"] = nlohmann::json::object();
    for (const auto& [code, count] : error_counts) {
        j["error_counts"][to_string(code)] = count;
    }
    
    j["category_counts"] = category_counts;
    
    if (last_error_time != TimePoint{}) {
        j["last_error_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            last_error_time.time_since_epoch()).count();
    }
    
    return j;
}

ErrorMetrics ErrorMetrics::from_json(const nlohmann::json& j) {
    ErrorMetrics metrics;
    metrics.total_errors = j.value("total_errors", 0);
    metrics.error_rate = j.value("error_rate", 0.0);
    
    if (j.contains("error_counts")) {
        for (const auto& [code_str, count] : j["error_counts"].items()) {
            auto code = parse_error_code(code_str);
            metrics.error_counts[code] = count.get<size_t>();
        }
    }
    
    metrics.category_counts = j.value("category_counts", 
        std::unordered_map<std::string, size_t>{});
    
    if (j.contains("last_error_time")) {
        auto ms = j["last_error_time"].get<int64_t>();
        metrics.last_error_time = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return metrics;
}

ErrorMetrics get_error_metrics() {
    auto& handler = ErrorHandler::instance();
    ErrorMetrics metrics;
    
    metrics.total_errors = handler.total_errors();
    
    // Calculate error counts by code
    for (int i = 0; i <= static_cast<int>(ErrorCode::Unknown); ++i) {
        auto code = static_cast<ErrorCode>(i);
        auto count = handler.errors_by_code(code);
        if (count > 0) {
            metrics.error_counts[code] = count;
        }
    }
    
    // Get recent errors for rate calculation
    auto recent = handler.recent_errors(100);
    if (recent.size() >= 2) {
        auto time_span = std::chrono::duration_cast<std::chrono::seconds>(
            recent.back().timestamp - recent.front().timestamp);
        if (time_span.count() > 0) {
            metrics.error_rate = static_cast<double>(recent.size()) / time_span.count();
        }
        metrics.last_error_time = recent.back().timestamp;
    }
    
    return metrics;
}

void reset_error_metrics() {
    // Note: This would require additional methods in ErrorHandler
    // For now, we'll just log the reset
    spdlog::info("Error metrics reset requested");
}

} // namespace RoboQuant::Broker
