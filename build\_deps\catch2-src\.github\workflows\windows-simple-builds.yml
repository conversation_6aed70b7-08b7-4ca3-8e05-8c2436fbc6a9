name: Windows builds (basic)

on: [push, pull_request]

jobs:
  build:
    name: ${{matrix.os}}, ${{matrix.std}}, ${{matrix.build_type}}, ${{matrix.platform}}
    runs-on: ${{matrix.os}}
    strategy:
      matrix:
        os: [windows-2019, windows-2022]
        platform: [Win32, x64]
        build_type: [Debug, Release]
        std: [14, 17]
    steps:
      - uses: actions/checkout@v2

      - name: Configure build
        working-directory: ${{runner.workspace}}
        run: |
          cmake -S $Env:GITHUB_WORKSPACE               `
                -B ${{runner.workspace}}/build         `
                -DCMAKE_CXX_STANDARD=${{matrix.std}}   `
                -A ${{matrix.platform}}                `
                --preset all-tests

      - name: Build tests
        working-directory: ${{runner.workspace}}
        run: cmake --build build --config ${{matrix.build_type}} --parallel %NUMBER_OF_PROCESSORS%
        shell: cmd

      - name: Run tests
        working-directory: ${{runner.workspace}}/build
        env:
            CTEST_OUTPUT_ON_FAILURE: 1
        run: ctest -C ${{matrix.build_type}} -j %NUMBER_OF_PROCESSORS%
        shell: cmd
