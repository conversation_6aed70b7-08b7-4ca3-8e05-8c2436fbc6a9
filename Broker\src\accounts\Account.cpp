#include "accounts/Account.h"
#include <spdlog/spdlog.h>
#include <random>
#include <sstream>
#include <iomanip>

namespace RoboQuant::Broker {

namespace {
    std::string generate_unique_id(const std::string& prefix) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<uint64_t> dis;
        
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        std::ostringstream oss;
        oss << prefix << "_" << timestamp << "_" << std::hex << dis(gen);
        return oss.str();
    }
}

// AccountBalance implementation
nlohmann::json AccountBalance::to_json() const {
    nlohmann::json j;
    j["total_balance"] = total_balance;
    j["available_balance"] = available_balance;
    j["frozen_balance"] = frozen_balance;
    j["margin_used"] = margin_used;
    j["margin_available"] = margin_available;
    j["unrealized_pnl"] = unrealized_pnl;
    j["realized_pnl"] = realized_pnl;
    j["commission_paid"] = commission_paid;
    j["currency"] = currency;
    j["net_liquidation_value"] = net_liquidation_value();
    j["buying_power"] = buying_power();
    j["margin_ratio"] = margin_ratio();
    return j;
}

AccountBalance AccountBalance::from_json(const nlohmann::json& j) {
    AccountBalance balance;
    balance.total_balance = j.value("total_balance", 0.0);
    balance.available_balance = j.value("available_balance", 0.0);
    balance.frozen_balance = j.value("frozen_balance", 0.0);
    balance.margin_used = j.value("margin_used", 0.0);
    balance.margin_available = j.value("margin_available", 0.0);
    balance.unrealized_pnl = j.value("unrealized_pnl", 0.0);
    balance.realized_pnl = j.value("realized_pnl", 0.0);
    balance.commission_paid = j.value("commission_paid", 0.0);
    balance.currency = j.value("currency", "CNY");
    return balance;
}

// RiskMetrics implementation
nlohmann::json RiskMetrics::to_json() const {
    nlohmann::json j;
    j["max_position_size"] = max_position_size;
    j["max_daily_loss"] = max_daily_loss;
    j["max_drawdown"] = max_drawdown;
    j["max_order_value"] = max_order_value;
    j["max_order_quantity"] = max_order_quantity;
    j["leverage_limit"] = leverage_limit;
    j["current_leverage"] = current_leverage;
    j["current_drawdown"] = current_drawdown;
    j["daily_pnl"] = daily_pnl;
    j["risk_limit_breached"] = risk_limit_breached;
    j["risk_message"] = risk_message;
    return j;
}

RiskMetrics RiskMetrics::from_json(const nlohmann::json& j) {
    RiskMetrics metrics;
    metrics.max_position_size = j.value("max_position_size", 0.1);
    metrics.max_daily_loss = j.value("max_daily_loss", 0.05);
    metrics.max_drawdown = j.value("max_drawdown", 0.2);
    metrics.max_order_value = j.value("max_order_value", 100000.0);
    metrics.max_order_quantity = j.value("max_order_quantity", 10000);
    metrics.leverage_limit = j.value("leverage_limit", 1.0);
    metrics.current_leverage = j.value("current_leverage", 0.0);
    metrics.current_drawdown = j.value("current_drawdown", 0.0);
    metrics.daily_pnl = j.value("daily_pnl", 0.0);
    metrics.risk_limit_breached = j.value("risk_limit_breached", false);
    metrics.risk_message = j.value("risk_message", "");
    return metrics;
}

// Account implementation
Account::Account(AccountId account_id, BrokerType broker_type, AccountType account_type)
    : account_id_(std::move(account_id))
    , broker_type_(broker_type)
    , account_type_(account_type)
    , create_time_(std::chrono::system_clock::now())
    , last_update_time_(create_time_) {
}

void Account::update_balance(const AccountBalance& new_balance) {
    balance_ = new_balance;
    update_timestamp();
    calculate_risk_metrics();
}

void Account::add_balance(Amount amount) {
    balance_.total_balance += amount;
    balance_.available_balance += amount;
    update_timestamp();
    calculate_risk_metrics();
}

void Account::subtract_balance(Amount amount) {
    balance_.total_balance -= amount;
    balance_.available_balance -= amount;
    update_timestamp();
    calculate_risk_metrics();
}

void Account::freeze_balance(Amount amount) {
    if (balance_.available_balance >= amount) {
        balance_.available_balance -= amount;
        balance_.frozen_balance += amount;
        update_timestamp();
    }
}

void Account::unfreeze_balance(Amount amount) {
    if (balance_.frozen_balance >= amount) {
        balance_.frozen_balance -= amount;
        balance_.available_balance += amount;
        update_timestamp();
    }
}

void Account::update_unrealized_pnl(Amount pnl) {
    balance_.unrealized_pnl = pnl;
    update_timestamp();
    calculate_risk_metrics();
}

void Account::add_realized_pnl(Amount pnl) {
    balance_.realized_pnl += pnl;
    balance_.total_balance += pnl;
    balance_.available_balance += pnl;
    update_timestamp();
    calculate_risk_metrics();
}

void Account::add_commission(Amount commission) {
    balance_.commission_paid += commission;
    balance_.total_balance -= commission;
    balance_.available_balance -= commission;
    update_timestamp();
}

void Account::update_risk_metrics(const RiskMetrics& metrics) {
    risk_metrics_ = metrics;
    update_timestamp();
}

void Account::set_risk_limit(const std::string& limit_type, double value) {
    if (limit_type == "max_position_size") {
        risk_metrics_.max_position_size = value;
    } else if (limit_type == "max_daily_loss") {
        risk_metrics_.max_daily_loss = value;
    } else if (limit_type == "max_drawdown") {
        risk_metrics_.max_drawdown = value;
    } else if (limit_type == "leverage_limit") {
        risk_metrics_.leverage_limit = value;
    }
    update_timestamp();
    calculate_risk_metrics();
}

bool Account::check_risk_limits(Amount order_value, Quantity order_quantity) const {
    // Check order value limit
    if (order_value > risk_metrics_.max_order_value) {
        return false;
    }
    
    // Check order quantity limit
    if (order_quantity > risk_metrics_.max_order_quantity) {
        return false;
    }
    
    // Check available balance
    if (order_value > balance_.available_balance) {
        return false;
    }
    
    // Check daily loss limit
    if (risk_metrics_.daily_pnl < -risk_metrics_.max_daily_loss * balance_.total_balance) {
        return false;
    }
    
    return true;
}

void Account::set_metadata(const std::string& key, const std::string& value) {
    metadata_[key] = value;
    update_timestamp();
}

std::optional<std::string> Account::get_metadata(const std::string& key) const {
    auto it = metadata_.find(key);
    return it != metadata_.end() ? std::make_optional(it->second) : std::nullopt;
}

void Account::remove_metadata(const std::string& key) {
    metadata_.erase(key);
    update_timestamp();
}

bool Account::is_valid() const {
    return !account_id_.empty() && 
           broker_type_ != BrokerType::Unknown &&
           balance_.total_balance >= 0;
}

std::string Account::validation_error() const {
    if (account_id_.empty()) return "Account ID is required";
    if (broker_type_ == BrokerType::Unknown) return "Valid broker type is required";
    if (balance_.total_balance < 0) return "Total balance cannot be negative";
    return "";
}

nlohmann::json Account::to_json() const {
    nlohmann::json j;
    j["account_id"] = account_id_;
    j["broker_type"] = to_string(broker_type_);
    j["account_type"] = to_string(account_type_);
    j["account_name"] = account_name_;
    j["owner_name"] = owner_name_;
    j["is_active"] = is_active_;
    j["trading_enabled"] = trading_enabled_;
    j["connection_status"] = to_string(connection_status_);
    j["balance"] = balance_.to_json();
    j["risk_metrics"] = risk_metrics_.to_json();
    
    j["create_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        create_time_.time_since_epoch()).count();
    j["last_update_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        last_update_time_.time_since_epoch()).count();
    
    j["metadata"] = metadata_;
    
    return j;
}

Account Account::from_json(const nlohmann::json& j) {
    Account account(
        j.at("account_id").get<std::string>(),
        parse_broker_type(j.at("broker_type").get<std::string>()),
        parse_account_type(j.value("account_type", "Cash"))
    );
    
    account.account_name_ = j.value("account_name", "");
    account.owner_name_ = j.value("owner_name", "");
    account.is_active_ = j.value("is_active", true);
    account.trading_enabled_ = j.value("trading_enabled", true);
    account.connection_status_ = parse_connection_status(j.value("connection_status", "Disconnected"));
    
    if (j.contains("balance")) {
        account.balance_ = AccountBalance::from_json(j["balance"]);
    }
    
    if (j.contains("risk_metrics")) {
        account.risk_metrics_ = RiskMetrics::from_json(j["risk_metrics"]);
    }
    
    if (j.contains("create_time")) {
        auto ms = j["create_time"].get<int64_t>();
        account.create_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    if (j.contains("last_update_time")) {
        auto ms = j["last_update_time"].get<int64_t>();
        account.last_update_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    account.metadata_ = j.value("metadata", std::unordered_map<std::string, std::string>{});
    
    return account;
}

std::string Account::to_string() const {
    std::ostringstream oss;
    oss << "Account[" << account_id_ << "] "
        << to_string(broker_type_) << " " << to_string(account_type_)
        << " Balance: " << std::fixed << std::setprecision(2) << balance_.total_balance
        << " " << balance_.currency;
    return oss.str();
}

bool Account::operator==(const Account& other) const noexcept {
    return account_id_ == other.account_id_ && broker_type_ == other.broker_type_;
}

void Account::calculate_risk_metrics() {
    // Calculate current leverage
    if (balance_.total_balance > 0) {
        risk_metrics_.current_leverage = balance_.margin_used / balance_.total_balance;
    }
    
    // Update daily P&L
    risk_metrics_.daily_pnl = balance_.realized_pnl;
    
    // Check risk limits
    bool risk_breached = false;
    std::string risk_message;
    
    if (risk_metrics_.current_leverage > risk_metrics_.leverage_limit) {
        risk_breached = true;
        risk_message += "Leverage limit exceeded; ";
    }
    
    if (risk_metrics_.daily_pnl < -risk_metrics_.max_daily_loss * balance_.total_balance) {
        risk_breached = true;
        risk_message += "Daily loss limit exceeded; ";
    }
    
    risk_metrics_.risk_limit_breached = risk_breached;
    risk_metrics_.risk_message = risk_message;
}

// Position implementation
Position::Position(AccountId account_id, AssetId asset_id, Quantity quantity, Price average_price)
    : account_id_(std::move(account_id))
    , asset_id_(std::move(asset_id))
    , quantity_(quantity)
    , average_price_(average_price)
    , market_price_(average_price)
    , create_time_(std::chrono::system_clock::now())
    , last_update_time_(create_time_) {
    
    if (quantity_ > 0) {
        long_quantity_ = quantity_;
    } else if (quantity_ < 0) {
        short_quantity_ = -quantity_;
    }
}

Amount Position::unrealized_pnl() const {
    if (quantity_ == 0) return 0.0;
    return quantity_ * (market_price_ - average_price_);
}

double Position::return_rate() const {
    if (average_price_ == 0.0) return 0.0;
    return (market_price_ - average_price_) / average_price_;
}

void Position::add_quantity(Quantity qty, Price price) {
    if (qty == 0) return;
    
    update_average_price(qty, price);
    quantity_ += qty;
    
    if (quantity_ > 0) {
        long_quantity_ = quantity_;
        short_quantity_ = 0;
    } else if (quantity_ < 0) {
        long_quantity_ = 0;
        short_quantity_ = -quantity_;
    } else {
        long_quantity_ = 0;
        short_quantity_ = 0;
    }
    
    update_timestamp();
}

void Position::reduce_quantity(Quantity qty, Price price) {
    if (qty == 0 || quantity_ == 0) return;
    
    // Calculate realized P&L for the closed portion
    Amount pnl = qty * (price - average_price_);
    realized_pnl_ += pnl;
    
    quantity_ -= qty;
    
    if (quantity_ > 0) {
        long_quantity_ = quantity_;
        short_quantity_ = 0;
    } else if (quantity_ < 0) {
        long_quantity_ = 0;
        short_quantity_ = -quantity_;
    } else {
        long_quantity_ = 0;
        short_quantity_ = 0;
    }
    
    update_timestamp();
}

void Position::update_market_price(Price price) {
    market_price_ = price;
    update_timestamp();
}

void Position::close_position(Price price) {
    if (quantity_ != 0) {
        Amount pnl = quantity_ * (price - average_price_);
        realized_pnl_ += pnl;
        quantity_ = 0;
        long_quantity_ = 0;
        short_quantity_ = 0;
        update_timestamp();
    }
}

void Position::update_average_price(Quantity new_qty, Price new_price) {
    if (quantity_ == 0) {
        average_price_ = new_price;
    } else if ((quantity_ > 0 && new_qty > 0) || (quantity_ < 0 && new_qty < 0)) {
        // Same direction - update average price
        Amount total_value = quantity_ * average_price_ + new_qty * new_price;
        Quantity total_qty = quantity_ + new_qty;
        average_price_ = total_value / total_qty;
    }
    // For opposite direction trades, keep the original average price
}

nlohmann::json Position::to_json() const {
    nlohmann::json j;
    j["account_id"] = account_id_;
    j["asset_id"] = asset_id_;
    j["quantity"] = quantity_;
    j["long_quantity"] = long_quantity_;
    j["short_quantity"] = short_quantity_;
    j["average_price"] = average_price_;
    j["market_price"] = market_price_;
    j["realized_pnl"] = realized_pnl_;
    j["unrealized_pnl"] = unrealized_pnl();
    j["total_pnl"] = total_pnl();
    j["return_rate"] = return_rate();
    
    j["create_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        create_time_.time_since_epoch()).count();
    j["last_update_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        last_update_time_.time_since_epoch()).count();
    
    return j;
}

Position Position::from_json(const nlohmann::json& j) {
    Position position(
        j.at("account_id").get<std::string>(),
        j.at("asset_id").get<std::string>(),
        j.value("quantity", 0),
        j.value("average_price", 0.0)
    );
    
    position.long_quantity_ = j.value("long_quantity", 0);
    position.short_quantity_ = j.value("short_quantity", 0);
    position.market_price_ = j.value("market_price", 0.0);
    position.realized_pnl_ = j.value("realized_pnl", 0.0);
    
    if (j.contains("create_time")) {
        auto ms = j["create_time"].get<int64_t>();
        position.create_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    if (j.contains("last_update_time")) {
        auto ms = j["last_update_time"].get<int64_t>();
        position.last_update_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return position;
}

// Trade implementation
Trade::Trade(TradeId trade_id, OrderId order_id, AccountId account_id, AssetId asset_id,
             OrderSide side, Quantity quantity, Price price, Commission commission)
    : trade_id_(std::move(trade_id))
    , order_id_(std::move(order_id))
    , account_id_(std::move(account_id))
    , asset_id_(std::move(asset_id))
    , side_(side)
    , quantity_(quantity)
    , price_(price)
    , commission_(commission)
    , trade_time_(std::chrono::system_clock::now()) {
}

nlohmann::json Trade::to_json() const {
    nlohmann::json j;
    j["trade_id"] = trade_id_;
    j["order_id"] = order_id_;
    j["account_id"] = account_id_;
    j["asset_id"] = asset_id_;
    j["side"] = to_string(side_);
    j["quantity"] = quantity_;
    j["price"] = price_;
    j["commission"] = commission_;
    j["gross_value"] = gross_value();
    j["net_value"] = net_value();
    
    j["trade_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        trade_time_.time_since_epoch()).count();
    
    return j;
}

Trade Trade::from_json(const nlohmann::json& j) {
    Trade trade(
        j.at("trade_id").get<std::string>(),
        j.at("order_id").get<std::string>(),
        j.at("account_id").get<std::string>(),
        j.at("asset_id").get<std::string>(),
        parse_order_side(j.at("side").get<std::string>()),
        j.at("quantity").get<Quantity>(),
        j.at("price").get<Price>(),
        j.value("commission", 0.0)
    );
    
    if (j.contains("trade_time")) {
        auto ms = j["trade_time"].get<int64_t>();
        trade.trade_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return trade;
}

// Utility functions
AccountId generate_account_id() {
    return generate_unique_id("ACC");
}

TradeId generate_trade_id() {
    return generate_unique_id("TRD");
}

bool is_margin_account(const Account& account) {
    return account.account_type() == AccountType::Margin ||
           account.account_type() == AccountType::Future ||
           account.account_type() == AccountType::Option;
}

bool is_cash_account(const Account& account) {
    return account.account_type() == AccountType::Cash;
}

Amount calculate_buying_power(const Account& account) {
    return account.balance().buying_power();
}

Amount calculate_margin_requirement(const Account& account, const std::vector<Position>& positions) {
    Amount total_margin = 0.0;
    
    for (const auto& position : positions) {
        if (position.account_id() == account.account_id()) {
            // Simple margin calculation - in practice this would be more complex
            Amount position_value = std::abs(position.quantity()) * position.market_price();
            total_margin += position_value * 0.1; // 10% margin requirement
        }
    }
    
    return total_margin;
}

} // namespace RoboQuant::Broker
