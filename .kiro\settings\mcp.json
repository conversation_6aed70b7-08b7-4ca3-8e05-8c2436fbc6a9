{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp"]}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking"]}}}