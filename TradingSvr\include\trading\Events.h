/**
 * @file Events.h
 * @brief Modern event system for trading platform
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include <functional>
#include <queue>
#include <thread>
#include <condition_variable>
#include <atomic>
#include <type_traits>
#include <shared_mutex>
#include <mutex>
#include <memory>
#include <vector>
#include <list>
#include <unordered_map>
#include <concurrentqueue.h>

namespace RoboQuant::Trading {

/**
 * @brief Base event class
 */
class Event {
public:
    explicit Event(std::string type, TimePoint timestamp = std::chrono::system_clock::now())
        : type_(std::move(type)), timestamp_(timestamp) {}
    
    virtual ~Event() = default;
    
    [[nodiscard]] const std::string& type() const noexcept { return type_; }
    [[nodiscard]] TimePoint timestamp() const noexcept { return timestamp_; }

private:
    std::string type_;
    TimePoint timestamp_;
};

/**
 * @brief Market data event
 */
class MarketDataEvent : public Event {
public:
    explicit MarketDataEvent(QuoteData data)
        : Event("market_data"), data_(std::move(data)) {}
    
    [[nodiscard]] const QuoteData& data() const noexcept { return data_; }

private:
    QuoteData data_;
};

/**
 * @brief Bar data event
 */
class BarDataEvent : public Event {
public:
    explicit BarDataEvent(BarData data)
        : Event("bar_data"), data_(std::move(data)) {}
    
    [[nodiscard]] const BarData& data() const noexcept { return data_; }

private:
    BarData data_;
};

/**
 * @brief Order fill event
 */
class OrderFillEvent : public Event {
public:
    explicit OrderFillEvent(OrderFill fill)
        : Event("order_fill"), fill_(std::move(fill)) {}
    
    [[nodiscard]] const OrderFill& fill() const noexcept { return fill_; }

private:
    OrderFill fill_;
};

/**
 * @brief Timer event
 */
class TimerEvent : public Event {
public:
    explicit TimerEvent(std::string timer_id)
        : Event("timer"), timer_id_(std::move(timer_id)) {}
    
    [[nodiscard]] const std::string& timer_id() const noexcept { return timer_id_; }

private:
    std::string timer_id_;
};

/**
 * @brief Event handler interface
 */
class EventHandler {
public:
    virtual ~EventHandler() = default;
    virtual void handle_event(const Event& event) = 0;
};

/**
 * @brief Type-safe event handler
 */
template<typename EventType>
class TypedEventHandler : public EventHandler {
public:
    using HandlerFunction = std::function<void(const EventType&)>;
    
    explicit TypedEventHandler(HandlerFunction handler)
        : handler_(std::move(handler)) {}
    
    void handle_event(const Event& event) override {
        if (const auto* typed_event = dynamic_cast<const EventType*>(&event)) {
            handler_(*typed_event);
        }
    }

private:
    HandlerFunction handler_;
};

/**
 * @brief Event bus for publish-subscribe pattern
 */
class EventBus {
public:
    EventBus();
    ~EventBus();

    // Non-copyable, movable
    EventBus(const EventBus&) = delete;
    EventBus& operator=(const EventBus&) = delete;
    EventBus(EventBus&&) = default;
    EventBus& operator=(EventBus&&) = default;

    // Subscription management
    template<typename EventType>
    void subscribe(std::function<void(const EventType&)> handler) {
        auto typed_handler = std::make_unique<TypedEventHandler<EventType>>(std::move(handler));
        subscribe_impl(EventType::event_type(), std::move(typed_handler));
    }
    
    void subscribe(const std::string& event_type, UniquePtr<EventHandler> handler);
    void unsubscribe(const std::string& event_type);
    
    // Event publishing
    void publish(UniquePtr<Event> event);
    
    template<typename EventType, typename... Args>
    void publish(Args&&... args) {
        auto event = std::make_unique<EventType>(std::forward<Args>(args)...);
        publish(std::move(event));
    }
    
    // Control
    void start();
    void stop();
    [[nodiscard]] bool is_running() const noexcept;

private:
    void subscribe_impl(const std::string& event_type, UniquePtr<EventHandler> handler);
    void process_events();
    
    std::atomic<bool> running_{false};
    std::thread worker_thread_;
    
    // ʹ���������������¼���������
    moodycamel::ConcurrentQueue<UniquePtr<Event>> event_queue_;
    std::atomic<bool> has_events_{false};
    
    mutable std::shared_mutex handlers_mutex_;
    std::unordered_map<std::string, std::vector<UniquePtr<EventHandler>>> handlers_;
};

/**
 * @brief Event dispatcher for immediate synchronous processing
 */
class EventDispatcher {
public:
    // Subscription management
    template<typename EventType>
    void subscribe(std::function<void(const EventType&)> handler) {
        auto typed_handler = std::make_unique<TypedEventHandler<EventType>>(std::move(handler));
        subscribe_impl(EventType::event_type(), std::move(typed_handler));
    }
    
    void subscribe(const std::string& event_type, UniquePtr<EventHandler> handler);
    void unsubscribe(const std::string& event_type);
    
    // Immediate event dispatch
    void dispatch(const Event& event);
    
    template<typename EventType, typename... Args>
    void dispatch(Args&&... args) {
        EventType event(std::forward<Args>(args)...);
        dispatch(event);
    }

private:
    void subscribe_impl(const std::string& event_type, UniquePtr<EventHandler> handler);
    
    mutable std::shared_mutex handlers_mutex_;
    std::unordered_map<std::string, std::vector<UniquePtr<EventHandler>>> handlers_;
};

/**
 * @brief Event aggregator for batching events
 */
class EventAggregator {
public:
    explicit EventAggregator(Duration batch_interval = std::chrono::milliseconds(100));
    ~EventAggregator();

    // Non-copyable, movable
    EventAggregator(const EventAggregator&) = delete;
    EventAggregator& operator=(const EventAggregator&) = delete;
    EventAggregator(EventAggregator&&) = default;
    EventAggregator& operator=(EventAggregator&&) = default;

    // Event collection
    void add_event(UniquePtr<Event> event);
    
    template<typename EventType, typename... Args>
    void add_event(Args&&... args) {
        auto event = std::make_unique<EventType>(std::forward<Args>(args)...);
        add_event(std::move(event));
    }
    
    // Batch processing
    using BatchProcessor = std::function<void(std::vector<UniquePtr<Event>>)>;
    void set_batch_processor(BatchProcessor processor);
    
    // Control
    void start();
    void stop();
    void flush(); // Process current batch immediately

private:
    void process_batches();
    
    Duration batch_interval_;
    BatchProcessor batch_processor_;
    
    std::atomic<bool> running_{false};
    std::thread worker_thread_;
    
    mutable std::mutex batch_mutex_;
    std::vector<UniquePtr<Event>> current_batch_;
    std::condition_variable batch_condition_;
};

} // namespace RoboQuant::Trading
