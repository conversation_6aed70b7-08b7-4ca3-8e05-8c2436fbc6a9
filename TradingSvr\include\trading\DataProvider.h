/**
 * @file DataProvider.h
 * @brief Modern data provider interface and implementations
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include <future>
#include <functional>
#include <shared_mutex>
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <optional>

namespace RoboQuant::Trading {

/**
 * @brief Data subscription interface
 */
class DataSubscription {
public:
    virtual ~DataSubscription() = default;
    virtual void cancel() = 0;
    virtual bool is_active() const = 0;
    virtual std::string get_subscription_id() const = 0;
};

/**
 * @brief Abstract data provider interface
 */
class DataProvider {
public:
    virtual ~DataProvider() = default;

    // Market data
    virtual std::future<std::optional<QuoteData>> get_quote(const AssetId& asset_id) = 0;
    virtual std::future<std::vector<BarData>> get_bars(
        const AssetId& asset_id,
        TimePoint start_time,
        TimePoint end_time,
        Duration bar_size = std::chrono::minutes(1)
    ) = 0;

    // Real-time subscriptions
    using QuoteCallback = std::function<void(const QuoteData&)>;
    using BarCallback = std::function<void(const BarData&)>;
    
    virtual std::unique_ptr<DataSubscription> subscribe_quotes(
        const std::vector<AssetId>& asset_ids,
        QuoteCallback callback
    ) = 0;
    
    virtual std::unique_ptr<DataSubscription> subscribe_bars(
        const std::vector<AssetId>& asset_ids,
        Duration bar_size,
        BarCallback callback
    ) = 0;

    // Asset information
    virtual std::future<std::vector<AssetId>> get_available_assets() = 0;
    virtual std::future<bool> is_asset_available(const AssetId& asset_id) = 0;
    
    // Connection management
    virtual std::future<bool> connect() = 0;
    virtual std::future<void> disconnect() = 0;
    virtual bool is_connected() const = 0;

    // Status and health
    virtual std::string get_provider_name() const = 0;
    virtual std::unordered_map<std::string, std::string> get_status() const = 0;
};

/**
 * @brief Market cross-section data for factor analysis
 */
struct MarketCrossSectionData {
    TimePoint timestamp;
    std::unordered_map<AssetId, double> factor_values;
    std::string factor_name;
    
    [[nodiscard]] std::optional<double> get_factor_value(const AssetId& asset_id) const {
        auto it = factor_values.find(asset_id);
        return it != factor_values.end() ? std::make_optional(it->second) : std::nullopt;
    }
};

/**
 * @brief Factor data provider interface
 */
class FactorDataProvider {
public:
    virtual ~FactorDataProvider() = default;

    // Factor data retrieval
    virtual std::future<std::optional<MarketCrossSectionData>> get_factor_data(
        const std::string& factor_name,
        TimePoint timestamp
    ) = 0;
    
    virtual std::future<std::vector<MarketCrossSectionData>> get_factor_history(
        const std::string& factor_name,
        TimePoint start_time,
        TimePoint end_time
    ) = 0;

    // Available factors
    virtual std::future<std::vector<std::string>> get_available_factors() = 0;
    virtual std::future<bool> is_factor_available(const std::string& factor_name) = 0;

    // Real-time factor updates
    using FactorCallback = std::function<void(const MarketCrossSectionData&)>;
    virtual std::unique_ptr<DataSubscription> subscribe_factor(
        const std::string& factor_name,
        FactorCallback callback
    ) = 0;
};

/**
 * @brief Fundamental data structure
 */
struct FundamentalData {
    AssetId asset_id;
    TimePoint timestamp;
    
    // Financial metrics
    std::optional<double> market_cap;
    std::optional<double> pe_ratio;
    std::optional<double> pb_ratio;
    std::optional<double> dividend_yield;
    std::optional<double> roe;
    std::optional<double> roa;
    std::optional<double> debt_to_equity;
    
    // Classification
    std::optional<std::string> sector;
    std::optional<std::string> industry;
    std::optional<std::string> country;
    
    // Corporate events
    std::optional<TimePoint> earnings_date;
    std::optional<TimePoint> ex_dividend_date;
    std::optional<double> dividend_amount;
    
    template<typename T>
    [[nodiscard]] std::optional<T> get_field(const std::string& field_name) const;
};

/**
 * @brief Fundamental data provider interface
 */
class FundamentalDataProvider {
public:
    virtual ~FundamentalDataProvider() = default;

    // Fundamental data retrieval
    virtual std::future<std::optional<FundamentalData>> get_fundamental_data(
        const AssetId& asset_id,
        std::optional<TimePoint> as_of_date = std::nullopt
    ) = 0;
    
    virtual std::future<std::vector<FundamentalData>> get_fundamental_history(
        const AssetId& asset_id,
        TimePoint start_time,
        TimePoint end_time
    ) = 0;

    // Screening
    virtual std::future<std::vector<AssetId>> screen_assets(
        const std::unordered_map<std::string, std::variant<double, std::string>>& criteria
    ) = 0;

    // Corporate events
    virtual std::future<std::vector<FundamentalData>> get_upcoming_earnings(
        std::optional<TimePoint> start_date = std::nullopt,
        std::optional<TimePoint> end_date = std::nullopt
    ) = 0;
    
    virtual std::future<std::vector<FundamentalData>> get_upcoming_dividends(
        std::optional<TimePoint> start_date = std::nullopt,
        std::optional<TimePoint> end_date = std::nullopt
    ) = 0;
};

/**
 * @brief Composite data provider that aggregates multiple providers
 */
class CompositeDataProvider : public DataProvider, public FactorDataProvider, public FundamentalDataProvider {
public:
    CompositeDataProvider() = default;
    ~CompositeDataProvider() override = default;

    // Provider management
    void add_market_data_provider(std::unique_ptr<DataProvider> provider);
    void add_factor_data_provider(std::unique_ptr<FactorDataProvider> provider);
    void add_fundamental_data_provider(std::unique_ptr<FundamentalDataProvider> provider);

    // DataProvider interface
    std::future<std::optional<QuoteData>> get_quote(const AssetId& asset_id) override;
    std::future<std::vector<BarData>> get_bars(
        const AssetId& asset_id,
        TimePoint start_time,
        TimePoint end_time,
        Duration bar_size = std::chrono::minutes(1)
    ) override;

    std::unique_ptr<DataSubscription> subscribe_quotes(
        const std::vector<AssetId>& asset_ids,
        QuoteCallback callback
    ) override;
    
    std::unique_ptr<DataSubscription> subscribe_bars(
        const std::vector<AssetId>& asset_ids,
        Duration bar_size,
        BarCallback callback
    ) override;

    std::future<std::vector<AssetId>> get_available_assets() override;
    std::future<bool> is_asset_available(const AssetId& asset_id) override;
    
    std::future<bool> connect() override;
    std::future<void> disconnect() override;
    bool is_connected() const override;

    std::string get_provider_name() const override { return "CompositeDataProvider"; }
    std::unordered_map<std::string, std::string> get_status() const override;

    // FactorDataProvider interface
    std::future<std::optional<MarketCrossSectionData>> get_factor_data(
        const std::string& factor_name,
        TimePoint timestamp
    ) override;
    
    std::future<std::vector<MarketCrossSectionData>> get_factor_history(
        const std::string& factor_name,
        TimePoint start_time,
        TimePoint end_time
    ) override;

    std::future<std::vector<std::string>> get_available_factors() override;
    std::future<bool> is_factor_available(const std::string& factor_name) override;

    std::unique_ptr<DataSubscription> subscribe_factor(
        const std::string& factor_name,
        FactorCallback callback
    ) override;

    // FundamentalDataProvider interface
    std::future<std::optional<FundamentalData>> get_fundamental_data(
        const AssetId& asset_id,
        std::optional<TimePoint> as_of_date = std::nullopt
    ) override;
    
    std::future<std::vector<FundamentalData>> get_fundamental_history(
        const AssetId& asset_id,
        TimePoint start_time,
        TimePoint end_time
    ) override;

    std::future<std::vector<AssetId>> screen_assets(
        const std::unordered_map<std::string, std::variant<double, std::string>>& criteria
    ) override;

    std::future<std::vector<FundamentalData>> get_upcoming_earnings(
        std::optional<TimePoint> start_date = std::nullopt,
        std::optional<TimePoint> end_date = std::nullopt
    ) override;
    
    std::future<std::vector<FundamentalData>> get_upcoming_dividends(
        std::optional<TimePoint> start_date = std::nullopt,
        std::optional<TimePoint> end_date = std::nullopt
    ) override;

private:
    std::vector<std::unique_ptr<DataProvider>> market_data_providers_;
    std::vector<std::unique_ptr<FactorDataProvider>> factor_data_providers_;
    std::vector<std::unique_ptr<FundamentalDataProvider>> fundamental_data_providers_;
    
    mutable std::shared_mutex providers_mutex_;
};

} // namespace RoboQuant::Trading
