name: <PERSON><PERSON> build

on: [push, pull_request]

jobs:
  build_and_test_ubuntu:
    name: Linux Ubuntu 22.04 Bazel build <GCC 11.2.0>
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        compilation_mode: [fastbuild, dbg, opt]

    steps:
    - uses: actions/checkout@v3

    - name: Mount bazel cache
      uses: actions/cache@v3
      with:
        path: "/home/<USER>/.cache/bazel"
        key: bazel-ubuntu22-gcc11

    - name: Build Catch2
      run: |
        bazelisk build --compilation_mode=${{matrix.compilation_mode}} //...
