/**
 * @file NetworkManager.h
 * @brief Modern network communication system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include "Events.h"
#include <boost/asio.hpp>
#include <boost/beast.hpp>
#include <future>
#include <queue>
#include <shared_mutex>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <string>
#include <vector>
#include <regex>

namespace RoboQuant::Trading {

namespace asio = boost::asio;
namespace beast = boost::beast;
namespace http = beast::http;
namespace websocket = beast::websocket;

/**
 * @brief Network message structure
 */
struct NetworkMessage {
    std::string id;
    std::string type;
    std::string payload;
    TimePoint timestamp;
    std::unordered_map<std::string, std::string> headers;
    
    // Serialization
    [[nodiscard]] std::string to_json() const;
    static std::optional<NetworkMessage> from_json(const std::string& json);
    
    // Convenience constructors
    static NetworkMessage create_request(const std::string& type, const std::string& payload);
    static NetworkMessage create_response(const std::string& request_id, const std::string& payload);
    static NetworkMessage create_notification(const std::string& type, const std::string& payload);
};

/**
 * @brief Network connection interface
 */
class NetworkConnection {
public:
    virtual ~NetworkConnection() = default;

    // Connection management
    virtual std::future<bool> connect(const std::string& endpoint) = 0;
    virtual std::future<void> disconnect() = 0;
    virtual bool is_connected() const = 0;

    // Message handling
    virtual std::future<bool> send_message(const NetworkMessage& message) = 0;
    virtual std::future<std::optional<NetworkMessage>> receive_message() = 0;

    // Callbacks
    using MessageCallback = std::function<void(const NetworkMessage&)>;
    using ConnectionCallback = std::function<void(bool connected)>;
    using ErrorCallback = std::function<void(const std::string& error)>;

    virtual void set_message_callback(MessageCallback callback) = 0;
    virtual void set_connection_callback(ConnectionCallback callback) = 0;
    virtual void set_error_callback(ErrorCallback callback) = 0;

    // Connection info
    virtual std::string get_endpoint() const = 0;
    virtual std::string get_connection_id() const = 0;
};

/**
 * @brief WebSocket connection implementation
 */
class WebSocketConnection : public NetworkConnection {
public:
    explicit WebSocketConnection(asio::io_context& io_context);
    ~WebSocketConnection() override;

    // Non-copyable, movable
    WebSocketConnection(const WebSocketConnection&) = delete;
    WebSocketConnection& operator=(const WebSocketConnection&) = delete;
    WebSocketConnection(WebSocketConnection&&) = default;
    WebSocketConnection& operator=(WebSocketConnection&&) = default;

    // NetworkConnection interface
    std::future<bool> connect(const std::string& endpoint) override;
    std::future<void> disconnect() override;
    bool is_connected() const override;

    std::future<bool> send_message(const NetworkMessage& message) override;
    std::future<std::optional<NetworkMessage>> receive_message() override;

    void set_message_callback(MessageCallback callback) override;
    void set_connection_callback(ConnectionCallback callback) override;
    void set_error_callback(ErrorCallback callback) override;

    std::string get_endpoint() const override;
    std::string get_connection_id() const override;

    // WebSocket specific
    void set_ping_interval(Duration interval);
    void set_reconnect_interval(Duration interval);
    void enable_auto_reconnect(bool enable);

private:
    void start_read();
    void start_ping_timer();
    void handle_reconnect();

    asio::io_context& io_context_;
    websocket::stream<beast::tcp_stream> ws_;
    asio::steady_timer ping_timer_;
    asio::steady_timer reconnect_timer_;

    std::string endpoint_;
    std::string connection_id_;
    std::atomic<bool> connected_{false};
    std::atomic<bool> auto_reconnect_{true};

    Duration ping_interval_{std::chrono::seconds(30)};
    Duration reconnect_interval_{std::chrono::seconds(5)};

    MessageCallback message_callback_;
    ConnectionCallback connection_callback_;
    ErrorCallback error_callback_;

    mutable std::mutex callbacks_mutex_;
    std::queue<NetworkMessage> send_queue_;
    std::mutex send_queue_mutex_;
    std::condition_variable send_condition_;
};

/**
 * @brief HTTP client for REST API communication
 */
class HttpClient {
public:
    explicit HttpClient(asio::io_context& io_context);
    ~HttpClient() = default;

    // Non-copyable, movable
    HttpClient(const HttpClient&) = delete;
    HttpClient& operator=(const HttpClient&) = delete;
    HttpClient(HttpClient&&) = default;
    HttpClient& operator=(HttpClient&&) = default;

    // HTTP methods
    std::future<http::response<http::string_body>> get(
        const std::string& url,
        const std::unordered_map<std::string, std::string>& headers = {}
    );

    std::future<http::response<http::string_body>> post(
        const std::string& url,
        const std::string& body,
        const std::unordered_map<std::string, std::string>& headers = {}
    );

    std::future<http::response<http::string_body>> put(
        const std::string& url,
        const std::string& body,
        const std::unordered_map<std::string, std::string>& headers = {}
    );

    std::future<http::response<http::string_body>> delete_request(
        const std::string& url,
        const std::unordered_map<std::string, std::string>& headers = {}
    );

    // Configuration
    void set_timeout(Duration timeout);
    void set_user_agent(const std::string& user_agent);
    void set_default_headers(const std::unordered_map<std::string, std::string>& headers);

private:
    std::future<http::response<http::string_body>> make_request(
        http::verb method,
        const std::string& url,
        const std::string& body = "",
        const std::unordered_map<std::string, std::string>& headers = {}
    );

    asio::io_context& io_context_;
    Duration timeout_{std::chrono::seconds(30)};
    std::string user_agent_{"RoboQuant/1.0"};
    std::unordered_map<std::string, std::string> default_headers_;
};

/**
 * @brief Network server for handling incoming connections
 */
class NetworkServer {
public:
    explicit NetworkServer(asio::io_context& io_context, uint16_t port);
    ~NetworkServer();

    // Non-copyable, movable
    NetworkServer(const NetworkServer&) = delete;
    NetworkServer& operator=(const NetworkServer&) = delete;
    NetworkServer(NetworkServer&&) = default;
    NetworkServer& operator=(NetworkServer&&) = default;

    // Server control
    std::future<bool> start();
    std::future<void> stop();
    bool is_running() const;

    // Client management
    using ClientConnectedCallback = std::function<void(const std::string& client_id)>;
    using ClientDisconnectedCallback = std::function<void(const std::string& client_id)>;
    using ClientMessageCallback = std::function<void(const std::string& client_id, const NetworkMessage& message)>;

    void set_client_connected_callback(ClientConnectedCallback callback);
    void set_client_disconnected_callback(ClientDisconnectedCallback callback);
    void set_client_message_callback(ClientMessageCallback callback);

    // Message broadcasting
    std::future<bool> send_to_client(const std::string& client_id, const NetworkMessage& message);
    std::future<void> broadcast_message(const NetworkMessage& message);
    std::future<void> broadcast_to_group(const std::string& group, const NetworkMessage& message);

    // Client management
    void add_client_to_group(const std::string& client_id, const std::string& group);
    void remove_client_from_group(const std::string& client_id, const std::string& group);
    [[nodiscard]] std::vector<std::string> get_connected_clients() const;
    [[nodiscard]] std::vector<std::string> get_clients_in_group(const std::string& group) const;

private:
    void accept_connections();
    void handle_client_connection(std::shared_ptr<NetworkConnection> connection);

    asio::io_context& io_context_;
    asio::ip::tcp::acceptor acceptor_;
    uint16_t port_;
    std::atomic<bool> running_{false};

    // Client management
    mutable std::shared_mutex clients_mutex_;
    std::unordered_map<std::string, std::shared_ptr<NetworkConnection>> clients_;
    std::unordered_map<std::string, std::unordered_set<std::string>> client_groups_;

    // Callbacks
    std::mutex callbacks_mutex_;
    ClientConnectedCallback client_connected_callback_;
    ClientDisconnectedCallback client_disconnected_callback_;
    ClientMessageCallback client_message_callback_;
};

/**
 * @brief Cloud client for external service communication
 */
class CloudClient {
public:
    explicit CloudClient(asio::io_context& io_context);
    ~CloudClient() = default;

    // Non-copyable, movable
    CloudClient(const CloudClient&) = delete;
    CloudClient& operator=(const CloudClient&) = delete;
    CloudClient(CloudClient&&) = default;
    CloudClient& operator=(CloudClient&&) = default;

    // Connection management
    std::future<bool> connect_to_service(const std::string& service_name, const std::string& endpoint);
    std::future<void> disconnect_from_service(const std::string& service_name);
    bool is_connected_to_service(const std::string& service_name) const;

    // Service communication
    std::future<std::optional<NetworkMessage>> send_request(
        const std::string& service_name,
        const NetworkMessage& request,
        Duration timeout = std::chrono::seconds(30)
    );

    std::future<bool> send_notification(const std::string& service_name, const NetworkMessage& notification);

    // Service discovery
    std::future<std::vector<std::string>> discover_services();
    std::future<std::optional<std::string>> get_service_endpoint(const std::string& service_name);

    // Event callbacks
    using ServiceEventCallback = std::function<void(const std::string& service_name, const NetworkMessage& message)>;
    void set_service_event_callback(ServiceEventCallback callback);

    // Health monitoring
    [[nodiscard]] std::unordered_map<std::string, bool> get_service_health() const;
    void enable_health_monitoring(bool enable, Duration check_interval = std::chrono::minutes(1));

private:
    void monitor_service_health();

    asio::io_context& io_context_;
    HttpClient http_client_;

    mutable std::shared_mutex connections_mutex_;
    std::unordered_map<std::string, std::unique_ptr<NetworkConnection>> connections_;
    std::unordered_map<std::string, std::string> service_endpoints_;

    // Health monitoring
    std::atomic<bool> health_monitoring_enabled_{false};
    Duration health_check_interval_{std::chrono::minutes(1)};
    asio::steady_timer health_timer_;
    std::unordered_map<std::string, bool> service_health_;

    // Callbacks
    std::mutex callback_mutex_;
    ServiceEventCallback service_event_callback_;
};

/**
 * @brief Network manager for centralized network operations
 */
class NetworkManager {
public:
    NetworkManager();
    ~NetworkManager();

    // Non-copyable, movable
    NetworkManager(const NetworkManager&) = delete;
    NetworkManager& operator=(const NetworkManager&) = delete;
    NetworkManager(NetworkManager&&) = default;
    NetworkManager& operator=(NetworkManager&&) = default;

    // Lifecycle
    void start();
    void stop();
    bool is_running() const;

    // Component access
    [[nodiscard]] HttpClient& get_http_client() { return *http_client_; }
    [[nodiscard]] CloudClient& get_cloud_client() { return *cloud_client_; }
    [[nodiscard]] NetworkServer& get_server() { return *server_; }

    // Factory methods
    std::unique_ptr<WebSocketConnection> create_websocket_connection();

    // Configuration
    void set_server_port(uint16_t port);
    void set_worker_threads(size_t thread_count);

private:
    void run_io_context();

    asio::io_context io_context_;
    std::unique_ptr<asio::io_context::work> work_guard_;
    std::vector<std::thread> worker_threads_;

    std::unique_ptr<HttpClient> http_client_;
    std::unique_ptr<CloudClient> cloud_client_;
    std::unique_ptr<NetworkServer> server_;

    std::atomic<bool> running_{false};
    uint16_t server_port_{8080};
    size_t worker_thread_count_{4};
};

} // namespace RoboQuant::Trading
