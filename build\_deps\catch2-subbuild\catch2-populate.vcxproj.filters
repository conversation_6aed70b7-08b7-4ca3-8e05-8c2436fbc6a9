﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\8371a1a20ab1ba87700aca6b462e6bdd\catch2-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\1e2b0374f93c210af9a119aa0fcc3ea5\catch2-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\fd22776dd8cc05e0db87e3e3f7b036aa\catch2-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\lab\RoboQuant\Experiments\build\_deps\catch2-subbuild\CMakeFiles\catch2-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{FA8B1303-5A25-3C27-AF82-671FF96BAFFB}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
