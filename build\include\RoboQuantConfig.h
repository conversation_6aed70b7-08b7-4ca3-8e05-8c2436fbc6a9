#pragma once

/**
 * @file RoboQuantConfig.h
 * @brief RoboQuant系统配置头文件
 * 
 * 此文件由CMake自动生成，包含编译时配置信息
 */

// ============================================================================
// 版本信息
// ============================================================================

#define ROBOQUANT_VERSION_MAJOR 1
#define ROBOQUANT_VERSION_MINOR 0
#define ROBOQUANT_VERSION_PATCH 0
#define ROBOQUANT_VERSION_STRING "1.0.0"

// ============================================================================
// 平台检测
// ============================================================================

#define ROBOQUANT_PLATFORM_WINDOWS
/* #undef ROBOQUANT_PLATFORM_LINUX */
/* #undef ROBOQUANT_PLATFORM_MACOS */

// ============================================================================
// 编译器检测
// ============================================================================

#define ROBOQUANT_COMPILER_MSVC
/* #undef ROBOQUANT_COMPILER_GCC */
/* #undef ROBOQUANT_COMPILER_CLANG */

// ============================================================================
// 功能特性
// ============================================================================

#define ROBOQUANT_HAS_BOOST
#define ROBOQUANT_HAS_CURL
#define ROBOQUANT_HAS_SQLITE3
/* #undef ROBOQUANT_HAS_LIBTORCH */
/* #undef ROBOQUANT_HAS_TALIB */

// ============================================================================
// 构建选项
// ============================================================================

#define ROBOQUANT_BUILD_TESTS
#define ROBOQUANT_BUILD_EXAMPLES
/* #undef ROBOQUANT_BUILD_BENCHMARKS */
/* #undef ROBOQUANT_BUILD_DOCS */
/* #undef ROBOQUANT_STATIC_LINKING */

// ============================================================================
// 模块配置
// ============================================================================

#define ROBOQUANT_BUILD_DATAHUB
#define ROBOQUANT_BUILD_TRADINGSVR
#define ROBOQUANT_BUILD_BROKER
#define ROBOQUANT_BUILD_QUANTSERVICES
#define ROBOQUANT_BUILD_SPECTRE
#define ROBOQUANT_BUILD_INTEGRATION

// ============================================================================
// 平台特定配置
// ============================================================================

#ifdef ROBOQUANT_PLATFORM_WINDOWS
    #define ROBOQUANT_EXPORT __declspec(dllexport)
    #define ROBOQUANT_IMPORT __declspec(dllimport)
    #define ROBOQUANT_CALL __stdcall
    #define ROBOQUANT_INLINE __forceinline
#else
    #define ROBOQUANT_EXPORT __attribute__((visibility("default")))
    #define ROBOQUANT_IMPORT
    #define ROBOQUANT_CALL
    #define ROBOQUANT_INLINE inline __attribute__((always_inline))
#endif

// ============================================================================
// API宏定义
// ============================================================================

#ifdef ROBOQUANT_STATIC_LINKING
    #define ROBOQUANT_API
#else
    #ifdef ROBOQUANT_BUILDING_DLL
        #define ROBOQUANT_API ROBOQUANT_EXPORT
    #else
        #define ROBOQUANT_API ROBOQUANT_IMPORT
    #endif
#endif

// ============================================================================
// 调试和日志配置
// ============================================================================

#ifdef DEBUG
    #define ROBOQUANT_DEBUG 1
    #define ROBOQUANT_LOG_LEVEL_DEFAULT 0  // TRACE
#else
    #define ROBOQUANT_DEBUG 0
    #define ROBOQUANT_LOG_LEVEL_DEFAULT 2  // INFO
#endif

// ============================================================================
// 性能配置
// ============================================================================

// SIMD支持
#if defined(__AVX2__)
    #define ROBOQUANT_HAS_AVX2 1
#endif

#if defined(__AVX__)
    #define ROBOQUANT_HAS_AVX 1
#endif

#if defined(__SSE4_2__)
    #define ROBOQUANT_HAS_SSE42 1
#endif

// 并行处理
#ifdef _OPENMP
    #define ROBOQUANT_HAS_OPENMP 1
#endif

// ============================================================================
// 编译器特定优化
// ============================================================================

#ifdef ROBOQUANT_COMPILER_MSVC
    #define ROBOQUANT_LIKELY(x) (x)
    #define ROBOQUANT_UNLIKELY(x) (x)
    #define ROBOQUANT_RESTRICT __restrict
    #define ROBOQUANT_NOINLINE __declspec(noinline)
#elif defined(ROBOQUANT_COMPILER_GCC) || defined(ROBOQUANT_COMPILER_CLANG)
    #define ROBOQUANT_LIKELY(x) __builtin_expect(!!(x), 1)
    #define ROBOQUANT_UNLIKELY(x) __builtin_expect(!!(x), 0)
    #define ROBOQUANT_RESTRICT __restrict__
    #define ROBOQUANT_NOINLINE __attribute__((noinline))
#else
    #define ROBOQUANT_LIKELY(x) (x)
    #define ROBOQUANT_UNLIKELY(x) (x)
    #define ROBOQUANT_RESTRICT
    #define ROBOQUANT_NOINLINE
#endif

// ============================================================================
// 内存对齐
// ============================================================================

#ifdef ROBOQUANT_COMPILER_MSVC
    #define ROBOQUANT_ALIGN(n) __declspec(align(n))
#else
    #define ROBOQUANT_ALIGN(n) __attribute__((aligned(n)))
#endif

// 缓存行大小
#define ROBOQUANT_CACHE_LINE_SIZE 64

// ============================================================================
// 断言和错误处理
// ============================================================================

#if ROBOQUANT_DEBUG
    #include <cassert>
    #define ROBOQUANT_ASSERT(x) assert(x)
    #define ROBOQUANT_DEBUG_ONLY(x) x
#else
    #define ROBOQUANT_ASSERT(x) ((void)0)
    #define ROBOQUANT_DEBUG_ONLY(x) ((void)0)
#endif

// ============================================================================
// 字符串化宏
// ============================================================================

#define ROBOQUANT_STRINGIFY(x) #x
#define ROBOQUANT_TOSTRING(x) ROBOQUANT_STRINGIFY(x)

// ============================================================================
// 版本检查宏
// ============================================================================

#define ROBOQUANT_VERSION_CHECK(major, minor, patch) \
    ((ROBOQUANT_VERSION_MAJOR > (major)) || \
     (ROBOQUANT_VERSION_MAJOR == (major) && ROBOQUANT_VERSION_MINOR > (minor)) || \
     (ROBOQUANT_VERSION_MAJOR == (major) && ROBOQUANT_VERSION_MINOR == (minor) && ROBOQUANT_VERSION_PATCH >= (patch)))

// ============================================================================
// 命名空间配置
// ============================================================================

#define ROBOQUANT_NAMESPACE_BEGIN namespace roboquant {
#define ROBOQUANT_NAMESPACE_END }

// ============================================================================
// 构建信息
// ============================================================================

#define ROBOQUANT_BUILD_DATE __DATE__
#define ROBOQUANT_BUILD_TIME __TIME__
#define ROBOQUANT_BUILD_TYPE "Release"

// ============================================================================
// 特性测试宏
// ============================================================================

// C++20特性
#if __cplusplus >= 202002L
    #define ROBOQUANT_HAS_CPP20 1
    #define ROBOQUANT_HAS_CONCEPTS 1
    #define ROBOQUANT_HAS_COROUTINES 1
    #define ROBOQUANT_HAS_MODULES 1
    #define ROBOQUANT_HAS_RANGES 1
#endif

// 线程支持
#ifdef _REENTRANT
    #define ROBOQUANT_HAS_THREADS 1
#endif

// ============================================================================
// 兼容性宏
// ============================================================================

// 废弃警告
#ifdef ROBOQUANT_COMPILER_MSVC
    #define ROBOQUANT_DEPRECATED(msg) __declspec(deprecated(msg))
#else
    #define ROBOQUANT_DEPRECATED(msg) __attribute__((deprecated(msg)))
#endif

// 未使用变量
#ifdef ROBOQUANT_COMPILER_MSVC
    #define ROBOQUANT_UNUSED(x) (void)(x)
#else
    #define ROBOQUANT_UNUSED(x) __attribute__((unused)) x
#endif

// ============================================================================
// 配置验证
// ============================================================================

// 确保至少有一个模块被启用
#if !defined(ROBOQUANT_BUILD_DATAHUB) && \
    !defined(ROBOQUANT_BUILD_TRADINGSVR) && \
    !defined(ROBOQUANT_BUILD_BROKER) && \
    !defined(ROBOQUANT_BUILD_QUANTSERVICES) && \
    !defined(ROBOQUANT_BUILD_SPECTRE) && \
    !defined(ROBOQUANT_BUILD_INTEGRATION)
    #error "At least one RoboQuant module must be enabled"
#endif

// 确保平台被正确检测
#if !defined(ROBOQUANT_PLATFORM_WINDOWS) && \
    !defined(ROBOQUANT_PLATFORM_LINUX) && \
    !defined(ROBOQUANT_PLATFORM_MACOS)
    #error "Platform not detected or unsupported"
#endif
