#pragma once

#include <vector>
#include <cmath>
#include <immintrin.h> // Intel Intrinsics for AVX

// 简单的CPU特性检测
struct CPUFeatures {
    bool avx2 = false;
    CPUFeatures() {
        int cpuInfo[4];
        __cpuidex(cpuInfo, 7, 0);
        avx2 = (cpuInfo[1] & (1 << 5));
    }
};
static const CPUFeatures cpu_features;

class RollingWindowSIMD {
    // ... (成员变量和构造函数之前相同) ...

public:
    RollingWindowSIMD(size_t window_size = 0) : 
        m_window_size(window_size), m_count(0), m_mean(0.0), m_s(0.0), m_head(0) {
        if (m_window_size > 0) {
            // 确保内存对齐以适应SIMD操作，32字节对齐AVX (256-bit)
            m_buffer.resize(m_window_size);
        }
    }

    void push(double value) {
        // push 逻辑与之前相同，因为增量算法本身是O(1)，SIMD优化不在此处
        // SIMD主要用于批量计算，而不是每次push时都进行
        if (m_window_size == 0) return;

        double old_value = 0.0;
        if (m_count == m_window_size) {
            old_value = m_buffer[m_head];
        }

        m_buffer[m_head] = value;
        m_head = (m_head + 1) % m_window_size;

        double old_mean = m_mean;
        if (m_count < m_window_size) {
            m_count++;
            m_mean = old_mean + (value - old_mean) / m_count;
            m_s += (value - old_mean) * (value - m_mean);
        } else {
            m_mean = old_mean + (value - old_value) / m_window_size;
            m_s += (value - old_value) * (value + old_value - m_mean - old_mean);
        }
    }
  
    double getMean() const { return m_mean; }
  
    double getVariance() const {
        return (m_count > 1) ? m_s / (m_count - 1) : 0.0;
    }
  
    double getStdDev() const {
        return std::sqrt(getVariance());
    }

    double standardize(double value) const {
        double std_dev = getStdDev();
        if (std::abs(std_dev) < 1e-9) { // Avoid division by zero
            return 0.0;
        }
        return (value - m_mean) / std_dev;
    }
  
    size_t getCount() const { return m_count; }

    // SIMD批量优化是一个独立的功能，用于一次性计算大量历史数据的统计量
    // 在系统初始化或加载历史数据时非常有用
    static std::pair<double, double> batchCalculateStats(const std::vector<double>& data) {
        if (data.empty()) return {0.0, 0.0};

        if (cpu_features.avx2 && data.size() >= 4) {
            return batchCalculateStatsAVX2(data);
        } else {
            return batchCalculateStatsScalar(data);
        }
    }

private:
    size_t m_window_size;
    std::vector<double> m_buffer; // Ring buffer
    size_t m_count; // Number of elements currently in buffer
  
    // For Welford's algorithm
    double m_mean;
    double m_s; // Sum of squares of differences from the current mean

    size_t m_head; // Pointer to the next insertion point

    static std::pair<double, double> batchCalculateStatsAVX2(const std::vector<double>& data) {
        size_t n = data.size();
        __m256d sum_vec = _mm256_setzero_pd();
      
        size_t i = 0;
        for (; i <= n - 4; i += 4) {
            __m256d v = _mm256_loadu_pd(&data[i]);
            sum_vec = _mm256_add_pd(sum_vec, v);
        }

        double temp_sum[4];
        _mm256_storeu_pd(temp_sum, sum_vec);
        double total_sum = temp_sum[0] + temp_sum[1] + temp_sum[2] + temp_sum[3];

        for (; i < n; ++i) {
            total_sum += data[i];
        }

        double mean = total_sum / n;
      
        __m256d s_vec = _mm256_setzero_pd();
        __m256d mean_vec = _mm256_set1_pd(mean);

        for (i = 0; i <= n - 4; i += 4) {
            __m256d v = _mm256_loadu_pd(&data[i]);
            __m256d diff = _mm256_sub_pd(v, mean_vec);
            s_vec = _mm256_add_pd(s_vec, _mm256_mul_pd(diff, diff));
        }

        double temp_s[4];
        _mm256_storeu_pd(temp_s, s_vec);
        double total_s = temp_s[0] + temp_s[1] + temp_s[2] + temp_s[3];

        for (; i < n; ++i) {
            total_s += (data[i] - mean) * (data[i] - mean);
        }

        return {mean, total_s};
    }

    static std::pair<double, double> batchCalculateStatsScalar(const std::vector<double>& data) {
        double sum = 0.0;
        for(double val : data) sum += val;
        double mean = sum / data.size();
      
        double s = 0.0;
        for(double val : data) s += (val - mean) * (val - mean);
        return {mean, s};
    }
};