#include "core/EventSystem.h"
#include <algorithm>
#include <spdlog/spdlog.h>

namespace RoboQuant::Broker {

// EventDispatcher implementation
EventDispatcher::EventDispatcher() = default;

EventDispatcher::~EventDispatcher() {
    stop();
}

void EventDispatcher::start() {
    if (running_.load()) {
        return;
    }
    
    running_.store(true);
    worker_ = std::thread(&EventDispatcher::worker_thread, this);
    
    spdlog::info("EventDispatcher started");
}

void EventDispatcher::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    queue_cv_.notify_all();
    
    if (worker_.joinable()) {
        worker_.join();
    }
    
    spdlog::info("EventDispatcher stopped");
}

void EventDispatcher::publish(EventPtr event) {
    if (!event) {
        spdlog::warn("Attempted to publish null event");
        return;
    }
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        event_queue_.push(event);
    }
    queue_cv_.notify_one();
}

void EventDispatcher::publish_sync(EventPtr event) {
    if (!event) {
        spdlog::warn("Attempted to publish null event synchronously");
        return;
    }
    
    dispatch_event(event);
}

void EventDispatcher::subscribe(EventType type, EventHandlerPtr handler) {
    if (!handler) {
        spdlog::warn("Attempted to subscribe null handler for event type: {}", 
                    static_cast<int>(type));
        return;
    }
    
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    handlers_[type].push_back(handler);
    
    spdlog::debug("Subscribed handler for event type: {}", static_cast<int>(type));
}

void EventDispatcher::unsubscribe(EventType type, EventHandlerPtr handler) {
    if (!handler) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    auto& handlers = handlers_[type];
    
    handlers.erase(
        std::remove_if(handlers.begin(), handlers.end(),
                      [&handler](const std::weak_ptr<EventHandler>& weak_handler) {
                          return weak_handler.expired() || weak_handler.lock() == handler;
                      }),
        handlers.end());
    
    spdlog::debug("Unsubscribed handler for event type: {}", static_cast<int>(type));
}

std::future<EventPtr> EventDispatcher::wait_for_event_async(EventType type) {
    std::lock_guard<std::mutex> lock(promises_mutex_);
    event_promises_[type].emplace_back();
    return event_promises_[type].back().get_future();
}

size_t EventDispatcher::pending_events() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return event_queue_.size();
}

size_t EventDispatcher::total_handlers() const {
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    size_t total = 0;
    for (const auto& [type, handlers] : handlers_) {
        total += handlers.size();
    }
    return total;
}

void EventDispatcher::worker_thread() {
    spdlog::debug("EventDispatcher worker thread started");
    
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        queue_cv_.wait(lock, [this] {
            return !event_queue_.empty() || !running_.load();
        });
        
        if (!running_.load()) {
            break;
        }
        
        std::queue<EventPtr> local_queue;
        local_queue.swap(event_queue_);
        lock.unlock();
        
        // Process all events in the local queue
        while (!local_queue.empty()) {
            auto event = local_queue.front();
            local_queue.pop();
            
            try {
                dispatch_event(event);
            } catch (const std::exception& e) {
                spdlog::error("Exception in event dispatch: {}", e.what());
            } catch (...) {
                spdlog::error("Unknown exception in event dispatch");
            }
        }
    }
    
    spdlog::debug("EventDispatcher worker thread stopped");
}

void EventDispatcher::dispatch_event(const EventPtr& event) {
    if (!event) {
        return;
    }
    
    // Dispatch to handlers
    {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        auto it = handlers_.find(event->type());
        if (it != handlers_.end()) {
            // Clean up expired weak pointers and dispatch to valid handlers
            auto& handlers = it->second;
            handlers.erase(
                std::remove_if(handlers.begin(), handlers.end(),
                              [&event](const EventHandlerPtr& handler) {
                                  if (!handler) {
                                      return true; // Remove null handlers
                                  }
                                  
                                  try {
                                      if (handler->can_handle(event->type())) {
                                          handler->handle_event(event);
                                      }
                                  } catch (const std::exception& e) {
                                      spdlog::error("Exception in event handler: {}", e.what());
                                  } catch (...) {
                                      spdlog::error("Unknown exception in event handler");
                                  }
                                  
                                  return false; // Keep valid handlers
                              }),
                handlers.end());
        }
    }
    
    // Notify promise waiters
    notify_promise_waiters(event);
}

void EventDispatcher::notify_promise_waiters(const EventPtr& event) {
    std::lock_guard<std::mutex> lock(promises_mutex_);
    auto it = event_promises_.find(event->type());
    if (it != event_promises_.end()) {
        auto& promises = it->second;
        for (auto& promise : promises) {
            try {
                promise.set_value(event);
            } catch (const std::exception& e) {
                spdlog::error("Exception setting promise value: {}", e.what());
            }
        }
        promises.clear();
    }
}

// Global instance
EventDispatcher& get_event_dispatcher() {
    static EventDispatcher instance;
    return instance;
}

// Convenience functions
void publish_event(EventPtr event) {
    get_event_dispatcher().publish(event);
}

void subscribe_to_events(EventType type, EventHandlerPtr handler) {
    get_event_dispatcher().subscribe(type, handler);
}

// Event creation helpers
EventPtr make_order_event(OrderId order_id, OrderStatus status, std::string message) {
    return std::make_shared<OrderEvent>(std::move(order_id), status, std::move(message));
}

EventPtr make_trade_event(TradeId trade_id, OrderId order_id, AssetId asset_id,
                         OrderSide side, Quantity quantity, Price price) {
    return std::make_shared<TradeEvent>(std::move(trade_id), std::move(order_id),
                                       std::move(asset_id), side, quantity, price);
}

EventPtr make_account_event(AccountId account_id, Amount balance, Amount available) {
    return std::make_shared<AccountEvent>(std::move(account_id), balance, available);
}

EventPtr make_connection_event(BrokerType broker_type, ConnectionStatus status, std::string message) {
    return std::make_shared<ConnectionEvent>(broker_type, status, std::move(message));
}

EventPtr make_error_event(ErrorCode error_code, std::string message, std::any context) {
    return std::make_shared<ErrorEvent>(error_code, std::move(message), std::move(context));
}

} // namespace RoboQuant::Broker
