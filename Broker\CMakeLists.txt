cmake_minimum_required(VERSION 3.20)
project(Broker_Modern VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

if(MSVC)
    add_compile_options(/W4 /WX /utf-8)
    add_compile_definitions(_WIN32_WINNT=0x0A00)
else()
    add_compile_options(-Wall -Wextra -Werror -pedantic)
endif()

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找依赖 - 使用父项目已配置的依赖
find_package(Boost REQUIRED COMPONENTS system thread)

# nlohmann_json 和 spdlog 通过父项目的 FetchContent 提供
# 如果作为独立项目构建，则需要查找
if(NOT TARGET nlohmann_json::nlohmann_json)
    find_package(nlohmann_json REQUIRED)
endif()

if(NOT TARGET spdlog::spdlog)
    find_package(spdlog REQUIRED)
endif()

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../DataHub_Modern/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../3rdParty/concurrentqueue
)

add_subdirectory(src)
add_subdirectory(tests)
add_subdirectory(examples)

add_library(broker_modern STATIC
    src/core/Types.cpp
    src/core/ErrorHandling.cpp
    src/core/Configuration.cpp
    src/core/EventSystem.cpp
    src/orders/Order.cpp
    src/accounts/Account.cpp
    src/brokers/BrokerInterface.cpp
    src/brokers/SimulationBroker.cpp
    src/brokers/CtpBroker.cpp
    src/brokers/CtpConfigManager.cpp
    src/brokers/CtpOptimizer.cpp
    src/utils/ThreadSafety.cpp
)

target_link_libraries(broker_modern
    Boost::system
    Boost::thread
    nlohmann_json::nlohmann_json
    spdlog::spdlog
)

set_target_properties(broker_modern PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    POSITION_INDEPENDENT_CODE ON
)

install(TARGETS broker_modern
    EXPORT BrokerModernTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)

include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/BrokerModernConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

export(EXPORT BrokerModernTargets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/BrokerModernTargets.cmake"
    NAMESPACE BrokerModern::
)

configure_file(cmake/BrokerModernConfig.cmake
    "${CMAKE_CURRENT_BINARY_DIR}/BrokerModernConfig.cmake"
    COPYONLY
)

install(EXPORT BrokerModernTargets
    FILE BrokerModernTargets.cmake
    NAMESPACE BrokerModern::
    DESTINATION lib/cmake/BrokerModern
)

install(FILES
    cmake/BrokerModernConfig.cmake
    "${CMAKE_CURRENT_BINARY_DIR}/BrokerModernConfigVersion.cmake"
    DESTINATION lib/cmake/BrokerModern
)
