#include "core/Configuration.h"
#include <spdlog/spdlog.h>
#include <fstream>
#include <regex>
#include <cstdlib>

namespace RoboQuant::Broker {

// ConfigChange implementation
nlohmann::json ConfigChange::to_json() const {
    nlohmann::json j;
    j["key"] = key;
    j["old_value"] = old_value.json();
    j["new_value"] = new_value.json();
    j["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()).count();
    j["source"] = source;
    return j;
}

ConfigChange ConfigChange::from_json(const nlohmann::json& j) {
    ConfigChange change;
    change.key = j.at("key").get<std::string>();
    change.old_value = ConfigValue(j.at("old_value"));
    change.new_value = ConfigValue(j.at("new_value"));
    change.source = j.value("source", "");
    
    if (j.contains("timestamp")) {
        auto ms = j["timestamp"].get<int64_t>();
        change.timestamp = TimePoint(std::chrono::milliseconds(ms));
    }
    
    return change;
}

// ConfigSection implementation
ConfigValue ConfigSection::get(const std::string& key) const {
    if (data_.contains(key)) {
        return ConfigValue(data_[key]);
    }
    return ConfigValue();
}

ConfigValue ConfigSection::get(const std::string& key, const ConfigValue& default_value) const {
    if (data_.contains(key)) {
        return ConfigValue(data_[key]);
    }
    return default_value;
}

void ConfigSection::set(const std::string& key, const ConfigValue& value) {
    data_[key] = value.json();
}

bool ConfigSection::has(const std::string& key) const {
    return data_.contains(key);
}

void ConfigSection::remove(const std::string& key) {
    data_.erase(key);
}

std::vector<std::string> ConfigSection::keys() const {
    std::vector<std::string> result;
    for (auto it = data_.begin(); it != data_.end(); ++it) {
        result.push_back(it.key());
    }
    return result;
}

ConfigSection ConfigSection::get_section(const std::string& key) const {
    if (data_.contains(key) && data_[key].is_object()) {
        return ConfigSection(data_[key]);
    }
    return ConfigSection();
}

void ConfigSection::set_section(const std::string& key, const ConfigSection& section) {
    data_[key] = section.to_json();
}

bool ConfigSection::validate_schema(const nlohmann::json& schema) const {
    // Basic JSON schema validation would go here
    // For now, just return true
    return true;
}

// ConfigurationManager implementation
ConfigurationManager& ConfigurationManager::instance() {
    static ConfigurationManager manager;
    return manager;
}

ConfigurationManager::~ConfigurationManager() {
    disable_hot_reload();
}

Result<void> ConfigurationManager::load_from_file(const std::filesystem::path& file_path) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Cannot open config file: " + file_path.string());
        }
        
        nlohmann::json json;
        file >> json;
        
        return load_from_json(json);
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to load config file: " + std::string(e.what()));
    }
}

Result<void> ConfigurationManager::load_from_string(const std::string& json_str) {
    try {
        auto json = nlohmann::json::parse(json_str);
        return load_from_json(json);
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to parse JSON string: " + std::string(e.what()));
    }
}

Result<void> ConfigurationManager::load_from_json(const nlohmann::json& json) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    // Validate against schema if available
    if (!schema_.empty()) {
        auto validation_result = validate_json_schema(json, schema_);
        if (!validation_result) {
            return validation_result;
        }
    }
    
    config_data_ = json;
    last_modified_ = std::chrono::system_clock::now();
    change_count_.fetch_add(1);
    
    // Apply environment overrides
    if (env_override_enabled_) {
        apply_env_overrides();
    }
    
    spdlog::info("Configuration loaded successfully");
    return Result<void>::success();
}

Result<void> ConfigurationManager::save_to_file(const std::filesystem::path& file_path) const {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Cannot create config file: " + file_path.string());
        }
        
        std::shared_lock<std::shared_mutex> lock(config_mutex_);
        file << config_data_.dump(4);
        
        spdlog::info("Configuration saved to: {}", file_path.string());
        return Result<void>::success();
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to save config file: " + std::string(e.what()));
    }
}

std::string ConfigurationManager::save_to_string() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    return config_data_.dump(4);
}

nlohmann::json ConfigurationManager::save_to_json() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    return config_data_;
}

ConfigValue ConfigurationManager::get(const std::string& key) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    
    std::string resolved_key = resolve_key_with_profile(key);
    
    // Support nested keys with dot notation
    auto keys = [&]() {
        std::vector<std::string> result;
        std::stringstream ss(resolved_key);
        std::string item;
        while (std::getline(ss, item, '.')) {
            result.push_back(item);
        }
        return result;
    }();
    
    nlohmann::json current = config_data_;
    for (const auto& k : keys) {
        if (current.contains(k)) {
            current = current[k];
        } else {
            return ConfigValue();
        }
    }
    
    return ConfigValue(current);
}

ConfigValue ConfigurationManager::get(const std::string& key, const ConfigValue& default_value) const {
    auto value = get(key);
    return value.is_null() ? default_value : value;
}

void ConfigurationManager::set(const std::string& key, const ConfigValue& value) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    auto old_value = get(key);
    
    // Support nested keys with dot notation
    auto keys = [&]() {
        std::vector<std::string> result;
        std::stringstream ss(key);
        std::string item;
        while (std::getline(ss, item, '.')) {
            result.push_back(item);
        }
        return result;
    }();
    
    nlohmann::json* current = &config_data_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->contains(keys[i]) || !(*current)[keys[i]].is_object()) {
            (*current)[keys[i]] = nlohmann::json::object();
        }
        current = &(*current)[keys[i]];
    }
    
    (*current)[keys.back()] = value.json();
    
    last_modified_ = std::chrono::system_clock::now();
    change_count_.fetch_add(1);
    
    lock.unlock();
    
    // Notify change
    notify_change(key, old_value, value);
    
    spdlog::debug("Configuration key '{}' updated", key);
}

ConfigSection ConfigurationManager::get_section(const std::string& key) const {
    auto value = get(key);
    if (value.is_object()) {
        return ConfigSection(value.json());
    }
    return ConfigSection();
}

void ConfigurationManager::set_section(const std::string& key, const ConfigSection& section) {
    set(key, ConfigValue(section.to_json()));
}

bool ConfigurationManager::has(const std::string& key) const {
    return !get(key).is_null();
}

void ConfigurationManager::remove(const std::string& key) {
    std::unique_lock<std::shared_mutex> lock(config_mutex_);
    
    auto old_value = get(key);
    
    // Support nested keys with dot notation
    auto keys = [&]() {
        std::vector<std::string> result;
        std::stringstream ss(key);
        std::string item;
        while (std::getline(ss, item, '.')) {
            result.push_back(item);
        }
        return result;
    }();
    
    nlohmann::json* current = &config_data_;
    for (size_t i = 0; i < keys.size() - 1; ++i) {
        if (!current->contains(keys[i])) {
            return; // Key doesn't exist
        }
        current = &(*current)[keys[i]];
    }
    
    current->erase(keys.back());
    
    last_modified_ = std::chrono::system_clock::now();
    change_count_.fetch_add(1);
    
    lock.unlock();
    
    // Notify change
    notify_change(key, old_value, ConfigValue());
    
    spdlog::debug("Configuration key '{}' removed", key);
}

std::vector<std::string> ConfigurationManager::keys() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    std::vector<std::string> result;
    
    std::function<void(const nlohmann::json&, const std::string&)> collect_keys = 
        [&](const nlohmann::json& obj, const std::string& prefix) {
            for (auto it = obj.begin(); it != obj.end(); ++it) {
                std::string key = prefix.empty() ? it.key() : prefix + "." + it.key();
                result.push_back(key);
                
                if (it.value().is_object()) {
                    collect_keys(it.value(), key);
                }
            }
        };
    
    if (config_data_.is_object()) {
        collect_keys(config_data_, "");
    }
    
    return result;
}

std::vector<std::string> ConfigurationManager::keys(const std::string& prefix) const {
    auto all_keys = keys();
    std::vector<std::string> result;
    
    for (const auto& key : all_keys) {
        if (key.starts_with(prefix)) {
            result.push_back(key);
        }
    }
    
    return result;
}

void ConfigurationManager::enable_hot_reload(const std::filesystem::path& file_path, Duration check_interval) {
    disable_hot_reload(); // Stop any existing hot reload
    
    hot_reload_file_ = file_path;
    hot_reload_interval_ = check_interval;
    hot_reload_enabled_.store(true);
    
    // Get initial file time
    if (std::filesystem::exists(file_path)) {
        last_file_time_ = std::filesystem::last_write_time(file_path);
    }
    
    hot_reload_thread_ = std::thread(&ConfigurationManager::hot_reload_worker, this);
    
    spdlog::info("Hot reload enabled for: {}", file_path.string());
}

void ConfigurationManager::disable_hot_reload() {
    if (hot_reload_enabled_.load()) {
        hot_reload_enabled_.store(false);
        if (hot_reload_thread_.joinable()) {
            hot_reload_thread_.join();
        }
        spdlog::info("Hot reload disabled");
    }
}

void ConfigurationManager::subscribe_to_changes(const std::string& key_pattern, ConfigChangeCallback callback) {
    std::unique_lock<std::shared_mutex> lock(callbacks_mutex_);
    change_callbacks_[key_pattern].push_back(std::move(callback));
}

void ConfigurationManager::unsubscribe_from_changes(const std::string& key_pattern) {
    std::unique_lock<std::shared_mutex> lock(callbacks_mutex_);
    change_callbacks_.erase(key_pattern);
}

void ConfigurationManager::notify_change(const std::string& key, const ConfigValue& old_value, const ConfigValue& new_value) {
    ConfigChange change;
    change.key = key;
    change.old_value = old_value;
    change.new_value = new_value;
    change.source = "api";
    
    std::shared_lock<std::shared_mutex> lock(callbacks_mutex_);
    for (const auto& [pattern, callbacks] : change_callbacks_) {
        if (matches_pattern(key, pattern)) {
            for (const auto& callback : callbacks) {
                try {
                    callback(change);
                } catch (const std::exception& e) {
                    spdlog::error("Exception in config change callback: {}", e.what());
                }
            }
        }
    }
}

void ConfigurationManager::hot_reload_worker() {
    while (hot_reload_enabled_.load()) {
        try {
            if (std::filesystem::exists(hot_reload_file_)) {
                auto current_time = std::filesystem::last_write_time(hot_reload_file_);
                if (current_time != last_file_time_) {
                    spdlog::info("Configuration file changed, reloading...");
                    auto result = load_from_file(hot_reload_file_);
                    if (result) {
                        last_file_time_ = current_time;
                        spdlog::info("Configuration reloaded successfully");
                    } else {
                        spdlog::error("Failed to reload configuration: {}", result.error().message);
                    }
                }
            }
        } catch (const std::exception& e) {
            spdlog::error("Exception in hot reload worker: {}", e.what());
        }
        
        std::this_thread::sleep_for(hot_reload_interval_);
    }
}

void ConfigurationManager::apply_env_overrides() {
    // This would scan environment variables and override config values
    // Implementation would depend on specific requirements
}

std::string ConfigurationManager::resolve_key_with_profile(const std::string& key) const {
    if (!active_profile_.empty()) {
        std::string profile_key = "profiles." + active_profile_ + "." + key;
        if (has(profile_key)) {
            return profile_key;
        }
    }
    return key;
}

bool ConfigurationManager::matches_pattern(const std::string& key, const std::string& pattern) const {
    // Simple wildcard matching - could be enhanced with regex
    if (pattern == "*") return true;
    if (pattern.ends_with("*")) {
        return key.starts_with(pattern.substr(0, pattern.length() - 1));
    }
    return key == pattern;
}

size_t ConfigurationManager::total_keys() const {
    return keys().size();
}

// Utility functions
Result<nlohmann::json> load_json_file(const std::filesystem::path& file_path) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return Result<nlohmann::json>::failure(ErrorCode::InvalidParameter, 
                "Cannot open file: " + file_path.string());
        }
        
        nlohmann::json json;
        file >> json;
        return Result<nlohmann::json>::success(std::move(json));
    } catch (const std::exception& e) {
        return Result<nlohmann::json>::failure(ErrorCode::InvalidParameter, 
            "Failed to load JSON file: " + std::string(e.what()));
    }
}

Result<void> save_json_file(const std::filesystem::path& file_path, const nlohmann::json& json) {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return Result<void>::failure(ErrorCode::InvalidParameter, 
                "Cannot create file: " + file_path.string());
        }
        
        file << json.dump(4);
        return Result<void>::success();
    } catch (const std::exception& e) {
        return Result<void>::failure(ErrorCode::InvalidParameter, 
            "Failed to save JSON file: " + std::string(e.what()));
    }
}

Result<void> validate_json_schema(const nlohmann::json& data, const nlohmann::json& schema) {
    // Basic validation - in a real implementation, you'd use a proper JSON schema validator
    return Result<void>::success();
}

std::optional<std::string> get_env_var(const std::string& name) {
    const char* value = std::getenv(name.c_str());
    return value ? std::optional<std::string>(value) : std::nullopt;
}

std::string get_env_var(const std::string& name, const std::string& default_value) {
    auto value = get_env_var(name);
    return value ? *value : default_value;
}

} // namespace RoboQuant::Broker
