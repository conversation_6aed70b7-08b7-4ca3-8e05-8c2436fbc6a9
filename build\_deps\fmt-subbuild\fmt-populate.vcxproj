﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3094592E-926C-3308-A183-BFAFE74A596C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>fmt-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-mkdir.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (git clone) for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\fmt-populate-gitinfo.txt;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-configure.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\tmp\fmt-populate-cfgcmd.txt;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-build.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-install.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\a96121c4600e7dda73f761d6c6a1c5bc\fmt-populate-test.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\Experiments\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\40477b9271a0934fcd99e6b323098173\fmt-populate-complete.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'fmt-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E make_directory E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/Debug/fmt-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-install;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-mkdir;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-download;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-patch;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-configure;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-build;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\src\fmt-populate-stamp\Debug\fmt-populate-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\Debug\fmt-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\c23ab31bc2dc3872cb399a1188e82f3f\fmt-populate.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\Debug\fmt-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\fmt-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild -BE:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild --check-stamp-file E:/lab/RoboQuant/Experiments/build/_deps/fmt-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\RepositoryInfo.txt.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\cfgcmd.txt.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\gitclone.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\gitupdate.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\mkdirs.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\fmt-populate-prefix\tmp\fmt-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\CMakeFiles\fmt-populate">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\Experiments\build\_deps\fmt-subbuild\ZERO_CHECK.vcxproj">
      <Project>{B270019F-08BA-38EB-8A27-29E20B356BE7}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>