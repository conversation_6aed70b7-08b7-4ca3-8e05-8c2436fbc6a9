/**
 * @file ExpressionEngine.h
 * @brief Modern expression parsing and evaluation engine
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include <variant>
#include <functional>
#include <memory>
#include <shared_mutex>
#include <unordered_map>
#include <optional>
#include <vector>
#include <string>

namespace RoboQuant::Trading {

/**
 * @brief Expression value types
 */
using ExpressionValue = std::variant<double, bool, std::string>;

/**
 * @brief Expression context for variable resolution
 */
class ExpressionContext {
public:
    ExpressionContext() = default;
    ~ExpressionContext() = default;

    // Variable management
    void set_variable(const std::string& name, ExpressionValue value);
    [[nodiscard]] std::optional<ExpressionValue> get_variable(const std::string& name) const;
    [[nodiscard]] bool has_variable(const std::string& name) const;
    void remove_variable(const std::string& name);
    void clear_variables();

    // Batch operations
    void set_variables(const std::unordered_map<std::string, ExpressionValue>& variables);
    [[nodiscard]] std::unordered_map<std::string, ExpressionValue> get_all_variables() const;

    // Type-safe accessors
    template<typename T>
    [[nodiscard]] std::optional<T> get_variable_as(const std::string& name) const {
        auto value = get_variable(name);
        if (value && std::holds_alternative<T>(*value)) {
            return std::get<T>(*value);
        }
        return std::nullopt;
    }

    // Convenience methods
    void set_double(const std::string& name, double value) { set_variable(name, value); }
    void set_bool(const std::string& name, bool value) { set_variable(name, value); }
    void set_string(const std::string& name, const std::string& value) { set_variable(name, value); }

    [[nodiscard]] std::optional<double> get_double(const std::string& name) const {
        return get_variable_as<double>(name);
    }
    [[nodiscard]] std::optional<bool> get_bool(const std::string& name) const {
        return get_variable_as<bool>(name);
    }
    [[nodiscard]] std::optional<std::string> get_string(const std::string& name) const {
        return get_variable_as<std::string>(name);
    }

private:
    mutable std::shared_mutex variables_mutex_;
    std::unordered_map<std::string, ExpressionValue> variables_;
};

/**
 * @brief Expression function interface
 */
class ExpressionFunction {
public:
    virtual ~ExpressionFunction() = default;
    virtual ExpressionValue call(const std::vector<ExpressionValue>& args) const = 0;
    virtual std::string get_name() const = 0;
    virtual size_t get_arity() const = 0; // Number of arguments
};

/**
 * @brief Template-based expression function
 */
template<typename Func>
class TemplateExpressionFunction : public ExpressionFunction {
public:
    explicit TemplateExpressionFunction(std::string name, size_t arity, Func func)
        : name_(std::move(name)), arity_(arity), func_(std::move(func)) {}

    ExpressionValue call(const std::vector<ExpressionValue>& args) const override {
        if (args.size() != arity_) {
            throw std::invalid_argument("Function " + name_ + " expects " + 
                                      std::to_string(arity_) + " arguments, got " + 
                                      std::to_string(args.size()));
        }
        return func_(args);
    }

    std::string get_name() const override { return name_; }
    size_t get_arity() const override { return arity_; }

private:
    std::string name_;
    size_t arity_;
    Func func_;
};

/**
 * @brief Expression AST node
 */
class ExpressionNode {
public:
    virtual ~ExpressionNode() = default;
    virtual ExpressionValue evaluate(const ExpressionContext& context) const = 0;
    virtual std::string to_string() const = 0;
};

/**
 * @brief Compiled expression
 */
class CompiledExpression {
public:
    explicit CompiledExpression(std::unique_ptr<ExpressionNode> root);
    ~CompiledExpression() = default;

    // Non-copyable, movable
    CompiledExpression(const CompiledExpression&) = delete;
    CompiledExpression& operator=(const CompiledExpression&) = delete;
    CompiledExpression(CompiledExpression&&) = default;
    CompiledExpression& operator=(CompiledExpression&&) = default;

    // Evaluation
    [[nodiscard]] ExpressionValue evaluate(const ExpressionContext& context) const;
    
    // Type-safe evaluation
    template<typename T>
    [[nodiscard]] std::optional<T> evaluate_as(const ExpressionContext& context) const {
        auto result = evaluate(context);
        if (std::holds_alternative<T>(result)) {
            return std::get<T>(result);
        }
        return std::nullopt;
    }

    [[nodiscard]] std::optional<double> evaluate_as_double(const ExpressionContext& context) const {
        return evaluate_as<double>(context);
    }
    [[nodiscard]] std::optional<bool> evaluate_as_bool(const ExpressionContext& context) const {
        return evaluate_as<bool>(context);
    }
    [[nodiscard]] std::optional<std::string> evaluate_as_string(const ExpressionContext& context) const {
        return evaluate_as<std::string>(context);
    }

    // Expression info
    [[nodiscard]] std::string to_string() const;

private:
    std::unique_ptr<ExpressionNode> root_;
};

/**
 * @brief Expression parser and compiler
 */
class ExpressionEngine {
public:
    ExpressionEngine();
    ~ExpressionEngine() = default;

    // Non-copyable, movable
    ExpressionEngine(const ExpressionEngine&) = delete;
    ExpressionEngine& operator=(const ExpressionEngine&) = delete;
    ExpressionEngine(ExpressionEngine&&) = default;
    ExpressionEngine& operator=(ExpressionEngine&&) = default;

    // Expression compilation
    [[nodiscard]] std::unique_ptr<CompiledExpression> compile(const std::string& expression);
    
    // Direct evaluation (compiles and evaluates)
    [[nodiscard]] ExpressionValue evaluate(const std::string& expression, const ExpressionContext& context);
    
    // Type-safe direct evaluation
    template<typename T>
    [[nodiscard]] std::optional<T> evaluate_as(const std::string& expression, const ExpressionContext& context) {
        auto result = evaluate(expression, context);
        if (std::holds_alternative<T>(result)) {
            return std::get<T>(result);
        }
        return std::nullopt;
    }

    // Function registration
    void register_function(std::unique_ptr<ExpressionFunction> function);
    
    template<typename Func>
    void register_function(const std::string& name, size_t arity, Func func) {
        auto function = std::make_unique<TemplateExpressionFunction<Func>>(name, arity, std::move(func));
        register_function(std::move(function));
    }

    void unregister_function(const std::string& name);
    [[nodiscard]] bool has_function(const std::string& name) const;
    [[nodiscard]] std::vector<std::string> get_registered_functions() const;

    // Built-in functions
    void register_builtin_functions();
    void register_math_functions();
    void register_string_functions();
    void register_trading_functions();

    // Expression validation
    [[nodiscard]] bool is_valid_expression(const std::string& expression) const;
    [[nodiscard]] std::vector<std::string> get_variables_in_expression(const std::string& expression) const;

    // Error handling
    struct ParseError {
        std::string message;
        size_t position{0};
        std::string expression;
    };
    
    [[nodiscard]] std::optional<ParseError> get_last_error() const;

private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};

/**
 * @brief Expression cache for compiled expressions
 */
class ExpressionCache {
public:
    explicit ExpressionCache(size_t max_size = 1000);
    ~ExpressionCache() = default;

    // Non-copyable, movable
    ExpressionCache(const ExpressionCache&) = delete;
    ExpressionCache& operator=(const ExpressionCache&) = delete;
    ExpressionCache(ExpressionCache&&) = default;
    ExpressionCache& operator=(ExpressionCache&&) = default;

    // Cache operations
    void put(const std::string& expression, std::unique_ptr<CompiledExpression> compiled);
    [[nodiscard]] CompiledExpression* get(const std::string& expression) const;
    [[nodiscard]] bool contains(const std::string& expression) const;
    void remove(const std::string& expression);
    void clear();

    // Cache statistics
    [[nodiscard]] size_t size() const;
    [[nodiscard]] size_t max_size() const;
    [[nodiscard]] size_t hit_count() const;
    [[nodiscard]] size_t miss_count() const;
    [[nodiscard]] double hit_rate() const;

private:
    void evict_lru();

    size_t max_size_;
    mutable std::shared_mutex cache_mutex_;
    std::unordered_map<std::string, std::unique_ptr<CompiledExpression>> cache_;
    std::list<std::string> lru_list_;
    std::unordered_map<std::string, std::list<std::string>::iterator> lru_map_;
    
    mutable std::atomic<size_t> hit_count_{0};
    mutable std::atomic<size_t> miss_count_{0};
};

/**
 * @brief Expression manager with caching and thread safety
 */
class ExpressionManager {
public:
    explicit ExpressionManager(size_t cache_size = 1000);
    ~ExpressionManager() = default;

    // Non-copyable, movable
    ExpressionManager(const ExpressionManager&) = delete;
    ExpressionManager& operator=(const ExpressionManager&) = delete;
    ExpressionManager(ExpressionManager&&) = default;
    ExpressionManager& operator=(ExpressionManager&&) = default;

    // Expression evaluation with caching
    [[nodiscard]] ExpressionValue evaluate(const std::string& expression, const ExpressionContext& context);
    
    template<typename T>
    [[nodiscard]] std::optional<T> evaluate_as(const std::string& expression, const ExpressionContext& context) {
        auto result = evaluate(expression, context);
        if (std::holds_alternative<T>(result)) {
            return std::get<T>(result);
        }
        return std::nullopt;
    }

    // Function registration
    template<typename Func>
    void register_function(const std::string& name, size_t arity, Func func) {
        engine_.register_function(name, arity, std::move(func));
    }

    // Cache management
    void clear_cache() { cache_.clear(); }
    [[nodiscard]] double get_cache_hit_rate() const { return cache_.hit_rate(); }

    // Built-in functions
    void register_all_builtin_functions() { engine_.register_builtin_functions(); }

private:
    ExpressionEngine engine_;
    ExpressionCache cache_;
};

} // namespace RoboQuant::Trading
