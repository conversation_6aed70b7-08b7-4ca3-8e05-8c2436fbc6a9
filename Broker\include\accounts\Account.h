#pragma once

#include "core/Types.h"
#include <nlohmann/json.hpp>
#include <unordered_map>
#include <optional>

namespace RoboQuant::Broker {

// Account balance information
struct AccountBalance {
    Amount total_balance{0.0};      // Total account value
    Amount available_balance{0.0};  // Available for trading
    Amount frozen_balance{0.0};     // Frozen/locked funds
    Amount margin_used{0.0};        // Used margin
    Amount margin_available{0.0};   // Available margin
    Amount unrealized_pnl{0.0};     // Unrealized P&L
    Amount realized_pnl{0.0};       // Realized P&L today
    Amount commission_paid{0.0};    // Commission paid today
    std::string currency{"CNY"};    // Account currency
    
    // Calculated fields
    Amount net_liquidation_value() const { return total_balance + unrealized_pnl; }
    Amount buying_power() const { return available_balance + margin_available; }
    double margin_ratio() const { 
        return total_balance > 0 ? margin_used / total_balance : 0.0; 
    }
    
    nlohmann::json to_json() const;
    static AccountBalance from_json(const nlohmann::json& j);
};

// Account risk metrics
struct RiskMetrics {
    double max_position_size{0.1};     // Max position as fraction of account
    double max_daily_loss{0.05};       // Max daily loss as fraction
    double max_drawdown{0.2};          // Max drawdown allowed
    Amount max_order_value{100000.0};  // Max single order value
    Quantity max_order_quantity{10000}; // Max single order quantity
    double leverage_limit{1.0};        // Maximum leverage allowed
    
    // Current risk status
    double current_leverage{0.0};
    double current_drawdown{0.0};
    Amount daily_pnl{0.0};
    bool risk_limit_breached{false};
    std::string risk_message;
    
    nlohmann::json to_json() const;
    static RiskMetrics from_json(const nlohmann::json& j);
};

// Account information
class Account {
public:
    Account() = default;
    Account(AccountId account_id, BrokerType broker_type, AccountType account_type = AccountType::Cash);
    
    // Copy and move
    Account(const Account&) = default;
    Account& operator=(const Account&) = default;
    Account(Account&&) = default;
    Account& operator=(Account&&) = default;
    
    // Basic information
    const AccountId& account_id() const noexcept { return account_id_; }
    BrokerType broker_type() const noexcept { return broker_type_; }
    AccountType account_type() const noexcept { return account_type_; }
    const std::string& account_name() const noexcept { return account_name_; }
    const std::string& owner_name() const noexcept { return owner_name_; }
    
    // Status
    bool is_active() const noexcept { return is_active_; }
    bool is_trading_enabled() const noexcept { return trading_enabled_; }
    ConnectionStatus connection_status() const noexcept { return connection_status_; }
    
    // Balance and risk
    const AccountBalance& balance() const noexcept { return balance_; }
    const RiskMetrics& risk_metrics() const noexcept { return risk_metrics_; }
    
    // Timestamps
    TimePoint create_time() const noexcept { return create_time_; }
    TimePoint last_update_time() const noexcept { return last_update_time_; }
    
    // Metadata
    const std::unordered_map<std::string, std::string>& metadata() const noexcept { return metadata_; }
    
    // Setters
    void set_account_name(const std::string& name) { account_name_ = name; update_timestamp(); }
    void set_owner_name(const std::string& name) { owner_name_ = name; update_timestamp(); }
    void set_active(bool active) { is_active_ = active; update_timestamp(); }
    void set_trading_enabled(bool enabled) { trading_enabled_ = enabled; update_timestamp(); }
    void set_connection_status(ConnectionStatus status) { connection_status_ = status; update_timestamp(); }
    
    // Balance management
    void update_balance(const AccountBalance& new_balance);
    void add_balance(Amount amount);
    void subtract_balance(Amount amount);
    void freeze_balance(Amount amount);
    void unfreeze_balance(Amount amount);
    void update_unrealized_pnl(Amount pnl);
    void add_realized_pnl(Amount pnl);
    void add_commission(Amount commission);
    
    // Risk management
    void update_risk_metrics(const RiskMetrics& metrics);
    void set_risk_limit(const std::string& limit_type, double value);
    bool check_risk_limits(Amount order_value, Quantity order_quantity) const;
    bool is_risk_limit_breached() const { return risk_metrics_.risk_limit_breached; }
    
    // Metadata management
    void set_metadata(const std::string& key, const std::string& value);
    std::optional<std::string> get_metadata(const std::string& key) const;
    void remove_metadata(const std::string& key);
    
    // Validation
    bool is_valid() const;
    std::string validation_error() const;
    
    // Serialization
    nlohmann::json to_json() const;
    static Account from_json(const nlohmann::json& j);

    // String representation
    std::string to_string() const;
    
    // Comparison
    bool operator==(const Account& other) const noexcept;
    bool operator!=(const Account& other) const noexcept { return !(*this == other); }

private:
    void update_timestamp() { last_update_time_ = std::chrono::system_clock::now(); }
    void calculate_risk_metrics();
    
    // Basic information
    AccountId account_id_;
    BrokerType broker_type_{BrokerType::Unknown};
    AccountType account_type_{AccountType::Cash};
    std::string account_name_;
    std::string owner_name_;
    
    // Status
    bool is_active_{true};
    bool trading_enabled_{true};
    ConnectionStatus connection_status_{ConnectionStatus::Disconnected};
    
    // Financial data
    AccountBalance balance_;
    RiskMetrics risk_metrics_;
    
    // Timestamps
    TimePoint create_time_;
    TimePoint last_update_time_;
    
    // Additional metadata
    std::unordered_map<std::string, std::string> metadata_;
};

// Position information
class Position {
public:
    Position() = default;
    Position(AccountId account_id, AssetId asset_id, Quantity quantity = 0, Price average_price = 0.0);
    
    // Basic information
    const AccountId& account_id() const noexcept { return account_id_; }
    const AssetId& asset_id() const noexcept { return asset_id_; }
    Quantity quantity() const noexcept { return quantity_; }
    Quantity long_quantity() const noexcept { return long_quantity_; }
    Quantity short_quantity() const noexcept { return short_quantity_; }
    Price average_price() const noexcept { return average_price_; }
    Price market_price() const noexcept { return market_price_; }
    
    // P&L calculations
    Amount unrealized_pnl() const;
    Amount realized_pnl() const noexcept { return realized_pnl_; }
    Amount total_pnl() const { return unrealized_pnl() + realized_pnl_; }
    double return_rate() const;
    
    // Position management
    void add_quantity(Quantity qty, Price price);
    void reduce_quantity(Quantity qty, Price price);
    void update_market_price(Price price);
    void close_position(Price price);
    
    // Status
    bool is_long() const noexcept { return quantity_ > 0; }
    bool is_short() const noexcept { return quantity_ < 0; }
    bool is_flat() const noexcept { return quantity_ == 0; }
    
    // Serialization
    nlohmann::json to_json() const;
    static Position from_json(const nlohmann::json& j);

private:
    void update_average_price(Quantity new_qty, Price new_price);
    void update_timestamp() { last_update_time_ = std::chrono::system_clock::now(); }
    
    AccountId account_id_;
    AssetId asset_id_;
    Quantity quantity_{0};
    Quantity long_quantity_{0};
    Quantity short_quantity_{0};
    Price average_price_{0.0};
    Price market_price_{0.0};
    Amount realized_pnl_{0.0};
    TimePoint create_time_;
    TimePoint last_update_time_;
};

// Trade information
class Trade {
public:
    Trade() = default;
    Trade(TradeId trade_id, OrderId order_id, AccountId account_id, AssetId asset_id,
          OrderSide side, Quantity quantity, Price price, Commission commission = 0.0);
    
    // Basic information
    const TradeId& trade_id() const noexcept { return trade_id_; }
    const OrderId& order_id() const noexcept { return order_id_; }
    const AccountId& account_id() const noexcept { return account_id_; }
    const AssetId& asset_id() const noexcept { return asset_id_; }
    OrderSide side() const noexcept { return side_; }
    Quantity quantity() const noexcept { return quantity_; }
    Price price() const noexcept { return price_; }
    Commission commission() const noexcept { return commission_; }
    TimePoint trade_time() const noexcept { return trade_time_; }
    
    // Calculations
    Amount gross_value() const { return quantity_ * price_; }
    Amount net_value() const { return gross_value() - commission_; }
    
    // Serialization
    nlohmann::json to_json() const;
    static Trade from_json(const nlohmann::json& j);

private:
    TradeId trade_id_;
    OrderId order_id_;
    AccountId account_id_;
    AssetId asset_id_;
    OrderSide side_{OrderSide::Buy};
    Quantity quantity_{0};
    Price price_{0.0};
    Commission commission_{0.0};
    TimePoint trade_time_;
};

// Utility functions
AccountId generate_account_id();
TradeId generate_trade_id();
bool is_margin_account(const Account& account);
bool is_cash_account(const Account& account);
Amount calculate_buying_power(const Account& account);
Amount calculate_margin_requirement(const Account& account, const std::vector<Position>& positions);

} // namespace RoboQuant::Broker

// JSON serialization support
namespace nlohmann {
    template<>
    struct adl_serializer<RoboQuant::Broker::Account> {
        static void to_json(json& j, const RoboQuant::Broker::Account& account) {
            j = account.to_json();
        }
        
        static void from_json(const json& j, RoboQuant::Broker::Account& account) {
            account = RoboQuant::Broker::Account::from_json(j);
        }
    };
    
    template<>
    struct adl_serializer<RoboQuant::Broker::Position> {
        static void to_json(json& j, const RoboQuant::Broker::Position& position) {
            j = position.to_json();
        }
        
        static void from_json(const json& j, RoboQuant::Broker::Position& position) {
            position = RoboQuant::Broker::Position::from_json(j);
        }
    };
    
    template<>
    struct adl_serializer<RoboQuant::Broker::Trade> {
        static void to_json(json& j, const RoboQuant::Broker::Trade& trade) {
            j = trade.to_json();
        }
        
        static void from_json(const json& j, RoboQuant::Broker::Trade& trade) {
            trade = RoboQuant::Broker::Trade::from_json(j);
        }
    };
}
