name: doc

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    # Use Ubuntu 20.04 because doxygen 1.8.13 from Ubuntu 18.04 is broken.
    runs-on: ubuntu-20.04

    steps:
    - uses: actions/checkout@c85c95e3d7251135ab7dc9ce3241c5835cc595a9 # v3.5.3

    - name: Add ubuntu mirrors
      run: |
        # Github Actions caching proxy is at times unreliable
        # see https://github.com/actions/runner-images/issues/7048
        printf 'http://azure.archive.ubuntu.com/ubuntu\tpriority:1\n' | sudo tee /etc/apt/mirrors.txt
        curl http://mirrors.ubuntu.com/mirrors.txt | sudo tee --append /etc/apt/mirrors.txt
        sudo sed -i 's~http://azure.archive.ubuntu.com/ubuntu/~mirror+file:/etc/apt/mirrors.txt~' /etc/apt/sources.list

    - name: Create Build Environment
      run: |
        sudo apt update
        sudo apt install doxygen python3-virtualenv
        sudo npm install -g less clean-css
        cmake -E make_directory ${{runner.workspace}}/build

    - name: Build
      working-directory: ${{runner.workspace}}/build
      env:
        KEY: ${{secrets.KEY}}
      run: $GITHUB_WORKSPACE/support/build-docs.py
