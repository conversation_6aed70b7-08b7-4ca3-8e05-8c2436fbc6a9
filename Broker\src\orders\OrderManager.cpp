#include "orders/OrderManager.h"
#include "brokers/BrokerInterface.h"
#include <spdlog/spdlog.h>
#include <fstream>
#include <algorithm>

namespace RoboQuant::Broker {

// OrderFilter implementation
bool OrderFilter::matches(const Order& order) const {
    if (account_id && order.account_id() != *account_id) return false;
    if (asset_id && order.asset_id() != *asset_id) return false;
    if (side && order.side() != *side) return false;
    if (type && order.type() != *type) return false;
    if (status && order.status() != *status) return false;
    if (strategy_id && order.strategy_id() != *strategy_id) return false;
    if (from_time && order.create_time() < *from_time) return false;
    if (to_time && order.create_time() > *to_time) return false;
    return true;
}

// OrderManager implementation
OrderManager::OrderManager() {
    spdlog::info("OrderManager initialized");
}

OrderManager::~OrderManager() {
    stop_event_handling();
    spdlog::info("OrderManager destroyed");
}

Result<OrderId> OrderManager::submit_order(const OrderRequest& request) {
    // Validate the order request
    auto validation_result = validate_order_request(request);
    if (is_error(validation_result)) {
        spdlog::warn("Order validation failed: {}", request.asset_id);
        return get_error(validation_result);
    }
    
    // Create the order
    Order order(request);
    OrderId order_id = order.order_id();
    
    // Add to internal storage
    add_order_internal(order);
    
    // Submit to broker if available
    if (broker_interface_) {
        auto submit_result = broker_interface_->submit_order(order);
        if (is_error(submit_result)) {
            // Update order status to rejected
            update_order_status(order_id, OrderStatus::Rejected);
            update_order_error(order_id, "Broker submission failed");
            return get_error(submit_result);
        }
    } else {
        spdlog::warn("No broker interface available for order submission");
        update_order_status(order_id, OrderStatus::Rejected);
        update_order_error(order_id, "No broker interface available");
        return ErrorCode::BrokerError;
    }
    
    spdlog::info("Order submitted: {} for {} {} {}", 
                order_id, order.quantity(), order.asset_id(), to_string(order.side()));
    
    return order_id;
}

Result<void> OrderManager::cancel_order(const OrderId& order_id) {
    auto order_opt = get_order(order_id);
    if (!order_opt) {
        spdlog::warn("Order not found for cancellation: {}", order_id);
        return ErrorCode::OrderNotFound;
    }
    
    const auto& order = *order_opt;
    if (!order.is_active()) {
        spdlog::warn("Cannot cancel inactive order: {}", order_id);
        return ErrorCode::InvalidParameter;
    }
    
    // Update status to pending cancel
    update_order_status(order_id, OrderStatus::PendingCancel);
    
    // Cancel with broker if available
    if (broker_interface_) {
        auto cancel_result = broker_interface_->cancel_order(order);
        if (is_error(cancel_result)) {
            // Revert status if broker cancellation failed
            update_order_status(order_id, OrderStatus::New);
            return get_error(cancel_result);
        }
    } else {
        // Direct cancellation if no broker
        update_order_status(order_id, OrderStatus::Cancelled);
    }
    
    spdlog::info("Order cancellation requested: {}", order_id);
    return ErrorCode::Success;
}

std::optional<Order> OrderManager::get_order(const OrderId& order_id) const {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    auto it = orders_.find(order_id);
    return it != orders_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<Order> OrderManager::get_orders(const OrderFilter& filter) const {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    std::vector<Order> result;
    
    for (const auto& [id, order] : orders_) {
        if (filter.matches(order)) {
            result.push_back(order);
        }
    }
    
    return result;
}

std::vector<Order> OrderManager::get_active_orders() const {
    OrderFilter filter;
    filter.status = OrderStatus::New;
    auto new_orders = get_orders(filter);
    
    filter.status = OrderStatus::PartiallyFilled;
    auto partial_orders = get_orders(filter);
    
    new_orders.insert(new_orders.end(), partial_orders.begin(), partial_orders.end());
    return new_orders;
}

std::vector<Order> OrderManager::get_orders_by_account(const AccountId& account_id) const {
    OrderFilter filter;
    filter.account_id = account_id;
    return get_orders(filter);
}

std::vector<Order> OrderManager::get_orders_by_asset(const AssetId& asset_id) const {
    OrderFilter filter;
    filter.asset_id = asset_id;
    return get_orders(filter);
}

std::vector<Order> OrderManager::get_orders_by_strategy(const std::string& strategy_id) const {
    OrderFilter filter;
    filter.strategy_id = strategy_id;
    return get_orders(filter);
}

void OrderManager::update_order_status(const OrderId& order_id, OrderStatus status) {
    update_order_internal(order_id, [status](Order& order) {
        order.set_status(status);
    });
    
    spdlog::debug("Order {} status updated to {}", order_id, to_string(status));
}

void OrderManager::update_order_fill(const OrderId& order_id, Quantity quantity, Price price) {
    update_order_internal(order_id, [quantity, price](Order& order) {
        order.add_fill(quantity, price);
    });
    
    spdlog::info("Order {} filled: {} @ {}", order_id, quantity, price);
}

void OrderManager::update_order_error(const OrderId& order_id, const std::string& error) {
    update_order_internal(order_id, [&error](Order& order) {
        order.set_error(error);
    });
}

void OrderManager::set_broker_order_id(const OrderId& order_id, const std::string& broker_order_id) {
    update_order_internal(order_id, [&broker_order_id](Order& order) {
        order.set_broker_order_id(broker_order_id);
    });
}

void OrderManager::set_broker_interface(std::shared_ptr<BrokerInterface> broker) {
    broker_interface_ = broker;
    spdlog::info("Broker interface set for OrderManager");
}

std::shared_ptr<BrokerInterface> OrderManager::get_broker_interface() const {
    return broker_interface_;
}

void OrderManager::add_validator(OrderValidator validator) {
    validators_.push_back(validator);
}

void OrderManager::clear_validators() {
    validators_.clear();
}

void OrderManager::set_order_callback(OrderCallback callback) {
    order_callback_ = callback;
}

void OrderManager::clear_order_callback() {
    order_callback_ = nullptr;
}

size_t OrderManager::total_orders() const {
    std::shared_lock<std::shared_mutex> lock(orders_mutex_);
    return orders_.size();
}

size_t OrderManager::active_orders_count() const {
    return get_active_orders().size();
}

size_t OrderManager::filled_orders_count() const {
    OrderFilter filter;
    filter.status = OrderStatus::Filled;
    return get_orders(filter).size();
}

size_t OrderManager::cancelled_orders_count() const {
    OrderFilter filter;
    filter.status = OrderStatus::Cancelled;
    return get_orders(filter).size();
}

void OrderManager::start_event_handling() {
    if (event_handling_active_.load()) {
        return;
    }
    
    event_handling_active_.store(true);
    
    // Subscribe to order events
    auto order_handler = std::make_shared<FunctionalEventHandler<std::function<void(const EventPtr&)>>>(
        EventType::OrderUpdate, [this](const EventPtr& event) { handle_order_event(event); });
    event_handlers_.push_back(order_handler);
    get_event_dispatcher().subscribe(EventType::OrderUpdate, order_handler);
    
    // Subscribe to trade events
    auto trade_handler = std::make_shared<FunctionalEventHandler<std::function<void(const EventPtr&)>>>(
        EventType::TradeUpdate, [this](const EventPtr& event) { handle_trade_event(event); });
    event_handlers_.push_back(trade_handler);
    get_event_dispatcher().subscribe(EventType::TradeUpdate, trade_handler);
    
    spdlog::info("OrderManager event handling started");
}

void OrderManager::stop_event_handling() {
    if (!event_handling_active_.load()) {
        return;
    }
    
    event_handling_active_.store(false);
    
    // Unsubscribe from events
    for (auto& handler : event_handlers_) {
        get_event_dispatcher().unsubscribe(EventType::OrderUpdate, handler);
        get_event_dispatcher().unsubscribe(EventType::TradeUpdate, handler);
    }
    event_handlers_.clear();
    
    spdlog::info("OrderManager event handling stopped");
}

Result<void> OrderManager::validate_order_request(const OrderRequest& request) const {
    // Basic validation
    auto basic_result = ::RoboQuant::Broker::validate_order_request(request);
    if (is_error(basic_result)) {
        return get_error(basic_result);
    }
    
    // Custom validators
    for (const auto& validator : validators_) {
        auto result = validator(request);
        if (is_error(result)) {
            return get_error(result);
        }
    }
    
    return ErrorCode::Success;
}

void OrderManager::add_order_internal(const Order& order) {
    {
        std::unique_lock<std::shared_mutex> lock(orders_mutex_);
        orders_[order.order_id()] = order;
    }
    
    // Update indices
    {
        std::unique_lock<std::shared_mutex> lock(indices_mutex_);
        orders_by_account_[order.account_id()].insert(order.order_id());
        orders_by_asset_[order.asset_id()].insert(order.order_id());
        if (!order.strategy_id().empty()) {
            orders_by_strategy_[order.strategy_id()].insert(order.order_id());
        }
        orders_by_status_[order.status()].insert(order.order_id());
    }
    
    notify_order_update(order);
}

void OrderManager::update_order_internal(const OrderId& order_id, std::function<void(Order&)> updater) {
    Order updated_order;
    bool found = false;
    
    {
        std::unique_lock<std::shared_mutex> lock(orders_mutex_);
        auto it = orders_.find(order_id);
        if (it != orders_.end()) {
            auto old_status = it->second.status();
            updater(it->second);
            updated_order = it->second;
            found = true;
            
            // Update status index if status changed
            if (old_status != it->second.status()) {
                std::unique_lock<std::shared_mutex> idx_lock(indices_mutex_);
                orders_by_status_[old_status].erase(order_id);
                orders_by_status_[it->second.status()].insert(order_id);
            }
        }
    }
    
    if (found) {
        notify_order_update(updated_order);
    }
}

void OrderManager::handle_order_event(const EventPtr& event) {
    if (auto order_event = event->as<OrderEvent>()) {
        update_order_status(order_event->order_id(), order_event->status());
        if (!order_event->message().empty()) {
            update_order_error(order_event->order_id(), order_event->message());
        }
    }
}

void OrderManager::handle_trade_event(const EventPtr& event) {
    if (auto trade_event = event->as<TradeEvent>()) {
        update_order_fill(trade_event->order_id(), 
                         trade_event->quantity(), 
                         trade_event->price());
    }
}

void OrderManager::notify_order_update(const Order& order) {
    if (order_callback_) {
        try {
            order_callback_(order);
        } catch (const std::exception& e) {
            spdlog::error("Exception in order callback: {}", e.what());
        }
    }
}

} // namespace RoboQuant::Broker
