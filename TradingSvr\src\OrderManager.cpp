/**
 * @file OrderManager.cpp
 * @brief Implementation of OrderManager and related classes
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/OrderManager.h"
#include <sstream>
#include <iomanip>
#include <random>
#include <thread>
#include <chrono>

namespace RoboQuant::Trading {

// Order Implementation
Order::Order(OrderId id, OrderRequest request) 
    : id_(std::move(id)), request_(std::move(request)), created_time_(std::chrono::system_clock::now()) {}

void Order::add_fill(const OrderFill& fill) {
    fills_.push_back(fill);
    filled_quantity_ += fill.quantity;
    total_commission_ += fill.commission;
    updated_time_ = std::chrono::system_clock::now();
    
    update_fill_statistics();
    update_status();
}

void Order::cancel(const std::string& reason) {
    cancel_reason_ = reason;
    status_ = OrderStatus::Cancelled;
    updated_time_ = std::chrono::system_clock::now();
}

void Order::reject(const std::string& reason) {
    reject_reason_ = reason;
    status_ = OrderStatus::Rejected;
    updated_time_ = std::chrono::system_clock::now();
}

void Order::update_status() {
    if (filled_quantity_ == 0) {
        status_ = OrderStatus::Pending;
    } else if (filled_quantity_ >= request_.quantity) {
        status_ = OrderStatus::Filled;
    } else {
        status_ = OrderStatus::PartiallyFilled;
    }
}

void Order::update_fill_statistics() {
    if (fills_.empty()) {
        average_fill_price_ = 0.0;
        return;
    }
    
    Amount total_value = 0.0;
    Quantity total_quantity = 0;
    
    for (const auto& fill : fills_) {
        total_value += static_cast<Amount>(fill.quantity) * fill.price;
        total_quantity += fill.quantity;
    }
    
    if (total_quantity > 0) {
        average_fill_price_ = total_value / static_cast<Amount>(total_quantity);
    }
}

std::string Order::to_json() const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(6);
    oss << "{"
        << "\"id\":\"" << id_ << "\","
        << "\"asset_id\":\"" << request_.asset_id << "\","
        << "\"side\":\"" << (request_.side == Side::Buy ? "Buy" : "Sell") << "\","
        << "\"quantity\":" << request_.quantity << ","
        << "\"filled_quantity\":" << filled_quantity_ << ","
        << "\"average_fill_price\":" << average_fill_price_ << ","
        << "\"status\":\"" << static_cast<int>(status_) << "\","
        << "\"total_commission\":" << total_commission_
        << "}";
    return oss.str();
}

// Basic Order Validator Implementation
OrderValidator::ValidationResult BasicOrderValidator::validate_order(const OrderRequest& request) const {
    ValidationResult result;
    
    // Check basic requirements
    if (request.asset_id.empty()) {
        result.error_message = "Asset ID cannot be empty";
        return result;
    }
    
    if (request.quantity <= 0) {
        result.error_message = "Quantity must be positive";
        return result;
    }
    
    if (request.type == OrderType::Limit && !request.price.has_value()) {
        result.error_message = "Limit orders must have a price";
        return result;
    }
    
    if (request.type == OrderType::Stop && !request.stop_price.has_value()) {
        result.error_message = "Stop orders must have a stop price";
        return result;
    }
    
    result.is_valid = true;
    return result;
}

// Risk Order Validator Implementation
OrderValidator::ValidationResult RiskOrderValidator::validate_order(const OrderRequest& request) const {
    ValidationResult result;
    
    // Check position value limit
    if (request.price.has_value()) {
        Amount position_value = static_cast<Amount>(request.quantity) * (*request.price);
        if (position_value > limits_.max_position_value) {
            result.error_message = "Order exceeds maximum position value limit";
            return result;
        }
    }
    
    result.is_valid = true;
    return result;
}

// Composite Order Validator Implementation
void CompositeOrderValidator::add_validator(UniquePtr<OrderValidator> validator) {
    validators_.push_back(std::move(validator));
}

OrderValidator::ValidationResult CompositeOrderValidator::validate_order(const OrderRequest& request) const {
    for (const auto& validator : validators_) {
        auto result = validator->validate_order(request);
        if (!result.is_valid) {
            return result;
        }
    }
    
    ValidationResult result;
    result.is_valid = true;
    return result;
}

// Order Manager Implementation
OrderManager::OrderManager(UniquePtr<OrderExecutor> executor, UniquePtr<OrderValidator> validator)
    : executor_(std::move(executor)), validator_(std::move(validator)) {}

OrderManager::~OrderManager() {
    stop();
}

std::future<std::optional<OrderId>> OrderManager::submit_order(const OrderRequest& request) {
    std::promise<std::optional<OrderId>> promise;
    auto future = promise.get_future();
    
    // Validate order if validator is available
    if (validator_) {
        auto validation_result = validator_->validate_order(request);
        if (!validation_result.is_valid) {
            promise.set_value(std::nullopt);
            return future;
        }
    }
    
    // Generate order ID and create order
    OrderId order_id = generate_order_id();
    Order order(order_id, request);
    
    // Store order
    {
        std::unique_lock lock(orders_mutex_);
        orders_[order_id] = order;
    }
    
    // Submit to executor
    if (executor_) {
        auto submit_future = executor_->submit_order(order);
        
        // Add to processing queue
        order_queue_.enqueue([this, order_id, submit_future = std::move(submit_future), promise = std::move(promise)]() mutable {
                try {
                    bool success = submit_future.get();
                    if (success) {
                        promise.set_value(order_id);
                        notify_order_event(orders_[order_id], "submitted");
                    } else {
                        update_order_status(order_id, OrderStatus::Rejected, "Execution failed");
                        promise.set_value(std::nullopt);
                    }
                } catch (const std::exception& e) {
                    update_order_status(order_id, OrderStatus::Rejected, e.what());
                    promise.set_value(std::nullopt);
                }
            });
        has_orders_.store(true);
    } else {
        promise.set_value(order_id);
    }
    
    return future;
}

std::future<bool> OrderManager::cancel_order(const OrderId& order_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    {
        std::shared_lock lock(orders_mutex_);
        auto it = orders_.find(order_id);
        if (it == orders_.end() || !it->second.is_active()) {
            promise.set_value(false);
            return future;
        }
    }
    
    if (executor_) {
        auto cancel_future = executor_->cancel_order(order_id);
        
        order_queue_.enqueue([this, order_id, cancel_future = std::move(cancel_future), promise = std::move(promise)]() mutable {
                try {
                    bool success = cancel_future.get();
                    if (success) {
                        update_order_status(order_id, OrderStatus::Cancelled, "User cancelled");
                    }
                    promise.set_value(success);
                } catch (const std::exception& e) {
                    promise.set_value(false);
                }
            });
        has_orders_.store(true);
    } else {
        update_order_status(order_id, OrderStatus::Cancelled, "User cancelled");
        promise.set_value(true);
    }
    
    return future;
}

std::future<bool> OrderManager::cancel_all_orders() {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::vector<OrderId> active_orders;
    {
        std::shared_lock lock(orders_mutex_);
        for (const auto& [id, order] : orders_) {
            if (order.is_active()) {
                active_orders.push_back(id);
            }
        }
    }
    
    std::vector<std::future<bool>> cancel_futures;
    for (const auto& order_id : active_orders) {
        cancel_futures.push_back(cancel_order(order_id));
    }
    
    // Wait for all cancellations
    bool all_success = true;
    for (auto& cancel_future : cancel_futures) {
        if (!cancel_future.get()) {
            all_success = false;
        }
    }
    
    promise.set_value(all_success);
    return future;
}

std::future<bool> OrderManager::cancel_orders_for_asset(const AssetId& asset_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::vector<OrderId> asset_orders;
    {
        std::shared_lock lock(orders_mutex_);
        for (const auto& [id, order] : orders_) {
            if (order.request().asset_id == asset_id && order.is_active()) {
                asset_orders.push_back(id);
            }
        }
    }
    
    std::vector<std::future<bool>> cancel_futures;
    for (const auto& order_id : asset_orders) {
        cancel_futures.push_back(cancel_order(order_id));
    }
    
    bool all_success = true;
    for (auto& cancel_future : cancel_futures) {
        if (!cancel_future.get()) {
            all_success = false;
        }
    }
    
    promise.set_value(all_success);
    return future;
}

std::optional<Order> OrderManager::get_order(const OrderId& order_id) const {
    std::shared_lock lock(orders_mutex_);
    auto it = orders_.find(order_id);
    return it != orders_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<Order> OrderManager::get_orders_for_asset(const AssetId& asset_id) const {
    std::shared_lock lock(orders_mutex_);
    std::vector<Order> result;
    
    for (const auto& [id, order] : orders_) {
        if (order.request().asset_id == asset_id) {
            result.push_back(order);
        }
    }
    
    return result;
}

std::vector<Order> OrderManager::get_active_orders() const {
    std::shared_lock lock(orders_mutex_);
    std::vector<Order> result;
    
    for (const auto& [id, order] : orders_) {
        if (order.is_active()) {
            result.push_back(order);
        }
    }
    
    return result;
}

std::vector<Order> OrderManager::get_all_orders() const {
    std::shared_lock lock(orders_mutex_);
    std::vector<Order> result;
    result.reserve(orders_.size());
    
    for (const auto& [id, order] : orders_) {
        result.push_back(order);
    }
    
    return result;
}

void OrderManager::update_order_fill(const OrderFill& fill) {
    std::unique_lock lock(orders_mutex_);
    auto it = orders_.find(fill.order_id);
    
    if (it != orders_.end()) {
        it->second.add_fill(fill);
        notify_order_event(it->second, "filled");
    }
}

void OrderManager::update_order_status(const OrderId& order_id, OrderStatus status, const std::string& reason) {
    std::unique_lock lock(orders_mutex_);
    auto it = orders_.find(order_id);
    
    if (it != orders_.end()) {
        if (status == OrderStatus::Cancelled) {
            it->second.cancel(reason);
        } else if (status == OrderStatus::Rejected) {
            it->second.reject(reason);
        }
        notify_order_event(it->second, "status_updated");
    }
}

void OrderManager::set_order_event_callback(OrderEventCallback callback) {
    std::lock_guard lock(callback_mutex_);
    order_event_callback_ = std::move(callback);
}

size_t OrderManager::total_order_count() const {
    std::shared_lock lock(orders_mutex_);
    return orders_.size();
}

size_t OrderManager::active_order_count() const {
    std::shared_lock lock(orders_mutex_);
    return std::count_if(orders_.begin(), orders_.end(),
                        [](const auto& pair) { return pair.second.is_active(); });
}

Amount OrderManager::total_commission_paid() const {
    std::shared_lock lock(orders_mutex_);
    Amount total = 0.0;
    
    for (const auto& [id, order] : orders_) {
        total += order.total_commission();
    }
    
    return total;
}

void OrderManager::start() {
    running_.store(true);
    worker_thread_ = std::thread(&OrderManager::process_order_queue, this);
}

void OrderManager::stop() {
    running_.store(false);
    queue_condition_.notify_all();
    
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
}

bool OrderManager::is_running() const noexcept {
    return running_.load();
}

void OrderManager::process_order_queue() {
    while (running_.load()) {
        std::function<void()> task;

        // Try to dequeue a task
        if (order_queue_.try_dequeue(task)) {
            try {
                task();
            } catch (const std::exception& e) {
                std::cerr << "Error processing order: " << e.what() << std::endl;
            }
        } else {
            // No task available, sleep briefly
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}

void OrderManager::notify_order_event(const Order& order, const std::string& event_type) {
    std::lock_guard lock(callback_mutex_);
    if (order_event_callback_) {
        order_event_callback_(order, event_type);
    }
}

OrderId OrderManager::generate_order_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint64_t> dis;
    
    uint64_t counter = order_counter_.fetch_add(1);
    uint64_t random_part = dis(gen);
    
    std::ostringstream oss;
    oss << "ORD_" << std::hex << random_part << "_" << counter;
    return oss.str();
}

// Simulation Order Executor Implementation
SimulationOrderExecutor::SimulationOrderExecutor(std::function<Price(const AssetId&)> price_provider)
    : price_provider_(std::move(price_provider)) {}

std::future<bool> SimulationOrderExecutor::submit_order(const Order& order) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    // Simulate order processing delay
    std::this_thread::sleep_for(fill_delay_);
    
    // Get current price
    Price current_price = price_provider_(order.request().asset_id);
    
    // Apply slippage
    Price fill_price = current_price;
    if (order.request().side == Side::Buy) {
        fill_price *= (1.0 + slippage_);
    } else {
        fill_price *= (1.0 - slippage_);
    }
    
    // Calculate commission
    Amount commission = static_cast<Amount>(order.request().quantity) * fill_price * commission_rate_;
    
    // Store pending order
    {
        std::lock_guard lock(orders_mutex_);
        pending_orders_[order.id()] = const_cast<Order&>(order);
    }
    
    promise.set_value(true);
    return future;
}

std::future<bool> SimulationOrderExecutor::cancel_order(const OrderId& order_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::lock_guard lock(orders_mutex_);
    auto it = pending_orders_.find(order_id);
    
    if (it != pending_orders_.end()) {
        pending_orders_.erase(it);
        promise.set_value(true);
    } else {
        promise.set_value(false);
    }
    
    return future;
}

std::future<bool> SimulationOrderExecutor::modify_order(const OrderId& order_id, const OrderRequest& new_request) {
    // Simulation doesn't support order modification
    std::promise<bool> promise;
    promise.set_value(false);
    return promise.get_future();
}

std::future<std::optional<OrderStatus>> SimulationOrderExecutor::get_order_status(const OrderId& order_id) {
    std::promise<std::optional<OrderStatus>> promise;
    auto future = promise.get_future();
    
    std::lock_guard lock(orders_mutex_);
    auto it = pending_orders_.find(order_id);
    
    if (it != pending_orders_.end()) {
        promise.set_value(it->second.status());
    } else {
        promise.set_value(std::nullopt);
    }
    
    return future;
}

std::future<std::vector<Order>> SimulationOrderExecutor::get_active_orders() {
    std::promise<std::vector<Order>> promise;
    auto future = promise.get_future();
    
    std::lock_guard lock(orders_mutex_);
    std::vector<Order> result;
    result.reserve(pending_orders_.size());
    
    for (const auto& [id, order] : pending_orders_) {
        if (order.is_active()) {
            result.push_back(order);
        }
    }
    
    promise.set_value(std::move(result));
    return future;
}

} // namespace RoboQuant::Trading
