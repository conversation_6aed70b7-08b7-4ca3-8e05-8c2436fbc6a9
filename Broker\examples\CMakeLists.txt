# Broker Examples CMakeLists.txt

# Basic example
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/basic_example.cpp)
    add_executable(broker_basic_example basic_example.cpp)
    target_link_libraries(broker_basic_example PRIVATE broker_core)
    target_include_directories(broker_basic_example PRIVATE ${CMAKE_SOURCE_DIR}/Broker/include)
    
    # Set output directory
    set_target_properties(broker_basic_example PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
    )
endif()

# CTP example
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/ctp_example.cpp)
    add_executable(broker_ctp_example ctp_example.cpp)
    target_link_libraries(broker_ctp_example PRIVATE broker_core)
    target_include_directories(broker_ctp_example PRIVATE ${CMAKE_SOURCE_DIR}/Broker/include)
    
    # Set output directory
    set_target_properties(broker_ctp_example PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
    )
endif()

message(STATUS "Broker examples configured")
