/**
 * @file RestApiServer.h
 * @brief REST API server for QuantServices
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <functional>
#include <unordered_map>
#include <vector>
#include <atomic>
#include <mutex>
#include <future>
#include <chrono>
#include <thread>

#include <boost/beast.hpp>
#include <boost/asio.hpp>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace QuantServices {

using Json = nlohmann::json;
namespace beast = boost::beast;
namespace http = beast::http;
namespace net = boost::asio;
using tcp = net::ip::tcp;

/**
 * @brief HTTP request type alias
 */
using HttpRequest = http::request<http::string_body>;

/**
 * @brief HTTP response type alias
 */
using HttpResponse = http::response<http::string_body>;

/**
 * @brief HTTP method enumeration
 */
enum class HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    OPTIONS
};

/**
 * @brief Convert HTTP method to string
 */
std::string to_string(HttpMethod method);

/**
 * @brief Parse HTTP method from string
 */
HttpMethod parse_http_method(const std::string& method);

/**
 * @brief HTTP status codes
 */
enum class HttpStatus {
    OK = 200,
    Created = 201,
    NoContent = 204,
    BadRequest = 400,
    Unauthorized = 401,
    Forbidden = 403,
    NotFound = 404,
    MethodNotAllowed = 405,
    InternalServerError = 500,
    NotImplemented = 501,
    ServiceUnavailable = 503
};

/**
 * @brief Request context structure
 */
struct RequestContext {
    HttpRequest request;
    std::string path;
    std::string query_string;
    std::unordered_map<std::string, std::string> path_params;
    std::unordered_map<std::string, std::string> query_params;
    std::unordered_map<std::string, std::string> headers;
    Json body_json;
    std::chrono::system_clock::time_point start_time;
    std::string client_ip;
    std::string request_id;
};

/**
 * @brief Response structure
 */
struct ApiResponse {
    HttpStatus status{HttpStatus::OK};
    Json body;
    std::unordered_map<std::string, std::string> headers;
    
    // Convenience constructors
    static ApiResponse success(const Json& data = Json::object());
    static ApiResponse error(HttpStatus status, const std::string& message, const std::string& details = "");
    static ApiResponse not_found(const std::string& resource = "Resource");
    static ApiResponse bad_request(const std::string& message);
    static ApiResponse internal_error(const std::string& message = "Internal server error");
};

/**
 * @brief Route handler function type
 */
using RouteHandler = std::function<ApiResponse(const RequestContext& context)>;

/**
 * @brief Middleware function type
 */
using Middleware = std::function<bool(RequestContext& context, ApiResponse& response)>;

/**
 * @brief Route information structure
 */
struct Route {
    HttpMethod method;
    std::string pattern;
    RouteHandler handler;
    std::vector<Middleware> middlewares;
    std::string description;
};

/**
 * @brief Server configuration
 */
struct ServerConfig {
    std::string host{"0.0.0.0"};
    uint16_t port{8080};
    size_t thread_count{4};
    size_t max_request_size{1024 * 1024}; // 1MB
    std::chrono::seconds request_timeout{30};
    bool enable_cors{true};
    std::vector<std::string> allowed_origins{"*"};
    std::string api_prefix{"/api/v1"};
    bool enable_logging{true};
    bool enable_metrics{true};
};

/**
 * @brief Main REST API server class
 */
class RestApiServer {
public:
    /**
     * @brief Constructor
     */
    explicit RestApiServer(const ServerConfig& config = ServerConfig{});
    
    /**
     * @brief Destructor
     */
    ~RestApiServer();
    
    // Non-copyable, non-movable
    RestApiServer(const RestApiServer&) = delete;
    RestApiServer& operator=(const RestApiServer&) = delete;
    RestApiServer(RestApiServer&&) = delete;
    RestApiServer& operator=(RestApiServer&&) = delete;
    
    /**
     * @brief Start the server
     */
    std::future<bool> start();
    
    /**
     * @brief Stop the server
     */
    std::future<void> stop();
    
    /**
     * @brief Check if server is running
     */
    bool is_running() const;
    
    /**
     * @brief Register route
     */
    void register_route(HttpMethod method, const std::string& pattern, 
                       RouteHandler handler, const std::string& description = "");
    
    /**
     * @brief Register middleware
     */
    void register_middleware(Middleware middleware);
    
    /**
     * @brief Register route with middlewares
     */
    void register_route_with_middlewares(HttpMethod method, const std::string& pattern,
                                        RouteHandler handler, 
                                        const std::vector<Middleware>& middlewares,
                                        const std::string& description = "");
    
    /**
     * @brief Get server configuration
     */
    const ServerConfig& get_config() const;
    
    /**
     * @brief Get server metrics
     */
    Json get_metrics() const;
    
    /**
     * @brief Get registered routes
     */
    std::vector<Route> get_routes() const;

private:
    // Configuration
    ServerConfig config_;
    
    // Network components
    net::io_context io_context_;
    std::unique_ptr<tcp::acceptor> acceptor_;
    std::vector<std::thread> worker_threads_;
    
    // Routing
    std::vector<Route> routes_;
    std::vector<Middleware> global_middlewares_;
    mutable std::shared_mutex routes_mutex_;
    
    // Status
    std::atomic<bool> running_{false};
    
    // Logging
    std::shared_ptr<spdlog::logger> logger_;
    
    // Metrics
    mutable std::mutex metrics_mutex_;
    struct {
        std::atomic<uint64_t> total_requests{0};
        std::atomic<uint64_t> successful_requests{0};
        std::atomic<uint64_t> failed_requests{0};
        std::atomic<uint64_t> bytes_sent{0};
        std::atomic<uint64_t> bytes_received{0};
        std::chrono::system_clock::time_point start_time;
    } metrics_;
    
    // Internal methods
    void run_worker();
    void handle_accept();
    void handle_request(tcp::socket socket);
    
    ApiResponse process_request(const HttpRequest& req, const std::string& client_ip);
    RequestContext create_request_context(const HttpRequest& req, const std::string& client_ip);
    HttpResponse create_http_response(const ApiResponse& api_response, const std::string& request_id);
    
    bool match_route(const std::string& method, const std::string& path,
                    const Route& route, std::unordered_map<std::string, std::string>& path_params);

    void apply_cors_headers(HttpResponse& response);
    void apply_cors_headers(ApiResponse& response);
    void update_metrics(const RequestContext& context, const ApiResponse& response);
    
    std::string generate_request_id();
    std::unordered_map<std::string, std::string> parse_query_string(const std::string& query);
};

/**
 * @brief Common middleware functions
 */
namespace Middleware {

/**
 * @brief CORS middleware
 */
Middleware cors(const std::vector<std::string>& allowed_origins = {"*"});

/**
 * @brief Request logging middleware
 */
Middleware request_logging();

/**
 * @brief Request size limit middleware
 */
Middleware request_size_limit(size_t max_size);

/**
 * @brief Authentication middleware (basic API key)
 */
Middleware api_key_auth(const std::string& api_key);

/**
 * @brief Rate limiting middleware
 */
Middleware rate_limit(size_t requests_per_minute);

/**
 * @brief Request timeout middleware
 */
Middleware request_timeout(std::chrono::seconds timeout);

} // namespace Middleware

} // namespace QuantServices
