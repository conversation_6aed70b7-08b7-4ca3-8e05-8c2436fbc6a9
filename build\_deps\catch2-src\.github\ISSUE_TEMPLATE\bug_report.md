---
name: Bug report
about: Create an issue that documents a bug
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Reproduction steps**
Steps to reproduce the bug.
<!-- Usually this means a small and self-contained piece of code that uses Catch and specifying compiler flags if relevant. -->


**Platform information:**
<!-- Fill in any extra information that might be important for your issue. -->
 - OS: **Windows NT**
 - Compiler+version: **GCC v2.9.5**
 - Catch version: **v1.2.3**


**Additional context**
Add any other context about the problem here.
