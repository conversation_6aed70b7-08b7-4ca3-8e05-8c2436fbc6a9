#include "orders/Order.h"
#include <spdlog/spdlog.h>
#include <random>
#include <sstream>
#include <iomanip>

namespace RoboQuant::Broker {

namespace {
    // Generate unique order ID
    std::string generate_unique_id() {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<uint64_t> dis;
        
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        std::ostringstream oss;
        oss << "ORD_" << timestamp << "_" << std::hex << dis(gen);
        return oss.str();
    }
}

// OrderRequest implementation
bool OrderRequest::is_valid() const {
    return !asset_id.empty() && 
           quantity > 0 && 
           !account_id.empty() &&
           (type != OrderType::Limit || price.has_value()) &&
           (type != OrderType::Stop || stop_price.has_value()) &&
           (type != OrderType::StopLimit || (price.has_value() && stop_price.has_value()));
}

std::string OrderRequest::validation_error() const {
    if (asset_id.empty()) return "Asset ID is required";
    if (quantity <= 0) return "Quantity must be positive";
    if (account_id.empty()) return "Account ID is required";
    if (type == OrderType::Limit && !price.has_value()) return "Limit orders require a price";
    if (type == OrderType::Stop && !stop_price.has_value()) return "Stop orders require a stop price";
    if (type == OrderType::StopLimit && (!price.has_value() || !stop_price.has_value())) {
        return "Stop-limit orders require both price and stop price";
    }
    return "";
}

nlohmann::json OrderRequest::to_json() const {
    nlohmann::json j;
    j["asset_id"] = asset_id;
    j["side"] = to_string(side);
    j["type"] = to_string(type);
    j["quantity"] = quantity;
    if (price) j["price"] = *price;
    if (stop_price) j["stop_price"] = *stop_price;
    j["time_in_force"] = to_string(time_in_force);
    j["position_effect"] = to_string(position_effect);
    j["account_id"] = account_id;
    j["strategy_id"] = strategy_id;
    j["metadata"] = metadata;
    return j;
}

OrderRequest OrderRequest::from_json(const nlohmann::json& j) {
    OrderRequest req;
    req.asset_id = j.at("asset_id").get<std::string>();
    req.side = parse_order_side(j.at("side").get<std::string>());
    req.type = parse_order_type(j.at("type").get<std::string>());
    req.quantity = j.at("quantity").get<Quantity>();
    
    if (j.contains("price")) req.price = j["price"].get<Price>();
    if (j.contains("stop_price")) req.stop_price = j["stop_price"].get<Price>();
    
    req.time_in_force = parse_time_in_force(j.value("time_in_force", "Day"));
    req.position_effect = parse_position_effect(j.value("position_effect", "Open"));
    req.account_id = j.at("account_id").get<std::string>();
    req.strategy_id = j.value("strategy_id", "");
    req.metadata = j.value("metadata", std::unordered_map<std::string, std::string>{});
    
    return req;
}

// Order implementation
Order::Order(const OrderRequest& request)
    : order_id_(generate_unique_id())
    , asset_id_(request.asset_id)
    , side_(request.side)
    , type_(request.type)
    , quantity_(request.quantity)
    , price_(request.price)
    , stop_price_(request.stop_price)
    , time_in_force_(request.time_in_force)
    , position_effect_(request.position_effect)
    , account_id_(request.account_id)
    , strategy_id_(request.strategy_id)
    , metadata_(request.metadata)
    , create_time_(std::chrono::system_clock::now())
    , update_time_(create_time_) {
}

Order::Order(OrderId order_id, const OrderRequest& request)
    : order_id_(std::move(order_id))
    , asset_id_(request.asset_id)
    , side_(request.side)
    , type_(request.type)
    , quantity_(request.quantity)
    , price_(request.price)
    , stop_price_(request.stop_price)
    , time_in_force_(request.time_in_force)
    , position_effect_(request.position_effect)
    , account_id_(request.account_id)
    , strategy_id_(request.strategy_id)
    , metadata_(request.metadata)
    , create_time_(std::chrono::system_clock::now())
    , update_time_(create_time_) {
}

void Order::set_status(OrderStatus status) {
    if (status_ != status) {
        status_ = status;
        update_timestamp();
        
        if (status == OrderStatus::Filled || status == OrderStatus::PartiallyFilled) {
            fill_time_ = update_time_;
        }
        
        spdlog::debug("Order {} status changed to {}", order_id_, to_string(status));
    }
}

void Order::set_error(const std::string& error) {
    last_error_ = error;
    update_timestamp();
    spdlog::warn("Order {} error: {}", order_id_, error);
}

void Order::add_fill(Quantity fill_quantity, Price fill_price) {
    if (fill_quantity <= 0) {
        spdlog::warn("Invalid fill quantity {} for order {}", fill_quantity, order_id_);
        return;
    }
    
    if (filled_quantity_ + fill_quantity > quantity_) {
        spdlog::warn("Fill quantity {} would exceed order quantity {} for order {}", 
                    fill_quantity, quantity_, order_id_);
        return;
    }
    
    fills_.emplace_back(fill_quantity, fill_price);
    filled_quantity_ += fill_quantity;
    calculate_average_fill_price();
    
    // Update status based on fill
    if (filled_quantity_ >= quantity_) {
        set_status(OrderStatus::Filled);
    } else {
        set_status(OrderStatus::PartiallyFilled);
    }
    
    spdlog::info("Order {} filled: {} @ {}, total filled: {}/{}", 
                order_id_, fill_quantity, fill_price, filled_quantity_, quantity_);
}

bool Order::is_active() const noexcept {
    return status_ == OrderStatus::New || 
           status_ == OrderStatus::PartiallyFilled ||
           status_ == OrderStatus::PendingNew ||
           status_ == OrderStatus::PendingCancel;
}

bool Order::is_terminal() const noexcept {
    return status_ == OrderStatus::Filled ||
           status_ == OrderStatus::Cancelled ||
           status_ == OrderStatus::Rejected ||
           status_ == OrderStatus::Expired;
}

bool Order::is_valid() const {
    return !order_id_.empty() && 
           !asset_id_.empty() && 
           quantity_ > 0 && 
           !account_id_.empty() &&
           (type_ != OrderType::Limit || price_.has_value()) &&
           (type_ != OrderType::Stop || stop_price_.has_value()) &&
           (type_ != OrderType::StopLimit || (price_.has_value() && stop_price_.has_value()));
}

std::string Order::validation_error() const {
    if (order_id_.empty()) return "Order ID is required";
    if (asset_id_.empty()) return "Asset ID is required";
    if (quantity_ <= 0) return "Quantity must be positive";
    if (account_id_.empty()) return "Account ID is required";
    if (type_ == OrderType::Limit && !price_.has_value()) return "Limit orders require a price";
    if (type_ == OrderType::Stop && !stop_price_.has_value()) return "Stop orders require a stop price";
    if (type_ == OrderType::StopLimit && (!price_.has_value() || !stop_price_.has_value())) {
        return "Stop-limit orders require both price and stop price";
    }
    return "";
}

void Order::calculate_average_fill_price() {
    if (fills_.empty() || filled_quantity_ == 0) {
        average_fill_price_ = 0.0;
        return;
    }
    
    Amount total_value = 0.0;
    for (const auto& fill : fills_) {
        total_value += fill.quantity * fill.price;
    }
    
    average_fill_price_ = total_value / filled_quantity_;
}

nlohmann::json Order::to_json() const {
    nlohmann::json j;
    j["order_id"] = order_id_;
    j["asset_id"] = asset_id_;
    j["side"] = to_string(side_);
    j["type"] = to_string(type_);
    j["quantity"] = quantity_;
    j["filled_quantity"] = filled_quantity_;
    if (price_) j["price"] = *price_;
    if (stop_price_) j["stop_price"] = *stop_price_;
    j["average_fill_price"] = average_fill_price_;
    j["status"] = to_string(status_);
    j["time_in_force"] = to_string(time_in_force_);
    j["position_effect"] = to_string(position_effect_);
    j["account_id"] = account_id_;
    j["strategy_id"] = strategy_id_;
    j["broker_order_id"] = broker_order_id_;
    
    j["create_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        create_time_.time_since_epoch()).count();
    j["update_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        update_time_.time_since_epoch()).count();
    if (fill_time_) {
        j["fill_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            fill_time_->time_since_epoch()).count();
    }
    
    j["last_error"] = last_error_;
    j["metadata"] = metadata_;
    
    // Include fills
    nlohmann::json fills_json = nlohmann::json::array();
    for (const auto& fill : fills_) {
        nlohmann::json fill_json;
        fill_json["quantity"] = fill.quantity;
        fill_json["price"] = fill.price;
        fill_json["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            fill.timestamp.time_since_epoch()).count();
        fills_json.push_back(fill_json);
    }
    j["fills"] = fills_json;
    
    return j;
}

Order Order::from_json(const nlohmann::json& j) {
    OrderRequest req;
    req.asset_id = j.at("asset_id").get<std::string>();
    req.side = parse_order_side(j.at("side").get<std::string>());
    req.type = parse_order_type(j.at("type").get<std::string>());
    req.quantity = j.at("quantity").get<Quantity>();
    if (j.contains("price")) req.price = j["price"].get<Price>();
    if (j.contains("stop_price")) req.stop_price = j["stop_price"].get<Price>();
    req.time_in_force = parse_time_in_force(j.at("time_in_force").get<std::string>());
    req.position_effect = parse_position_effect(j.at("position_effect").get<std::string>());
    req.account_id = j.at("account_id").get<std::string>();
    req.strategy_id = j.value("strategy_id", "");
    req.metadata = j.value("metadata", std::unordered_map<std::string, std::string>{});
    
    Order order(j.at("order_id").get<std::string>(), req);
    order.filled_quantity_ = j.value("filled_quantity", 0);
    order.average_fill_price_ = j.value("average_fill_price", 0.0);
    order.status_ = parse_order_status(j.at("status").get<std::string>());
    order.broker_order_id_ = j.value("broker_order_id", "");
    order.last_error_ = j.value("last_error", "");
    
    // Restore timestamps
    if (j.contains("create_time")) {
        auto ms = j["create_time"].get<int64_t>();
        order.create_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    if (j.contains("update_time")) {
        auto ms = j["update_time"].get<int64_t>();
        order.update_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    if (j.contains("fill_time")) {
        auto ms = j["fill_time"].get<int64_t>();
        order.fill_time_ = TimePoint(std::chrono::milliseconds(ms));
    }
    
    // Restore fills
    if (j.contains("fills")) {
        for (const auto& fill_json : j["fills"]) {
            auto quantity = fill_json.at("quantity").get<Quantity>();
            auto price = fill_json.at("price").get<Price>();
            auto timestamp_ms = fill_json.at("timestamp").get<int64_t>();
            auto timestamp = TimePoint(std::chrono::milliseconds(timestamp_ms));
            order.fills_.emplace_back(quantity, price, timestamp);
        }
    }
    
    return order;
}

bool Order::operator==(const Order& other) const noexcept {
    return order_id_ == other.order_id_;
}

// Utility functions
OrderId generate_order_id() {
    return generate_unique_id();
}

bool is_buy_order(const Order& order) noexcept {
    return order.side() == OrderSide::Buy;
}

bool is_sell_order(const Order& order) noexcept {
    return order.side() == OrderSide::Sell;
}

bool is_market_order(const Order& order) noexcept {
    return order.type() == OrderType::Market;
}

bool is_limit_order(const Order& order) noexcept {
    return order.type() == OrderType::Limit;
}

Result<void> validate_order_request(const OrderRequest& request) {
    if (!request.is_valid()) {
        return ErrorCode::InvalidParameter;
    }
    return ErrorCode::Success;
}

Result<void> validate_order(const Order& order) {
    if (!order.is_valid()) {
        return ErrorCode::InvalidParameter;
    }
    return ErrorCode::Success;
}

} // namespace RoboQuant::Broker
