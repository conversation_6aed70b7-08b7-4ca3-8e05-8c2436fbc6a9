#pragma once

#include <mutex>
#include <shared_mutex>
#include <atomic>
#include <condition_variable>
#include <thread>
#include <chrono>
#include <functional>
#include <memory>
#include <queue>
#include <vector>
#include <future>
#include <type_traits>

// Include concurrentqueue for lock-free operations
#include <concurrentqueue.h>

namespace RoboQuant::Broker::Utils {

// RAII lock guards with timeout support
template<typename Mutex>
class TimedLockGuard {
public:
    template<typename Rep, typename Period>
    TimedLockGuard(Mutex& mutex, const std::chrono::duration<Rep, Period>& timeout)
        : mutex_(mutex), locked_(false) {
        locked_ = mutex_.try_lock_for(timeout);
    }
    
    ~TimedLockGuard() {
        if (locked_) {
            mutex_.unlock();
        }
    }
    
    bool owns_lock() const noexcept { return locked_; }
    explicit operator bool() const noexcept { return locked_; }

private:
    Mutex& mutex_;
    bool locked_;
};

// Shared lock with timeout
template<typename SharedMutex>
class TimedSharedLockGuard {
public:
    template<typename Rep, typename Period>
    TimedSharedLockGuard(SharedMutex& mutex, const std::chrono::duration<Rep, Period>& timeout)
        : mutex_(mutex), locked_(false) {
        locked_ = mutex_.try_lock_shared_for(timeout);
    }
    
    ~TimedSharedLockGuard() {
        if (locked_) {
            mutex_.unlock_shared();
        }
    }
    
    bool owns_lock() const noexcept { return locked_; }
    explicit operator bool() const noexcept { return locked_; }

private:
    SharedMutex& mutex_;
    bool locked_;
};

// Thread-safe counter
class AtomicCounter {
public:
    AtomicCounter(int64_t initial_value = 0) : value_(initial_value) {}
    
    int64_t increment() { return value_.fetch_add(1) + 1; }
    int64_t decrement() { return value_.fetch_sub(1) - 1; }
    int64_t add(int64_t delta) { return value_.fetch_add(delta) + delta; }
    int64_t subtract(int64_t delta) { return value_.fetch_sub(delta) - delta; }
    
    int64_t get() const { return value_.load(); }
    void set(int64_t new_value) { value_.store(new_value); }
    
    bool compare_and_swap(int64_t expected, int64_t desired) {
        return value_.compare_exchange_strong(expected, desired);
    }

private:
    std::atomic<int64_t> value_;
};

// Thread-safe flag
class AtomicFlag {
public:
    AtomicFlag(bool initial_state = false) : flag_(initial_state) {}
    
    bool is_set() const { return flag_.load(); }
    void set() { flag_.store(true); }
    void clear() { flag_.store(false); }
    void toggle() { flag_.store(!flag_.load()); }
    
    bool test_and_set() { return flag_.exchange(true); }
    bool test_and_clear() { return flag_.exchange(false); }

private:
    std::atomic<bool> flag_;
};

// Lock-free queue wrapper
template<typename T>
class LockFreeQueue {
public:
    LockFreeQueue() = default;
    
    void enqueue(T&& item) {
        queue_.enqueue(std::move(item));
        size_.increment();
    }
    
    void enqueue(const T& item) {
        queue_.enqueue(item);
        size_.increment();
    }
    
    bool try_dequeue(T& item) {
        if (queue_.try_dequeue(item)) {
            size_.decrement();
            return true;
        }
        return false;
    }
    
    bool empty() const {
        return size_.get() == 0;
    }
    
    size_t size() const {
        return static_cast<size_t>(std::max(0L, size_.get()));
    }
    
    // Bulk operations
    template<typename It>
    size_t enqueue_bulk(It itemFirst, size_t count) {
        auto enqueued = queue_.enqueue_bulk(itemFirst, count);
        size_.add(static_cast<int64_t>(enqueued));
        return enqueued;
    }
    
    template<typename It>
    size_t try_dequeue_bulk(It itemFirst, size_t max) {
        auto dequeued = queue_.try_dequeue_bulk(itemFirst, max);
        size_.subtract(static_cast<int64_t>(dequeued));
        return dequeued;
    }

private:
    moodycamel::ConcurrentQueue<T> queue_;
    AtomicCounter size_;
};

// Thread-safe object pool
template<typename T>
class ObjectPool {
public:
    template<typename... Args>
    explicit ObjectPool(size_t initial_size, Args&&... args) {
        for (size_t i = 0; i < initial_size; ++i) {
            pool_.enqueue(std::make_unique<T>(std::forward<Args>(args)...));
        }
    }
    
    std::unique_ptr<T> acquire() {
        std::unique_ptr<T> obj;
        if (pool_.try_dequeue(obj)) {
            return obj;
        }
        return std::make_unique<T>();
    }
    
    void release(std::unique_ptr<T> obj) {
        if (obj) {
            pool_.enqueue(std::move(obj));
        }
    }
    
    size_t size() const {
        return pool_.size();
    }

private:
    LockFreeQueue<std::unique_ptr<T>> pool_;
};

// Read-Write lock with priority
class PriorityRWLock {
public:
    enum class Priority { Reader, Writer, Fair };
    
    explicit PriorityRWLock(Priority priority = Priority::Fair)
        : priority_(priority) {}
    
    void lock_shared() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (priority_ == Priority::Writer) {
            // Wait for writers to finish
            reader_cv_.wait(lock, [this] { return writers_ == 0 && waiting_writers_ == 0; });
        } else {
            // Wait for active writers only
            reader_cv_.wait(lock, [this] { return writers_ == 0; });
        }
        
        ++readers_;
    }
    
    void unlock_shared() {
        std::unique_lock<std::mutex> lock(mutex_);
        --readers_;
        if (readers_ == 0) {
            writer_cv_.notify_one();
        }
    }
    
    void lock() {
        std::unique_lock<std::mutex> lock(mutex_);
        ++waiting_writers_;
        
        writer_cv_.wait(lock, [this] { return readers_ == 0 && writers_ == 0; });
        
        --waiting_writers_;
        ++writers_;
    }
    
    void unlock() {
        std::unique_lock<std::mutex> lock(mutex_);
        --writers_;
        
        if (priority_ == Priority::Writer && waiting_writers_ > 0) {
            writer_cv_.notify_one();
        } else {
            reader_cv_.notify_all();
            writer_cv_.notify_one();
        }
    }

private:
    Priority priority_;
    std::mutex mutex_;
    std::condition_variable reader_cv_;
    std::condition_variable writer_cv_;
    int readers_{0};
    int writers_{0};
    int waiting_writers_{0};
};

// Spinlock for very short critical sections
class SpinLock {
public:
    void lock() {
        while (flag_.test_and_set(std::memory_order_acquire)) {
            // Busy wait with pause instruction
            std::this_thread::yield();
        }
    }
    
    bool try_lock() {
        return !flag_.test_and_set(std::memory_order_acquire);
    }
    
    void unlock() {
        flag_.clear(std::memory_order_release);
    }

private:
    std::atomic_flag flag_ = ATOMIC_FLAG_INIT;
};

// Thread pool for async operations
class ThreadPool {
public:
    explicit ThreadPool(size_t num_threads = std::thread::hardware_concurrency());
    ~ThreadPool();
    
    // Non-copyable, non-movable
    ThreadPool(const ThreadPool&) = delete;
    ThreadPool& operator=(const ThreadPool&) = delete;
    ThreadPool(ThreadPool&&) = delete;
    ThreadPool& operator=(ThreadPool&&) = delete;
    
    template<typename F, typename... Args>
    auto submit(F&& f, Args&&... args) -> std::future<std::invoke_result_t<F, Args...>> {
        using return_type = std::invoke_result_t<F, Args...>;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            if (stop_) {
                throw std::runtime_error("ThreadPool is stopped");
            }
            
            tasks_.emplace([task] { (*task)(); });
        }
        
        condition_.notify_one();
        return future;
    }
    
    size_t active_threads() const { return active_threads_.load(); }
    size_t pending_tasks() const;
    
    void wait_for_all();

private:
    void worker_thread();
    
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_{false};
    std::atomic<size_t> active_threads_{0};
};

// Barrier for thread synchronization
class Barrier {
public:
    explicit Barrier(size_t count) : count_(count), waiting_(0), generation_(0) {}
    
    void wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        auto gen = generation_;
        
        if (++waiting_ == count_) {
            // Last thread to arrive
            waiting_ = 0;
            ++generation_;
            cv_.notify_all();
        } else {
            // Wait for all threads
            cv_.wait(lock, [this, gen] { return gen != generation_; });
        }
    }

private:
    const size_t count_;
    size_t waiting_;
    size_t generation_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

// Latch for one-time synchronization
class Latch {
public:
    explicit Latch(size_t count) : count_(count) {}
    
    void count_down(size_t n = 1) {
        std::unique_lock<std::mutex> lock(mutex_);
        count_ = (n >= count_) ? 0 : count_ - n;
        if (count_ == 0) {
            cv_.notify_all();
        }
    }
    
    void wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        cv_.wait(lock, [this] { return count_ == 0; });
    }
    
    bool try_wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        return count_ == 0;
    }

private:
    size_t count_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

// RAII thread management
class ThreadManager {
public:
    ThreadManager() = default;
    ~ThreadManager() { join_all(); }
    
    // Non-copyable, non-movable
    ThreadManager(const ThreadManager&) = delete;
    ThreadManager& operator=(const ThreadManager&) = delete;
    ThreadManager(ThreadManager&&) = delete;
    ThreadManager& operator=(ThreadManager&&) = delete;
    
    template<typename F, typename... Args>
    void create_thread(F&& f, Args&&... args) {
        std::unique_lock<std::mutex> lock(mutex_);
        threads_.emplace_back(std::forward<F>(f), std::forward<Args>(args)...);
    }
    
    void join_all() {
        std::unique_lock<std::mutex> lock(mutex_);
        for (auto& thread : threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        threads_.clear();
    }
    
    size_t thread_count() const {
        std::unique_lock<std::mutex> lock(mutex_);
        return threads_.size();
    }

private:
    mutable std::mutex mutex_;
    std::vector<std::thread> threads_;
};

// Utility functions
template<typename Mutex, typename Rep, typename Period>
auto make_timed_lock(Mutex& mutex, const std::chrono::duration<Rep, Period>& timeout) {
    return TimedLockGuard<Mutex>(mutex, timeout);
}

template<typename SharedMutex, typename Rep, typename Period>
auto make_timed_shared_lock(SharedMutex& mutex, const std::chrono::duration<Rep, Period>& timeout) {
    return TimedSharedLockGuard<SharedMutex>(mutex, timeout);
}

// Thread-local storage helper
template<typename T>
class ThreadLocal {
public:
    template<typename... Args>
    ThreadLocal(Args&&... args) : factory_([args...] { return T(args...); }) {}
    
    T& get() {
        if (!ptr_) {
            ptr_ = std::make_unique<T>(factory_());
        }
        return *ptr_;
    }
    
    const T& get() const {
        return const_cast<ThreadLocal*>(this)->get();
    }
    
    T* operator->() { return &get(); }
    const T* operator->() const { return &get(); }
    
    T& operator*() { return get(); }
    const T& operator*() const { return get(); }

private:
    std::function<T()> factory_;
    thread_local static std::unique_ptr<T> ptr_;
};

template<typename T>
thread_local std::unique_ptr<T> ThreadLocal<T>::ptr_;

} // namespace RoboQuant::Broker::Utils
