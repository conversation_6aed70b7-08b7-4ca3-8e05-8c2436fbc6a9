#include "brokers/BrokerInterface.h"
#include "core/EventSystem.h"
#include <spdlog/spdlog.h>
#include <future>

namespace RoboQuant::Broker {

// BrokerCapabilities implementation
nlohmann::json BrokerCapabilities::to_json() const {
    nlohmann::json j;
    j["supports_market_orders"] = supports_market_orders;
    j["supports_limit_orders"] = supports_limit_orders;
    j["supports_stop_orders"] = supports_stop_orders;
    j["supports_stop_limit_orders"] = supports_stop_limit_orders;
    j["supports_fak_orders"] = supports_fak_orders;
    j["supports_fok_orders"] = supports_fok_orders;
    j["supports_order_modification"] = supports_order_modification;
    j["supports_partial_fills"] = supports_partial_fills;
    j["supports_position_tracking"] = supports_position_tracking;
    j["supports_real_time_data"] = supports_real_time_data;
    j["supports_historical_data"] = supports_historical_data;
    j["supports_multiple_accounts"] = supports_multiple_accounts;
    
    j["supported_asset_types"] = nlohmann::json::array();
    for (auto type : supported_asset_types) {
        j["supported_asset_types"].push_back(to_string(type));
    }
    
    j["supported_exchanges"] = supported_exchanges;
    return j;
}

BrokerCapabilities BrokerCapabilities::from_json(const nlohmann::json& j) {
    BrokerCapabilities caps;
    caps.supports_market_orders = j.value("supports_market_orders", true);
    caps.supports_limit_orders = j.value("supports_limit_orders", true);
    caps.supports_stop_orders = j.value("supports_stop_orders", false);
    caps.supports_stop_limit_orders = j.value("supports_stop_limit_orders", false);
    caps.supports_fak_orders = j.value("supports_fak_orders", false);
    caps.supports_fok_orders = j.value("supports_fok_orders", false);
    caps.supports_order_modification = j.value("supports_order_modification", false);
    caps.supports_partial_fills = j.value("supports_partial_fills", true);
    caps.supports_position_tracking = j.value("supports_position_tracking", true);
    caps.supports_real_time_data = j.value("supports_real_time_data", true);
    caps.supports_historical_data = j.value("supports_historical_data", false);
    caps.supports_multiple_accounts = j.value("supports_multiple_accounts", false);
    
    if (j.contains("supported_asset_types")) {
        for (const auto& type_str : j["supported_asset_types"]) {
            caps.supported_asset_types.push_back(parse_asset_type(type_str.get<std::string>()));
        }
    }
    
    caps.supported_exchanges = j.value("supported_exchanges", std::vector<std::string>{});
    return caps;
}

// BrokerConfig implementation
nlohmann::json BrokerConfig::to_json() const {
    nlohmann::json j;
    j["broker_type"] = to_string(broker_type);
    j["connection_info"] = {
        {"broker_type", to_string(connection_info.broker_type)},
        {"server_address", connection_info.server_address},
        {"port", connection_info.port},
        {"username", connection_info.username},
        {"password", connection_info.password},
        {"extra_params", connection_info.extra_params}
    };
    j["settings"] = settings;
    j["auto_reconnect"] = auto_reconnect;
    j["reconnect_interval_ms"] = reconnect_interval.count();
    j["heartbeat_interval_ms"] = heartbeat_interval.count();
    return j;
}

BrokerConfig BrokerConfig::from_json(const nlohmann::json& j) {
    BrokerConfig config;
    config.broker_type = parse_broker_type(j.at("broker_type").get<std::string>());
    
    if (j.contains("connection_info")) {
        const auto& conn = j["connection_info"];
        config.connection_info.broker_type = parse_broker_type(conn.value("broker_type", "Unknown"));
        config.connection_info.server_address = conn.value("server_address", "");
        config.connection_info.port = conn.value("port", 0);
        config.connection_info.username = conn.value("username", "");
        config.connection_info.password = conn.value("password", "");
        config.connection_info.extra_params = conn.value("extra_params", 
            std::unordered_map<std::string, std::string>{});
    }
    
    config.settings = j.value("settings", std::unordered_map<std::string, std::string>{});
    config.auto_reconnect = j.value("auto_reconnect", true);
    config.reconnect_interval = Duration(j.value("reconnect_interval_ms", 30000));
    config.heartbeat_interval = Duration(j.value("heartbeat_interval_ms", 60000));
    
    return config;
}

// BrokerRegistry implementation
BrokerRegistry& BrokerRegistry::instance() {
    static BrokerRegistry registry;
    return registry;
}

void BrokerRegistry::register_factory(BrokerFactoryPtr factory) {
    if (!factory) {
        spdlog::warn("Attempted to register null broker factory");
        return;
    }
    
    std::unique_lock<std::shared_mutex> lock(factories_mutex_);
    factories_[factory->get_broker_type()] = factory;
    
    spdlog::info("Registered broker factory: {} ({})", 
                factory->get_broker_name(), to_string(factory->get_broker_type()));
}

void BrokerRegistry::unregister_factory(BrokerType broker_type) {
    std::unique_lock<std::shared_mutex> lock(factories_mutex_);
    auto it = factories_.find(broker_type);
    if (it != factories_.end()) {
        spdlog::info("Unregistered broker factory: {}", to_string(broker_type));
        factories_.erase(it);
    }
}

BrokerInterfacePtr BrokerRegistry::create_broker(BrokerType broker_type) {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    auto it = factories_.find(broker_type);
    if (it != factories_.end()) {
        return it->second->create_broker();
    }
    
    spdlog::error("No factory registered for broker type: {}", to_string(broker_type));
    return nullptr;
}

BrokerInterfacePtr BrokerRegistry::create_broker(const BrokerConfig& config) {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    auto it = factories_.find(config.broker_type);
    if (it != factories_.end() && it->second->supports_config(config)) {
        return it->second->create_broker(config);
    }
    
    spdlog::error("No suitable factory found for broker config: {}", to_string(config.broker_type));
    return nullptr;
}

std::vector<BrokerType> BrokerRegistry::get_supported_broker_types() const {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    std::vector<BrokerType> types;
    for (const auto& [type, factory] : factories_) {
        types.push_back(type);
    }
    return types;
}

std::vector<std::string> BrokerRegistry::get_supported_broker_names() const {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    std::vector<std::string> names;
    for (const auto& [type, factory] : factories_) {
        names.push_back(factory->get_broker_name());
    }
    return names;
}

BrokerFactoryPtr BrokerRegistry::get_factory(BrokerType broker_type) const {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    auto it = factories_.find(broker_type);
    return it != factories_.end() ? it->second : nullptr;
}

bool BrokerRegistry::is_supported(BrokerType broker_type) const {
    std::shared_lock<std::shared_mutex> lock(factories_mutex_);
    return factories_.find(broker_type) != factories_.end();
}

// BaseBroker implementation
BaseBroker::BaseBroker(BrokerType type, const std::string& name)
    : broker_type_(type), broker_name_(name), last_heartbeat_(std::chrono::system_clock::now()) {
    spdlog::info("BaseBroker created: {} ({})", name, to_string(type));
}

Result<void> BaseBroker::configure(const BrokerConfig& config) {
    if (config.broker_type != broker_type_) {
        spdlog::error("Config broker type mismatch: expected {}, got {}", 
                     to_string(broker_type_), to_string(config.broker_type));
        return ErrorCode::InvalidParameter;
    }
    
    std::unique_lock<std::shared_mutex> lock(state_mutex_);
    config_ = config;
    
    spdlog::info("Broker configured: {}", broker_name_);
    return ErrorCode::Success;
}

std::future<Result<void>> BaseBroker::submit_order_async(const Order& order) {
    return std::async(std::launch::async, [this, order]() {
        return submit_order(order);
    });
}

std::future<Result<void>> BaseBroker::cancel_order_async(const Order& order) {
    return std::async(std::launch::async, [this, order]() {
        return cancel_order(order);
    });
}

std::future<Result<std::vector<Order>>> BaseBroker::query_orders_async(const AccountId& account_id) {
    return std::async(std::launch::async, [this, account_id]() {
        return query_orders(account_id);
    });
}

std::future<Result<std::vector<Account>>> BaseBroker::query_accounts_async() {
    return std::async(std::launch::async, [this]() {
        return query_accounts();
    });
}

void BaseBroker::set_connection_status(ConnectionStatus status) {
    ConnectionStatus old_status;
    {
        std::unique_lock<std::shared_mutex> lock(state_mutex_);
        old_status = connection_status_;
        connection_status_ = status;
    }
    
    if (old_status != status) {
        spdlog::info("Broker {} connection status changed: {} -> {}", 
                    broker_name_, to_string(old_status), to_string(status));
        
        if (connection_callback_) {
            connection_callback_(broker_type_, status, "");
        }
        
        // Publish connection event
        auto event = make_connection_event(broker_type_, status);
        publish_event(event);
    }
}

void BaseBroker::notify_order_update(const Order& order) {
    if (order_callback_) {
        order_callback_(order);
    }
    
    // Publish order event
    auto event = make_order_event(order.order_id(), order.status());
    publish_event(event);
}

void BaseBroker::notify_trade(const Trade& trade) {
    if (trade_callback_) {
        trade_callback_(trade);
    }
    
    // Note: Trade event creation would need Trade class implementation
    // auto event = make_trade_event(...);
    // publish_event(event);
}

void BaseBroker::notify_account_update(const Account& account) {
    if (account_callback_) {
        account_callback_(account);
    }
    
    // Note: Account event creation would need Account class implementation
    // auto event = make_account_event(...);
    // publish_event(event);
}

void BaseBroker::notify_position_update(const Position& position) {
    if (position_callback_) {
        position_callback_(position);
    }
}

void BaseBroker::notify_error(ErrorCode code, const std::string& message, const std::any& context) {
    spdlog::error("Broker {} error: {} - {}", broker_name_, to_string(code), message);
    
    if (error_callback_) {
        error_callback_(code, message, context);
    }
    
    // Publish error event
    auto event = make_error_event(code, message, context);
    publish_event(event);
}

void BaseBroker::update_heartbeat() {
    std::unique_lock<std::shared_mutex> lock(state_mutex_);
    last_heartbeat_ = std::chrono::system_clock::now();
}

// Utility functions
std::string broker_type_to_string(BrokerType type) {
    return to_string(type);
}

BrokerType string_to_broker_type(const std::string& str) {
    return parse_broker_type(str);
}

bool is_broker_available(BrokerType type) {
    return BrokerRegistry::instance().is_supported(type);
}

std::vector<BrokerType> get_available_brokers() {
    return BrokerRegistry::instance().get_supported_broker_types();
}

} // namespace RoboQuant::Broker
