#pragma once

#include <string>
#include <chrono>
#include <memory>
#include <optional>
#include <variant>
#include <vector>
#include <unordered_map>
#include <concepts>
#include <cstdint>

namespace RoboQuant::Broker {

// Forward declarations
class Order;
class Account;
class Position;
class Trade;
class BrokerInterface;

// Type aliases
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::milliseconds;
using AssetId = std::string;
using AccountId = std::string;
using OrderId = std::string;
using TradeId = std::string;
using BrokerId = std::string;
using PortfolioId = std::string;

// Numeric types with strong typing
using Price = double;
using Quantity = int64_t;
using Volume = uint64_t;
using Amount = double;
using Commission = double;

// Modern enumerations with explicit underlying types
enum class BrokerType : uint8_t {
    CTP = 0,
    IB = 1,
    FIX = 2,
    Simulation = 3,
    XTP = 4,
    Unknown = 255
};

enum class AssetType : uint8_t {
    Stock = 0,
    Future = 1,
    Option = 2,
    Bond = 3,
    Fund = 4,
    Currency = 5,
    Crypto = 6,
    Index = 7,
    Unknown = 255
};

enum class OrderSide : uint8_t {
    Buy = 0,
    Sell = 1
};

enum class OrderType : uint8_t {
    Market = 0,
    Limit = 1,
    Stop = 2,
    StopLimit = 3,
    FAK = 4,  // Fill And Kill
    FOK = 5,  // Fill Or Kill
    Unknown = 255
};

enum class OrderStatus : uint8_t {
    PendingNew = 0,
    New = 1,
    PartiallyFilled = 2,
    Filled = 3,
    PendingCancel = 4,
    Cancelled = 5,
    Rejected = 6,
    Expired = 7,
    Unknown = 255
};

enum class PositionEffect : uint8_t {
    Open = 0,
    Close = 1,
    CloseToday = 2,
    CloseYesterday = 3,
    ForceClose = 4,
    Unknown = 255
};

enum class TimeInForce : uint8_t {
    Day = 0,
    GTC = 1,  // Good Till Cancelled
    IOC = 2,  // Immediate Or Cancel
    FOK = 3,  // Fill Or Kill
    GTD = 4,  // Good Till Date
    Unknown = 255
};

enum class AccountType : uint8_t {
    Cash = 0,
    Margin = 1,
    Future = 2,
    Option = 3,
    Unknown = 255
};

enum class ConnectionStatus : uint8_t {
    Disconnected = 0,
    Connecting = 1,
    Connected = 2,
    Authenticated = 3,
    Error = 4
};

enum class EventType : uint8_t {
    OrderUpdate = 0,
    TradeUpdate = 1,
    AccountUpdate = 2,
    PositionUpdate = 3,
    ConnectionUpdate = 4,
    ErrorEvent = 5,
    MarketDataUpdate = 6,
    Unknown = 255
};

// Error codes
enum class ErrorCode : uint32_t {
    Success = 0,
    InvalidParameter = 1,
    OrderNotFound = 2,
    AccountNotFound = 3,
    InsufficientFunds = 4,
    ConnectionError = 5,
    AuthenticationError = 6,
    PermissionDenied = 7,
    OrderRejected = 8,
    BrokerError = 9,
    NetworkError = 10,
    TimeoutError = 11,
    InternalError = 12,
    Unknown = 0xFFFFFFFF
};

// Concepts for type safety
template<typename T>
concept Numeric = std::integral<T> || std::floating_point<T>;

template<typename T>
concept StringLike = std::convertible_to<T, std::string>;

template<typename T>
concept OrderLike = requires(T t) {
    { t.order_id() } -> std::convertible_to<OrderId>;
    { t.asset_id() } -> std::convertible_to<AssetId>;
    { t.side() } -> std::convertible_to<OrderSide>;
    { t.quantity() } -> std::convertible_to<Quantity>;
};

template<typename T>
concept AccountLike = requires(T t) {
    { t.account_id() } -> std::convertible_to<AccountId>;
    { t.broker_type() } -> std::convertible_to<BrokerType>;
    { t.balance() } -> std::convertible_to<Amount>;
};

// Utility structures
struct AssetInfo {
    AssetId asset_id;
    std::string symbol;
    AssetType type{AssetType::Unknown};
    std::string exchange;
    Price tick_size{0.01};
    Quantity lot_size{1};
    std::string currency{"CNY"};
    bool tradable{true};
    
    AssetInfo() = default;
    AssetInfo(AssetId id, std::string sym, AssetType t = AssetType::Stock)
        : asset_id(std::move(id)), symbol(std::move(sym)), type(t) {}
};

struct ConnectionInfo {
    BrokerType broker_type{BrokerType::Unknown};
    std::string server_address;
    uint16_t port{0};
    std::string username;
    std::string password;
    std::unordered_map<std::string, std::string> extra_params;
    
    ConnectionInfo() = default;
    ConnectionInfo(BrokerType type, std::string addr, uint16_t p)
        : broker_type(type), server_address(std::move(addr)), port(p) {}
};

// Result type for error handling
template<typename T>
using Result = std::variant<T, ErrorCode>;

template<typename T>
constexpr bool is_success(const Result<T>& result) {
    return std::holds_alternative<T>(result);
}

template<typename T>
constexpr bool is_error(const Result<T>& result) {
    return std::holds_alternative<ErrorCode>(result);
}

template<typename T>
constexpr T& get_value(Result<T>& result) {
    return std::get<T>(result);
}

template<typename T>
constexpr const T& get_value(const Result<T>& result) {
    return std::get<T>(result);
}

template<typename T>
constexpr ErrorCode get_error(const Result<T>& result) {
    return std::get<ErrorCode>(result);
}

// String conversion utilities
std::string to_string(BrokerType type);
std::string to_string(AssetType type);
std::string to_string(OrderSide side);
std::string to_string(OrderType type);
std::string to_string(OrderStatus status);
std::string to_string(PositionEffect effect);
std::string to_string(TimeInForce tif);
std::string to_string(AccountType type);
std::string to_string(ConnectionStatus status);
std::string to_string(EventType type);
std::string to_string(ErrorCode code);

// Parsing utilities
BrokerType parse_broker_type(const std::string& str);
AssetType parse_asset_type(const std::string& str);
OrderSide parse_order_side(const std::string& str);
OrderType parse_order_type(const std::string& str);
OrderStatus parse_order_status(const std::string& str);
PositionEffect parse_position_effect(const std::string& str);
TimeInForce parse_time_in_force(const std::string& str);
AccountType parse_account_type(const std::string& str);
ConnectionStatus parse_connection_status(const std::string& str);
EventType parse_event_type(const std::string& str);
ErrorCode parse_error_code(const std::string& str);

} // namespace RoboQuant::Broker
