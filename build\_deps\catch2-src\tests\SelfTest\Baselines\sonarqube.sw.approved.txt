<?xml version="1.0" encoding="UTF-8"?>
<!-- filters='"*" ~[!nonportable] ~[!benchmark] ~[approvals]' rng-seed=1 -->
<testExecutions version="1"loose text artifact
>
  <file path="tests/<exe-name>/IntrospectiveTests/Clara.tests.cpp">
    <testCase name="Clara::Arg supports single-arg parse the way <PERSON><PERSON> does" duration="{duration}"/>
    <testCase name="Clara::Opt supports accept-many lambdas/Parsing fails on multiple options without accept_many" duration="{duration}"/>
    <testCase name="Clara::Opt supports accept-many lambdas/Parsing succeeds on multiple options with accept_many" duration="{duration}"/>
    <testCase name="is_unary_function" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/CmdLine.tests.cpp">
    <testCase name="Parsing sharding-related cli flags/shard-count" duration="{duration}"/>
    <testCase name="Parsing sharding-related cli flags/Negative shard count reports error" duration="{duration}"/>
    <testCase name="Parsing sharding-related cli flags/Zero shard count reports error" duration="{duration}"/>
    <testCase name="Parsing sharding-related cli flags/shard-index" duration="{duration}"/>
    <testCase name="Parsing sharding-related cli flags/Negative shard index reports error" duration="{duration}"/>
    <testCase name="Parsing sharding-related cli flags/Shard index 0 is accepted" duration="{duration}"/>
    <testCase name="Parsing warnings/NoAssertions" duration="{duration}"/>
    <testCase name="Parsing warnings/NoTests is no longer supported" duration="{duration}"/>
    <testCase name="Parsing warnings/Combining multiple warnings" duration="{duration}"/>
    <testCase name="Process can be configured on command line/empty args don't cause a crash" duration="{duration}"/>
    <testCase name="Process can be configured on command line/default - no arguments" duration="{duration}"/>
    <testCase name="Process can be configured on command line/test lists/Specify one test case using" duration="{duration}"/>
    <testCase name="Process can be configured on command line/test lists/Specify one test case exclusion using exclude:" duration="{duration}"/>
    <testCase name="Process can be configured on command line/test lists/Specify one test case exclusion using ~" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/-r/console" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/-r/xml" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/--reporter/junit" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/must match one of the available ones" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/With output file" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/With Windows-like absolute path as output file" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/Multiple reporters/All with output files" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/Multiple reporters/Mixed output files and default output" duration="{duration}"/>
    <testCase name="Process can be configured on command line/reporter/Multiple reporters/cannot have multiple reporters with default output" duration="{duration}"/>
    <testCase name="Process can be configured on command line/debugger/-b" duration="{duration}"/>
    <testCase name="Process can be configured on command line/debugger/--break" duration="{duration}"/>
    <testCase name="Process can be configured on command line/abort/-a aborts after first failure" duration="{duration}"/>
    <testCase name="Process can be configured on command line/abort/-x 2 aborts after two failures" duration="{duration}"/>
    <testCase name="Process can be configured on command line/abort/-x must be numeric" duration="{duration}"/>
    <testCase name="Process can be configured on command line/abort/wait-for-keypress/Accepted options" duration="{duration}"/>
    <testCase name="Process can be configured on command line/abort/wait-for-keypress/invalid options are reported" duration="{duration}"/>
    <testCase name="Process can be configured on command line/nothrow/-e" duration="{duration}"/>
    <testCase name="Process can be configured on command line/nothrow/--nothrow" duration="{duration}"/>
    <testCase name="Process can be configured on command line/output filename/-o filename" duration="{duration}"/>
    <testCase name="Process can be configured on command line/output filename/--out" duration="{duration}"/>
    <testCase name="Process can be configured on command line/combinations/Single character flags can be combined" duration="{duration}"/>
    <testCase name="Process can be configured on command line/use-colour/without option" duration="{duration}"/>
    <testCase name="Process can be configured on command line/use-colour/auto" duration="{duration}"/>
    <testCase name="Process can be configured on command line/use-colour/yes" duration="{duration}"/>
    <testCase name="Process can be configured on command line/use-colour/no" duration="{duration}"/>
    <testCase name="Process can be configured on command line/use-colour/error" duration="{duration}"/>
    <testCase name="Process can be configured on command line/Benchmark options/samples" duration="{duration}"/>
    <testCase name="Process can be configured on command line/Benchmark options/resamples" duration="{duration}"/>
    <testCase name="Process can be configured on command line/Benchmark options/confidence-interval" duration="{duration}"/>
    <testCase name="Process can be configured on command line/Benchmark options/no-analysis" duration="{duration}"/>
    <testCase name="Process can be configured on command line/Benchmark options/warmup-time" duration="{duration}"/>
    <testCase name="Test with special, characters &quot;in name" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/ColourImpl.tests.cpp">
    <testCase name="ColourGuard behaviour/ColourGuard is disengaged by default" duration="{duration}"/>
    <testCase name="ColourGuard behaviour/ColourGuard is engaged by op&lt;&lt;" duration="{duration}"/>
    <testCase name="ColourGuard behaviour/ColourGuard can be engaged explicitly" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Details.tests.cpp">
    <testCase name="CaseInsensitiveEqualsTo is case insensitive/Degenerate cases" duration="{duration}"/>
    <testCase name="CaseInsensitiveEqualsTo is case insensitive/Plain comparisons" duration="{duration}"/>
    <testCase name="CaseInsensitiveLess is case insensitive/Degenerate cases" duration="{duration}"/>
    <testCase name="CaseInsensitiveLess is case insensitive/Plain comparisons" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/FloatingPoint.tests.cpp">
    <testCase name="convertToBits" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/GeneratorsImpl.tests.cpp">
    <testCase name="Filter generator throws exception for empty generator" duration="{duration}"/>
    <testCase name="Generators internals/Single value" duration="{duration}"/>
    <testCase name="Generators internals/Preset values" duration="{duration}"/>
    <testCase name="Generators internals/Generator combinator" duration="{duration}"/>
    <testCase name="Generators internals/Explicitly typed generator sequence" duration="{duration}"/>
    <testCase name="Generators internals/Filter generator/Simple filtering" duration="{duration}"/>
    <testCase name="Generators internals/Filter generator/Filter out multiple elements at the start and end" duration="{duration}"/>
    <testCase name="Generators internals/Filter generator/Throws on construction if it can't get initial element" duration="{duration}"/>
    <testCase name="Generators internals/Take generator/Take less" duration="{duration}"/>
    <testCase name="Generators internals/Take generator/Take more" duration="{duration}"/>
    <testCase name="Generators internals/Map with explicit return type" duration="{duration}"/>
    <testCase name="Generators internals/Map with deduced return type" duration="{duration}"/>
    <testCase name="Generators internals/Repeat/Singular repeat" duration="{duration}"/>
    <testCase name="Generators internals/Repeat/Actual repeat" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive auto step/Integer" duration="{duration}"/>
    <testCase name="Generators internals/Range/Negative auto step/Integer" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Integer/Exact" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Integer/Slightly over end" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Integer/Slightly under end" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Floating Point/Exact" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Floating Point/Slightly over end" duration="{duration}"/>
    <testCase name="Generators internals/Range/Positive manual step/Floating Point/Slightly under end" duration="{duration}"/>
    <testCase name="Generators internals/Range/Negative manual step/Integer/Exact" duration="{duration}"/>
    <testCase name="Generators internals/Range/Negative manual step/Integer/Slightly over end" duration="{duration}"/>
    <testCase name="Generators internals/Range/Negative manual step/Integer/Slightly under end" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/InternalBenchmark.tests.cpp">
    <testCase name="analyse no analysis" duration="{duration}"/>
    <testCase name="benchmark function call/without chronometer" duration="{duration}"/>
    <testCase name="benchmark function call/with chronometer" duration="{duration}"/>
    <testCase name="classify_outliers/none" duration="{duration}"/>
    <testCase name="classify_outliers/low severe" duration="{duration}"/>
    <testCase name="classify_outliers/low mild" duration="{duration}"/>
    <testCase name="classify_outliers/high mild" duration="{duration}"/>
    <testCase name="classify_outliers/high severe" duration="{duration}"/>
    <testCase name="classify_outliers/mixed" duration="{duration}"/>
    <testCase name="erfc_inv" duration="{duration}"/>
    <testCase name="estimate_clock_resolution" duration="{duration}"/>
    <testCase name="mean" duration="{duration}"/>
    <testCase name="measure" duration="{duration}"/>
    <testCase name="normal_cdf" duration="{duration}"/>
    <testCase name="normal_quantile" duration="{duration}"/>
    <testCase name="resolution" duration="{duration}"/>
    <testCase name="run_for_at_least, chronometer" duration="{duration}"/>
    <testCase name="run_for_at_least, int" duration="{duration}"/>
    <testCase name="uniform samples" duration="{duration}"/>
    <testCase name="warmup" duration="{duration}"/>
    <testCase name="weighted_average_quantile" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Parse.tests.cpp">
    <testCase name="Parse uints/proper inputs" duration="{duration}"/>
    <testCase name="Parse uints/Bad inputs" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/PartTracker.tests.cpp">
    <testCase name="#1938 - GENERATE after a section/A" duration="{duration}"/>
    <testCase name="#1938 - GENERATE after a section/B" duration="{duration}"/>
    <testCase name="#1938 - Section followed by flat generate" duration="{duration}"/>
    <testCase name="#1938 - Section followed by flat generate/A" duration="{duration}"/>
    <testCase name="#1938 - flat generate" duration="{duration}"/>
    <testCase name="#1938 - mixed sections and generates" duration="{duration}"/>
    <testCase name="#1938 - mixed sections and generates/A" duration="{duration}"/>
    <testCase name="#1938 - mixed sections and generates/B" duration="{duration}"/>
    <testCase name="#1938 - nested generate" duration="{duration}"/>
    <testCase name="Tracker" duration="{duration}"/>
    <testCase name="Tracker/successfully close one section" duration="{duration}"/>
    <testCase name="Tracker/fail one section" duration="{duration}"/>
    <testCase name="Tracker/fail one section/re-enter after failed section" duration="{duration}"/>
    <testCase name="Tracker/fail one section/re-enter after failed section and find next section" duration="{duration}"/>
    <testCase name="Tracker/successfully close one section, then find another" duration="{duration}"/>
    <testCase name="Tracker/successfully close one section, then find another/Re-enter - skips S1 and enters S2" duration="{duration}"/>
    <testCase name="Tracker/successfully close one section, then find another/Re-enter - skips S1 and enters S2/Successfully close S2" duration="{duration}"/>
    <testCase name="Tracker/successfully close one section, then find another/Re-enter - skips S1 and enters S2/fail S2" duration="{duration}"/>
    <testCase name="Tracker/open a nested section" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/RandomNumberGeneration.tests.cpp">
    <testCase name="Comparison ops" duration="{duration}"/>
    <testCase name="Our PCG implementation provides expected results for known seeds/Default seeded" duration="{duration}"/>
    <testCase name="Our PCG implementation provides expected results for known seeds/Specific seed" duration="{duration}"/>
    <testCase name="Random seed generation accepts known methods" duration="{duration}"/>
    <testCase name="Random seed generation reports unknown methods" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Reporters.tests.cpp">
    <testCase name="Multireporter calls reporters and listeners in correct order" duration="{duration}"/>
    <testCase name="Multireporter updates ReporterPreferences properly" duration="{duration}"/>
    <testCase name="Multireporter updates ReporterPreferences properly/Adding listeners" duration="{duration}"/>
    <testCase name="Multireporter updates ReporterPreferences properly/Adding reporters" duration="{duration}"/>
    <testCase name="Registering reporter with '::' in name fails" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/Automake reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/Automake reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/Automake reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/compact reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/compact reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/compact reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/console reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/console reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/console reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/JUnit reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/JUnit reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/JUnit reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/SonarQube reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/SonarQube reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/SonarQube reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TAP reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TAP reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TAP reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TeamCity reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TeamCity reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/TeamCity reporter lists tests" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/XML reporter lists tags" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/XML reporter lists reporters" duration="{duration}"/>
    <testCase name="Reporter's write listings to provided stream/XML reporter lists tests" duration="{duration}"/>
    <testCase name="Reproducer for #2309 - a very long description past 80 chars (default console width) with a late colon : blablabla" duration="{duration}"/>
    <testCase name="The default listing implementation write to provided stream/Listing tags" duration="{duration}"/>
    <testCase name="The default listing implementation write to provided stream/Listing reporters" duration="{duration}"/>
    <testCase name="The default listing implementation write to provided stream/Listing tests" duration="{duration}"/>
    <testCase name="The default listing implementation write to provided stream/Listing listeners" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Stream.tests.cpp">
    <testCase name="Cout stream properly declares it writes to stdout" duration="{duration}"/>
    <testCase name="Empty stream name opens cout stream" duration="{duration}"/>
    <testCase name="makeStream recognizes %debug stream name" duration="{duration}"/>
    <testCase name="request an unknown %-starting stream fails" duration="{duration}"/>
    <testCase name="stdout and stderr streams have %-starting name" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/String.tests.cpp">
    <testCase name="StringRef/Empty string" duration="{duration}"/>
    <testCase name="StringRef/From string literal" duration="{duration}"/>
    <testCase name="StringRef/From sub-string" duration="{duration}"/>
    <testCase name="StringRef/Copy construction is shallow" duration="{duration}"/>
    <testCase name="StringRef/Copy assignment is shallow" duration="{duration}"/>
    <testCase name="StringRef/Substrings/zero-based substring" duration="{duration}"/>
    <testCase name="StringRef/Substrings/non-zero-based substring" duration="{duration}"/>
    <testCase name="StringRef/Substrings/Pointer values of full refs should match" duration="{duration}"/>
    <testCase name="StringRef/Substrings/Pointer values of substring refs should also match" duration="{duration}"/>
    <testCase name="StringRef/Substrings/Past the end substring" duration="{duration}"/>
    <testCase name="StringRef/Substrings/Substring off the end are trimmed" duration="{duration}"/>
    <testCase name="StringRef/Substrings/substring start after the end is empty" duration="{duration}"/>
    <testCase name="StringRef/Comparisons are deep" duration="{duration}"/>
    <testCase name="StringRef/from std::string/implicitly constructed" duration="{duration}"/>
    <testCase name="StringRef/from std::string/explicitly constructed" duration="{duration}"/>
    <testCase name="StringRef/from std::string/assigned" duration="{duration}"/>
    <testCase name="StringRef/to std::string/explicitly constructed" duration="{duration}"/>
    <testCase name="StringRef/to std::string/assigned" duration="{duration}"/>
    <testCase name="StringRef/std::string += StringRef" duration="{duration}"/>
    <testCase name="StringRef/StringRef + StringRef" duration="{duration}"/>
    <testCase name="StringRef at compilation time/Simple constructors" duration="{duration}"/>
    <testCase name="StringRef at compilation time/UDL construction" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/StringManip.tests.cpp">
    <testCase name="Trim strings" duration="{duration}"/>
    <testCase name="replaceInPlace/replace single char" duration="{duration}"/>
    <testCase name="replaceInPlace/replace two chars" duration="{duration}"/>
    <testCase name="replaceInPlace/replace first char" duration="{duration}"/>
    <testCase name="replaceInPlace/replace last char" duration="{duration}"/>
    <testCase name="replaceInPlace/replace all chars" duration="{duration}"/>
    <testCase name="replaceInPlace/replace no chars" duration="{duration}"/>
    <testCase name="replaceInPlace/escape '" duration="{duration}"/>
    <testCase name="splitString" duration="{duration}"/>
    <testCase name="startsWith" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Tag.tests.cpp">
    <testCase name="Tag alias can be registered against tag patterns/The same tag alias can only be registered once" duration="{duration}"/>
    <testCase name="Tag alias can be registered against tag patterns/Tag aliases must be of the form [@name]" duration="{duration}"/>
    <testCase name="Tags with spaces and non-alphanumerical characters are accepted" duration="{duration}"/>
    <testCase name="Test case with identical tags keeps just one" duration="{duration}"/>
    <testCase name="empty tags are not allowed" duration="{duration}"/>
    <testCase name="shortened hide tags are split apart" duration="{duration}"/>
    <testCase name="tags with dots in later positions are not parsed as hidden" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/TestCaseInfoHasher.tests.cpp">
    <testCase name="Hashers with different seed produce different hash with same test case" duration="{duration}"/>
    <testCase name="Hashers with same seed produce same hash" duration="{duration}"/>
    <testCase name="Hashing different test cases produces different result/Different test name" duration="{duration}"/>
    <testCase name="Hashing different test cases produces different result/Different classname" duration="{duration}"/>
    <testCase name="Hashing different test cases produces different result/Different tags" duration="{duration}"/>
    <testCase name="Hashing test case produces same hash across multiple calls" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/TestSpec.tests.cpp">
    <testCase name="#1905 -- test spec parser properly clears internal state between compound tests" duration="{duration}"/>
    <testCase name="#1912 -- test spec parser handles escaping/Various parentheses" duration="{duration}"/>
    <testCase name="#1912 -- test spec parser handles escaping/backslash in test name" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/TestSpecParser.tests.cpp">
    <testCase name="Parsed tags are matched case insensitive" duration="{duration}"/>
    <testCase name="Parsing tags with non-alphabetical characters is pass-through" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/ToString.tests.cpp">
    <testCase name="Directly creating an EnumInfo" duration="{duration}"/>
    <testCase name="Range type with sentinel" duration="{duration}"/>
    <testCase name="Stringifying char arrays with statically known sizes - char" duration="{duration}"/>
    <testCase name="Stringifying char arrays with statically known sizes - signed char" duration="{duration}"/>
    <testCase name="Stringifying char arrays with statically known sizes - unsigned char" duration="{duration}"/>
    <testCase name="parseEnums/No enums" duration="{duration}"/>
    <testCase name="parseEnums/One enum value" duration="{duration}"/>
    <testCase name="parseEnums/Multiple enum values" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/UniquePtr.tests.cpp">
    <testCase name="Upcasting special member functions/Move constructor" duration="{duration}"/>
    <testCase name="Upcasting special member functions/move assignment" duration="{duration}"/>
    <testCase name="make_unique reimplementation/From lvalue copies" duration="{duration}"/>
    <testCase name="make_unique reimplementation/From rvalue moves" duration="{duration}"/>
    <testCase name="make_unique reimplementation/Variadic constructor" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Default constructed unique_ptr is empty" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Take ownership of allocation" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Take ownership of allocation/Plain reset deallocates" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Take ownership of allocation/Reset replaces ownership" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Release releases ownership" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Move constructor" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/Move assignment" duration="{duration}"/>
    <testCase name="unique_ptr reimplementation: basic functionality/free swap" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/IntrospectiveTests/Xml.tests.cpp">
    <testCase name="XmlEncode/normal string" duration="{duration}"/>
    <testCase name="XmlEncode/empty string" duration="{duration}"/>
    <testCase name="XmlEncode/string with ampersand" duration="{duration}"/>
    <testCase name="XmlEncode/string with less-than" duration="{duration}"/>
    <testCase name="XmlEncode/string with greater-than" duration="{duration}"/>
    <testCase name="XmlEncode/string with quotes" duration="{duration}"/>
    <testCase name="XmlEncode/string with control char (1)" duration="{duration}"/>
    <testCase name="XmlEncode/string with control char (x7F)" duration="{duration}"/>
    <testCase name="XmlWriter writes boolean attributes as true/false" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Approx.tests.cpp">
    <testCase name="A comparison that uses literals instead of the normal constructor" duration="{duration}"/>
    <testCase name="Absolute margin" duration="{duration}"/>
    <testCase name="Approx setters validate their arguments" duration="{duration}"/>
    <testCase name="Approx with exactly-representable margin" duration="{duration}"/>
    <testCase name="Approximate PI" duration="{duration}"/>
    <testCase name="Approximate comparisons with different epsilons" duration="{duration}"/>
    <testCase name="Approximate comparisons with floats" duration="{duration}"/>
    <testCase name="Approximate comparisons with ints" duration="{duration}"/>
    <testCase name="Approximate comparisons with mixed numeric types" duration="{duration}"/>
    <testCase name="Comparison with explicitly convertible types" duration="{duration}"/>
    <testCase name="Default scale is invisible to comparison" duration="{duration}"/>
    <testCase name="Epsilon only applies to Approx's value" duration="{duration}"/>
    <testCase name="Greater-than inequalities with different epsilons" duration="{duration}"/>
    <testCase name="Less-than inequalities with different epsilons" duration="{duration}"/>
    <testCase name="Some simple comparisons between doubles" duration="{duration}"/>
    <testCase name="Use a custom approx" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/BDD.tests.cpp">
    <testCase name="Scenario: BDD tests requiring Fixtures to provide commonly-accessed data or methods/Given: No operations precede me" duration="{duration}"/>
    <testCase name="Scenario: BDD tests requiring Fixtures to provide commonly-accessed data or methods/Given: No operations precede me/When: We get the count/Then: Subsequently values are higher" duration="{duration}"/>
    <testCase name="Scenario: Do that thing with the thing/Given: This stuff exists/And given: And some assumption/When: I do this/Then: it should do this" duration="{duration}"/>
    <testCase name="Scenario: Do that thing with the thing/Given: This stuff exists/And given: And some assumption/When: I do this/Then: it should do this/And: do that" duration="{duration}"/>
    <testCase name="Scenario: This is a really long scenario name to see how the list command deals with wrapping/Given: A section name that is so long that it cannot fit in a single console width/When: The test headers are printed as part of the normal running of the scenario/Then: The, deliberately very long and overly verbose (you see what I did there?) section names must wrap, along with an indent" duration="{duration}"/>
    <testCase name="Scenario: Vector resizing affects size and capacity/Given: an empty vector" duration="{duration}"/>
    <testCase name="Scenario: Vector resizing affects size and capacity/Given: an empty vector/When: it is made larger/Then: the size and capacity go up" duration="{duration}"/>
    <testCase name="Scenario: Vector resizing affects size and capacity/Given: an empty vector/When: it is made larger/Then: the size and capacity go up/And when: it is made smaller again/Then: the size goes down but the capacity stays the same" duration="{duration}"/>
    <testCase name="Scenario: Vector resizing affects size and capacity/Given: an empty vector/When: we reserve more space/Then: The capacity is increased but the size remains the same" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Class.tests.cpp">
    <testCase name="A METHOD_AS_TEST_CASE based test run that fails" duration="{duration}">
      <failure message="REQUIRE(s == &quot;world&quot;)">
FAILED:
	REQUIRE( s == "world" )
with expansion:
	"hello" == "world"
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A METHOD_AS_TEST_CASE based test run that succeeds" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that fails - Template_Foo&lt;float>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>::m_a.size() == 1)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>::m_a.size() == 1 )
with expansion:
	0 == 1
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that fails - Template_Foo&lt;int>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>::m_a.size() == 1)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>::m_a.size() == 1 )
with expansion:
	0 == 1
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that fails - std::vector&lt;float>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>::m_a.size() == 1)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>::m_a.size() == 1 )
with expansion:
	0 == 1
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that fails - std::vector&lt;int>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>::m_a.size() == 1)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>::m_a.size() == 1 )
with expansion:
	0 == 1
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that succeeds - Template_Foo&lt;float>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that succeeds - Template_Foo&lt;int>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that succeeds - std::vector&lt;float>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD based test run that succeeds - std::vector&lt;int>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that fails - Template_Foo_2&lt;float, 6>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2 )
with expansion:
	6 &lt; 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that fails - Template_Foo_2&lt;int, 2>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2 )
with expansion:
	2 &lt; 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that fails - std::array&lt;float, 6>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2 )
with expansion:
	6 &lt; 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that fails - std::array&lt;int, 2>" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2)">
FAILED:
	REQUIRE( Template_Fixture_2&lt;TestType>{}.m_a.size() &lt; 2 )
with expansion:
	2 &lt; 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that succeeds - Template_Foo_2&lt;float,6>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that succeeds - Template_Foo_2&lt;int,2>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that succeeds - std::array&lt;float,6>" duration="{duration}"/>
    <testCase name="A TEMPLATE_PRODUCT_TEST_CASE_METHOD_SIG based test run that succeeds - std::array&lt;int,2>" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that fails - double" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture&lt;TestType>::m_a == 2)">
FAILED:
	REQUIRE( Template_Fixture&lt;TestType>::m_a == 2 )
with expansion:
	1.0 == 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that fails - float" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture&lt;TestType>::m_a == 2)">
FAILED:
	REQUIRE( Template_Fixture&lt;TestType>::m_a == 2 )
with expansion:
	1.0f == 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that fails - int" duration="{duration}">
      <failure message="REQUIRE(Template_Fixture&lt;TestType>::m_a == 2)">
FAILED:
	REQUIRE( Template_Fixture&lt;TestType>::m_a == 2 )
with expansion:
	1 == 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that succeeds - double" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that succeeds - float" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD based test run that succeeds - int" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that fails - 1" duration="{duration}">
      <failure message="REQUIRE(Nttp_Fixture&lt;V>::value == 0)">
FAILED:
	REQUIRE( Nttp_Fixture&lt;V>::value == 0 )
with expansion:
	1 == 0
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that fails - 3" duration="{duration}">
      <failure message="REQUIRE(Nttp_Fixture&lt;V>::value == 0)">
FAILED:
	REQUIRE( Nttp_Fixture&lt;V>::value == 0 )
with expansion:
	3 == 0
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that fails - 6" duration="{duration}">
      <failure message="REQUIRE(Nttp_Fixture&lt;V>::value == 0)">
FAILED:
	REQUIRE( Nttp_Fixture&lt;V>::value == 0 )
with expansion:
	6 == 0
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that succeeds - 1" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that succeeds - 3" duration="{duration}"/>
    <testCase name="A TEMPLATE_TEST_CASE_METHOD_SIG based test run that succeeds - 6" duration="{duration}"/>
    <testCase name="A TEST_CASE_METHOD based test run that fails" duration="{duration}">
      <failure message="REQUIRE(m_a == 2)">
FAILED:
	REQUIRE( m_a == 2 )
with expansion:
	1 == 2
at Class.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A TEST_CASE_METHOD based test run that succeeds" duration="{duration}"/>
    <testCase name="Template test case method with test types specified inside std::tuple - MyTypes - 0" duration="{duration}"/>
    <testCase name="Template test case method with test types specified inside std::tuple - MyTypes - 1" duration="{duration}"/>
    <testCase name="Template test case method with test types specified inside std::tuple - MyTypes - 2" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Compilation.tests.cpp">
    <testCase name="#1027: Bitfields can be captured" duration="{duration}"/>
    <testCase name="#1147" duration="{duration}"/>
    <testCase name="#1238" duration="{duration}"/>
    <testCase name="#1245" duration="{duration}"/>
    <testCase name="#1319: Sections can have description (even if it is not saved/SectionName" duration="{duration}"/>
    <testCase name="#1403" duration="{duration}"/>
    <testCase name="#1548" duration="{duration}"/>
    <testCase name="#809" duration="{duration}"/>
    <testCase name="#833" duration="{duration}"/>
    <testCase name="#872" duration="{duration}"/>
    <testCase name="Assertion macros support bit operators and bool conversions" duration="{duration}"/>
    <testCase name="Lambdas in assertions" duration="{duration}"/>
    <testCase name="Optionally static assertions" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Condition.tests.cpp">
    <testCase name="'Not' checks that should fail" duration="{duration}">
      <failure message="CHECK(false != false)">
FAILED:
	CHECK( false != false )
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(true != true)">
FAILED:
	CHECK( true != true )
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(!true)">
FAILED:
	CHECK( !true )
with expansion:
	false
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_FALSE(!(true))">
FAILED:
	CHECK_FALSE( true )
with expansion:
	!true
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(!trueValue)">
FAILED:
	CHECK( !trueValue )
with expansion:
	false
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_FALSE(!(trueValue))">
FAILED:
	CHECK_FALSE( trueValue )
with expansion:
	!true
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(!(1 == 1))">
FAILED:
	CHECK( !(1 == 1) )
with expansion:
	false
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_FALSE(!(1 == 1))">
FAILED:
	CHECK_FALSE( 1 == 1 )
at Condition.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="'Not' checks that should succeed" duration="{duration}"/>
    <testCase name="Comparisons between ints where one side is computed" duration="{duration}"/>
    <testCase name="Comparisons between unsigned ints and negative signed ints match c++ standard behaviour" duration="{duration}"/>
    <testCase name="Comparisons with int literals don't warn when mixing signed/ unsigned" duration="{duration}"/>
    <testCase name="Equality checks that should fail" duration="{duration}">
      <skipped message="CHECK(data.int_seven == 6)">
FAILED:
	CHECK( data.int_seven == 6 )
with expansion:
	7 == 6
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.int_seven == 8)">
FAILED:
	CHECK( data.int_seven == 8 )
with expansion:
	7 == 8
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.int_seven == 0)">
FAILED:
	CHECK( data.int_seven == 0 )
with expansion:
	7 == 0
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.float_nine_point_one == Approx( 9.11f ))">
FAILED:
	CHECK( data.float_nine_point_one == Approx( 9.11f ) )
with expansion:
	9.1f == Approx( 9.1099996567 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.float_nine_point_one == Approx( 9.0f ))">
FAILED:
	CHECK( data.float_nine_point_one == Approx( 9.0f ) )
with expansion:
	9.1f == Approx( 9.0 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.float_nine_point_one == Approx( 1 ))">
FAILED:
	CHECK( data.float_nine_point_one == Approx( 1 ) )
with expansion:
	9.1f == Approx( 1.0 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.float_nine_point_one == Approx( 0 ))">
FAILED:
	CHECK( data.float_nine_point_one == Approx( 0 ) )
with expansion:
	9.1f == Approx( 0.0 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.double_pi == Approx( 3.1415 ))">
FAILED:
	CHECK( data.double_pi == Approx( 3.1415 ) )
with expansion:
	3.1415926535 == Approx( 3.1415 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello == &quot;goodbye&quot;)">
FAILED:
	CHECK( data.str_hello == "goodbye" )
with expansion:
	"hello" == "goodbye"
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello == &quot;hell&quot;)">
FAILED:
	CHECK( data.str_hello == "hell" )
with expansion:
	"hello" == "hell"
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello == &quot;hello1&quot;)">
FAILED:
	CHECK( data.str_hello == "hello1" )
with expansion:
	"hello" == "hello1"
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello.size() == 6)">
FAILED:
	CHECK( data.str_hello.size() == 6 )
with expansion:
	5 == 6
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(x == Approx( 1.301 ))">
FAILED:
	CHECK( x == Approx( 1.301 ) )
with expansion:
	1.3 == Approx( 1.301 )
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Equality checks that should succeed" duration="{duration}"/>
    <testCase name="Inequality checks that should fail" duration="{duration}">
      <skipped message="CHECK(data.int_seven != 7)">
FAILED:
	CHECK( data.int_seven != 7 )
with expansion:
	7 != 7
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.float_nine_point_one != Approx( 9.1f ))">
FAILED:
	CHECK( data.float_nine_point_one != Approx( 9.1f ) )
with expansion:
	9.1f != Approx( 9.1000003815 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.double_pi != Approx( 3.1415926535 ))">
FAILED:
	CHECK( data.double_pi != Approx( 3.1415926535 ) )
with expansion:
	3.1415926535 != Approx( 3.1415926535 )
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello != &quot;hello&quot;)">
FAILED:
	CHECK( data.str_hello != "hello" )
with expansion:
	"hello" != "hello"
at Condition.tests.cpp:<line number>
      </skipped>
      <skipped message="CHECK(data.str_hello.size() != 5)">
FAILED:
	CHECK( data.str_hello.size() != 5 )
with expansion:
	5 != 5
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Inequality checks that should succeed" duration="{duration}"/>
    <testCase name="Mayfail test case with nested sections/1/A" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Mayfail test case with nested sections/2/A" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Mayfail test case with nested sections/1/B" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Mayfail test case with nested sections/2/B" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Condition.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Ordering comparison checks that should fail" duration="{duration}">
      <failure message="CHECK(data.int_seven > 7)">
FAILED:
	CHECK( data.int_seven > 7 )
with expansion:
	7 > 7
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven &lt; 7)">
FAILED:
	CHECK( data.int_seven &lt; 7 )
with expansion:
	7 &lt; 7
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven > 8)">
FAILED:
	CHECK( data.int_seven > 8 )
with expansion:
	7 > 8
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven &lt; 6)">
FAILED:
	CHECK( data.int_seven &lt; 6 )
with expansion:
	7 &lt; 6
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven &lt; 0)">
FAILED:
	CHECK( data.int_seven &lt; 0 )
with expansion:
	7 &lt; 0
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven &lt; -1)">
FAILED:
	CHECK( data.int_seven &lt; -1 )
with expansion:
	7 &lt; -1
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven >= 8)">
FAILED:
	CHECK( data.int_seven >= 8 )
with expansion:
	7 >= 8
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.int_seven &lt;= 6)">
FAILED:
	CHECK( data.int_seven &lt;= 6 )
with expansion:
	7 &lt;= 6
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.float_nine_point_one &lt; 9)">
FAILED:
	CHECK( data.float_nine_point_one &lt; 9 )
with expansion:
	9.1f &lt; 9
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.float_nine_point_one > 10)">
FAILED:
	CHECK( data.float_nine_point_one > 10 )
with expansion:
	9.1f > 10
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.float_nine_point_one > 9.2)">
FAILED:
	CHECK( data.float_nine_point_one > 9.2 )
with expansion:
	9.1f > 9.2
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello > &quot;hello&quot;)">
FAILED:
	CHECK( data.str_hello > "hello" )
with expansion:
	"hello" > "hello"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello &lt; &quot;hello&quot;)">
FAILED:
	CHECK( data.str_hello &lt; "hello" )
with expansion:
	"hello" &lt; "hello"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello > &quot;hellp&quot;)">
FAILED:
	CHECK( data.str_hello > "hellp" )
with expansion:
	"hello" > "hellp"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello > &quot;z&quot;)">
FAILED:
	CHECK( data.str_hello > "z" )
with expansion:
	"hello" > "z"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello &lt; &quot;hellm&quot;)">
FAILED:
	CHECK( data.str_hello &lt; "hellm" )
with expansion:
	"hello" &lt; "hellm"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello &lt; &quot;a&quot;)">
FAILED:
	CHECK( data.str_hello &lt; "a" )
with expansion:
	"hello" &lt; "a"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello >= &quot;z&quot;)">
FAILED:
	CHECK( data.str_hello >= "z" )
with expansion:
	"hello" >= "z"
at Condition.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(data.str_hello &lt;= &quot;a&quot;)">
FAILED:
	CHECK( data.str_hello &lt;= "a" )
with expansion:
	"hello" &lt;= "a"
at Condition.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Ordering comparison checks that should succeed" duration="{duration}"/>
    <testCase name="Pointers can be compared to null" duration="{duration}"/>
    <testCase name="comparisons between const int variables" duration="{duration}"/>
    <testCase name="comparisons between int variables" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Decomposition.tests.cpp">
    <testCase name="Reconstruction should be based on stringification: #914" duration="{duration}">
      <failure message="CHECK(truthy(false))">
FAILED:
	CHECK( truthy(false) )
with expansion:
	Hey, its truthy!
at Decomposition.tests.cpp:<line number>
      </failure>
    </testCase>
  </file>
  <file path="tests/<exe-name>/UsageTests/EnumToString.tests.cpp">
    <testCase name="Enums can quickly have stringification enabled using REGISTER_ENUM" duration="{duration}"/>
    <testCase name="Enums in namespaces can quickly have stringification enabled using REGISTER_ENUM" duration="{duration}"/>
    <testCase name="toString(enum class w/operator&lt;&lt;)" duration="{duration}"/>
    <testCase name="toString(enum class)" duration="{duration}"/>
    <testCase name="toString(enum w/operator&lt;&lt;)" duration="{duration}"/>
    <testCase name="toString(enum)" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Exception.tests.cpp">
    <testCase name="#748 - captures with unexpected exceptions/outside assertions" duration="{duration}">
      <skipped message="TEST_CASE()">
FAILED:
expected exception
answer := 42
at Exception.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="#748 - captures with unexpected exceptions/inside REQUIRE_NOTHROW" duration="{duration}">
      <skipped message="REQUIRE_NOTHROW(thisThrows())">
FAILED:
	REQUIRE_NOTHROW( thisThrows() )
expected exception
answer := 42
at Exception.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="#748 - captures with unexpected exceptions/inside REQUIRE_THROWS" duration="{duration}"/>
    <testCase name="An unchecked exception reports the line of the last assertion" duration="{duration}">
      <error message="({Unknown expression after the reported line})">
FAILED:
	{Unknown expression after the reported line}
unexpected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Custom exceptions can be translated when testing for nothrow" duration="{duration}">
      <error message="REQUIRE_NOTHROW(throwCustom())">
FAILED:
	REQUIRE_NOTHROW( throwCustom() )
custom exception - not std
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Custom exceptions can be translated when testing for throwing as something else" duration="{duration}">
      <error message="REQUIRE_THROWS_AS(throwCustom(), std::exception)">
FAILED:
	REQUIRE_THROWS_AS( throwCustom(), std::exception )
custom exception - not std
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Custom std-exceptions can be custom translated" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
custom std exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Exception messages can be tested for/exact match" duration="{duration}"/>
    <testCase name="Exception messages can be tested for/different case" duration="{duration}"/>
    <testCase name="Exception messages can be tested for/wildcarded" duration="{duration}"/>
    <testCase name="Expected exceptions that don't throw or unexpected exceptions fail the test" duration="{duration}">
      <error message="CHECK_THROWS_AS(thisThrows(), std::string)">
FAILED:
	CHECK_THROWS_AS( thisThrows(), std::string )
expected exception
at Exception.tests.cpp:<line number>
      </error>
      <failure message="CHECK_THROWS_AS(thisDoesntThrow(), std::domain_error)">
FAILED:
	CHECK_THROWS_AS( thisDoesntThrow(), std::domain_error )
at Exception.tests.cpp:<line number>
      </failure>
      <error message="CHECK_NOTHROW(thisThrows())">
FAILED:
	CHECK_NOTHROW( thisThrows() )
expected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Mismatching exception messages failing the test" duration="{duration}">
      <failure message="REQUIRE_THROWS_WITH(thisThrows(), &quot;should fail&quot;)">
FAILED:
	REQUIRE_THROWS_WITH( thisThrows(), "should fail" )
with expansion:
	"expected exception" equals: "should fail"
at Exception.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Non-std exceptions can be translated" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
custom exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Thrown string literals are translated" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
For some reason someone is throwing a string literal!
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Unexpected exceptions can be translated" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
3.14
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="When checked exceptions are thrown they can be expected or unexpected" duration="{duration}"/>
    <testCase name="When unchecked exceptions are thrown directly they are always failures" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
unexpected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="When unchecked exceptions are thrown during a CHECK the test should continue" duration="{duration}">
      <error message="CHECK(thisThrows() == 0)">
FAILED:
	CHECK( thisThrows() == 0 )
expected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="When unchecked exceptions are thrown during a REQUIRE the test should abort fail" duration="{duration}">
      <error message="REQUIRE(thisThrows() == 0)">
FAILED:
	REQUIRE( thisThrows() == 0 )
expected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="When unchecked exceptions are thrown from functions they are always failures" duration="{duration}">
      <error message="CHECK(thisThrows() == 0)">
FAILED:
	CHECK( thisThrows() == 0 )
expected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="When unchecked exceptions are thrown from sections they are always failures/section name" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
unexpected exception
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="thrown std::strings are translated" duration="{duration}">
      <error message="TEST_CASE()">
FAILED:
Why would you throw a std::string?
at Exception.tests.cpp:<line number>
      </error>
    </testCase>
  </file>
  <file path="tests/<exe-name>/UsageTests/Generators.tests.cpp">
    <testCase name="#1913 - GENERATE inside a for loop should not keep recreating the generator" duration="{duration}"/>
    <testCase name="#1913 - GENERATEs can share a line" duration="{duration}"/>
    <testCase name="#2615 - Throwing in constructor generator fails test case but does not abort" duration="{duration}">
      <skipped message="TEST_CASE()">
FAILED:
failure to init
at Generators.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="3x3x3 ints" duration="{duration}"/>
    <testCase name="Copy and then generate a range/from var and iterators" duration="{duration}"/>
    <testCase name="Copy and then generate a range/From a temporary container" duration="{duration}"/>
    <testCase name="Copy and then generate a range/Final validation" duration="{duration}"/>
    <testCase name="Generators -- adapters/Filtering by predicate/Basic usage" duration="{duration}"/>
    <testCase name="Generators -- adapters/Filtering by predicate/Throws if there are no matching values" duration="{duration}"/>
    <testCase name="Generators -- adapters/Shortening a range" duration="{duration}"/>
    <testCase name="Generators -- adapters/Transforming elements/Same type" duration="{duration}"/>
    <testCase name="Generators -- adapters/Transforming elements/Different type" duration="{duration}"/>
    <testCase name="Generators -- adapters/Transforming elements/Different deduced type" duration="{duration}"/>
    <testCase name="Generators -- adapters/Repeating a generator" duration="{duration}"/>
    <testCase name="Generators -- adapters/Chunking a generator into sized pieces/Number of elements in source is divisible by chunk size" duration="{duration}"/>
    <testCase name="Generators -- adapters/Chunking a generator into sized pieces/Number of elements in source is not divisible by chunk size" duration="{duration}"/>
    <testCase name="Generators -- adapters/Chunking a generator into sized pieces/Chunk size of zero" duration="{duration}"/>
    <testCase name="Generators -- adapters/Chunking a generator into sized pieces/Throws on too small generators" duration="{duration}"/>
    <testCase name="Generators -- simple/one" duration="{duration}"/>
    <testCase name="Generators -- simple/two" duration="{duration}"/>
    <testCase name="Nested generators and captured variables" duration="{duration}"/>
    <testCase name="strlen3" duration="{duration}"/>
    <testCase name="tables" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Matchers.tests.cpp">
    <testCase name="#2152 - ULP checks between differently signed values were wrong - double" duration="{duration}"/>
    <testCase name="#2152 - ULP checks between differently signed values were wrong - float" duration="{duration}"/>
    <testCase name="Arbitrary predicate matcher/Function pointer" duration="{duration}"/>
    <testCase name="Arbitrary predicate matcher/Lambdas + different type" duration="{duration}"/>
    <testCase name="Combining MatchAllOfGeneric does not nest" duration="{duration}"/>
    <testCase name="Combining MatchAnyOfGeneric does not nest" duration="{duration}"/>
    <testCase name="Combining MatchNotOfGeneric does not nest" duration="{duration}"/>
    <testCase name="Combining concrete matchers does not use templated matchers" duration="{duration}"/>
    <testCase name="Combining only templated matchers" duration="{duration}"/>
    <testCase name="Combining templated and concrete matchers" duration="{duration}"/>
    <testCase name="Combining templated matchers" duration="{duration}"/>
    <testCase name="Composed generic matchers shortcircuit/MatchAllOf" duration="{duration}"/>
    <testCase name="Composed generic matchers shortcircuit/MatchAnyOf" duration="{duration}"/>
    <testCase name="Composed matchers shortcircuit/MatchAllOf" duration="{duration}"/>
    <testCase name="Composed matchers shortcircuit/MatchAnyOf" duration="{duration}"/>
    <testCase name="Contains string matcher" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), ContainsSubstring( &quot;not there&quot;, Catch::CaseSensitive::No ))">
FAILED:
	CHECK_THAT( testStringForMatching(), ContainsSubstring( "not there", Catch::CaseSensitive::No ) )
with expansion:
	"this string contains 'abc' as a substring" contains: "not there" (case insensitive)
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), ContainsSubstring( &quot;STRING&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), ContainsSubstring( "STRING" ) )
with expansion:
	"this string contains 'abc' as a substring" contains: "STRING"
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="EndsWith string matcher" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), EndsWith( &quot;Substring&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), EndsWith( "Substring" ) )
with expansion:
	"this string contains 'abc' as a substring" ends with: "Substring"
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), EndsWith( &quot;this&quot;, Catch::CaseSensitive::No ))">
FAILED:
	CHECK_THAT( testStringForMatching(), EndsWith( "this", Catch::CaseSensitive::No ) )
with expansion:
	"this string contains 'abc' as a substring" ends with: "this" (case insensitive)
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Equals" duration="{duration}"/>
    <testCase name="Equals string matcher" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), Equals( &quot;this string contains 'ABC' as a substring&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), Equals( "this string contains 'ABC' as a substring" ) )
with expansion:
	"this string contains 'abc' as a substring" equals: "this string contains 'ABC' as a substring"
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), Equals( &quot;something else&quot;, Catch::CaseSensitive::No ))">
FAILED:
	CHECK_THAT( testStringForMatching(), Equals( "something else", Catch::CaseSensitive::No ) )
with expansion:
	"this string contains 'abc' as a substring" equals: "something else" (case insensitive)
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Exception matchers that fail/No exception" duration="{duration}">
      <failure message="CHECK_THROWS_MATCHES(doesNotThrow(), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	CHECK_THROWS_MATCHES( doesNotThrow(), SpecialException, ExceptionMatcher{ 1 } )
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="REQUIRE_THROWS_MATCHES(doesNotThrow(), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	REQUIRE_THROWS_MATCHES( doesNotThrow(), SpecialException, ExceptionMatcher{ 1 } )
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Exception matchers that fail/Type mismatch" duration="{duration}">
      <error message="CHECK_THROWS_MATCHES(throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	CHECK_THROWS_MATCHES( throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 } )
Unknown exception
at Matchers.tests.cpp:<line number>
      </error>
      <error message="REQUIRE_THROWS_MATCHES(throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	REQUIRE_THROWS_MATCHES( throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 } )
Unknown exception
at Matchers.tests.cpp:<line number>
      </error>
    </testCase>
    <testCase name="Exception matchers that fail/Contents are wrong" duration="{duration}">
      <failure message="CHECK_THROWS_MATCHES(throwsSpecialException( 3 ), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	CHECK_THROWS_MATCHES( throwsSpecialException( 3 ), SpecialException, ExceptionMatcher{ 1 } )
with expansion:
	SpecialException::what special exception has value of 1
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="REQUIRE_THROWS_MATCHES(throwsSpecialException( 4 ), SpecialException, ExceptionMatcher{ 1 })">
FAILED:
	REQUIRE_THROWS_MATCHES( throwsSpecialException( 4 ), SpecialException, ExceptionMatcher{ 1 } )
with expansion:
	SpecialException::what special exception has value of 1
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Exception matchers that succeed" duration="{duration}"/>
    <testCase name="Exception message can be matched" duration="{duration}"/>
    <testCase name="Exceptions matchers" duration="{duration}"/>
    <testCase name="Floating point matchers: double/Relative" duration="{duration}"/>
    <testCase name="Floating point matchers: double/Relative/Some subnormal values" duration="{duration}"/>
    <testCase name="Floating point matchers: double/Margin" duration="{duration}"/>
    <testCase name="Floating point matchers: double/ULPs" duration="{duration}"/>
    <testCase name="Floating point matchers: double/Composed" duration="{duration}"/>
    <testCase name="Floating point matchers: double/Constructor validation" duration="{duration}"/>
    <testCase name="Floating point matchers: double/IsNaN" duration="{duration}"/>
    <testCase name="Floating point matchers: float/Relative" duration="{duration}"/>
    <testCase name="Floating point matchers: float/Relative/Some subnormal values" duration="{duration}"/>
    <testCase name="Floating point matchers: float/Margin" duration="{duration}"/>
    <testCase name="Floating point matchers: float/ULPs" duration="{duration}"/>
    <testCase name="Floating point matchers: float/Composed" duration="{duration}"/>
    <testCase name="Floating point matchers: float/Constructor validation" duration="{duration}"/>
    <testCase name="Floating point matchers: float/IsNaN" duration="{duration}"/>
    <testCase name="Matchers can be (AllOf) composed with the &amp;&amp; operator" duration="{duration}"/>
    <testCase name="Matchers can be (AnyOf) composed with the || operator" duration="{duration}"/>
    <testCase name="Matchers can be composed with both &amp;&amp; and ||" duration="{duration}"/>
    <testCase name="Matchers can be composed with both &amp;&amp; and || - failing" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), ( ContainsSubstring( &quot;string&quot; ) || ContainsSubstring( &quot;different&quot; ) ) &amp;&amp; ContainsSubstring( &quot;random&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), ( ContainsSubstring( "string" ) || ContainsSubstring( "different" ) ) &amp;&amp; ContainsSubstring( "random" ) )
with expansion:
	"this string contains 'abc' as a substring" ( ( contains: "string" or contains: "different" ) and contains: "random" )
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Matchers can be negated (Not) with the ! operator" duration="{duration}"/>
    <testCase name="Matchers can be negated (Not) with the ! operator - failing" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), !ContainsSubstring( &quot;substring&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), !ContainsSubstring( "substring" ) )
with expansion:
	"this string contains 'abc' as a substring" not contains: "substring"
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Overloaded comma or address-of operators are not used" duration="{duration}"/>
    <testCase name="Predicate matcher can accept const char*" duration="{duration}"/>
    <testCase name="Regex string matcher" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), Matches( &quot;this STRING contains 'abc' as a substring&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), Matches( "this STRING contains 'abc' as a substring" ) )
with expansion:
	"this string contains 'abc' as a substring" matches "this STRING contains 'abc' as a substring" case sensitively
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), Matches( &quot;contains 'abc' as a substring&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), Matches( "contains 'abc' as a substring" ) )
with expansion:
	"this string contains 'abc' as a substring" matches "contains 'abc' as a substring" case sensitively
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), Matches( &quot;this string contains 'abc' as a&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), Matches( "this string contains 'abc' as a" ) )
with expansion:
	"this string contains 'abc' as a substring" matches "this string contains 'abc' as a" case sensitively
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Regression test #1" duration="{duration}"/>
    <testCase name="StartsWith string matcher" duration="{duration}">
      <failure message="CHECK_THAT(testStringForMatching(), StartsWith( &quot;This String&quot; ))">
FAILED:
	CHECK_THAT( testStringForMatching(), StartsWith( "This String" ) )
with expansion:
	"this string contains 'abc' as a substring" starts with: "This String"
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(testStringForMatching(), StartsWith( &quot;string&quot;, Catch::CaseSensitive::No ))">
FAILED:
	CHECK_THAT( testStringForMatching(), StartsWith( "string", Catch::CaseSensitive::No ) )
with expansion:
	"this string contains 'abc' as a substring" starts with: "string" (case insensitive)
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="String matchers" duration="{duration}"/>
    <testCase name="Vector Approx matcher/Empty vector is roughly equal to an empty vector" duration="{duration}"/>
    <testCase name="Vector Approx matcher/Vectors with elements/A vector is approx equal to itself" duration="{duration}"/>
    <testCase name="Vector Approx matcher/Vectors with elements/Different length" duration="{duration}"/>
    <testCase name="Vector Approx matcher/Vectors with elements/Same length, different elements" duration="{duration}"/>
    <testCase name="Vector Approx matcher -- failing/Empty and non empty vectors are not approx equal" duration="{duration}">
      <failure message="CHECK_THAT(empty, Approx( t1 ))">
FAILED:
	CHECK_THAT( empty, Approx( t1 ) )
with expansion:
	{  } is approx: { 1.0, 2.0 }
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Vector Approx matcher -- failing/Just different vectors" duration="{duration}">
      <failure message="CHECK_THAT(v1, Approx( v2 ))">
FAILED:
	CHECK_THAT( v1, Approx( v2 ) )
with expansion:
	{ 2.0, 4.0, 6.0 } is approx: { 1.0, 3.0, 5.0 }
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Vector matchers/Contains (element)" duration="{duration}"/>
    <testCase name="Vector matchers/Contains (vector)" duration="{duration}"/>
    <testCase name="Vector matchers/Contains (element), composed" duration="{duration}"/>
    <testCase name="Vector matchers/Equals" duration="{duration}"/>
    <testCase name="Vector matchers/UnorderedEquals" duration="{duration}"/>
    <testCase name="Vector matchers that fail/Contains (element)" duration="{duration}">
      <failure message="CHECK_THAT(v, VectorContains( -1 ))">
FAILED:
	CHECK_THAT( v, VectorContains( -1 ) )
with expansion:
	{ 1, 2, 3 } Contains: -1
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(empty, VectorContains( 1 ))">
FAILED:
	CHECK_THAT( empty, VectorContains( 1 ) )
with expansion:
	{  } Contains: 1
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Vector matchers that fail/Contains (vector)" duration="{duration}">
      <failure message="CHECK_THAT(empty, Contains( v ))">
FAILED:
	CHECK_THAT( empty, Contains( v ) )
with expansion:
	{  } Contains: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(v, Contains( v2 ))">
FAILED:
	CHECK_THAT( v, Contains( v2 ) )
with expansion:
	{ 1, 2, 3 } Contains: { 1, 2, 4 }
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Vector matchers that fail/Equals" duration="{duration}">
      <failure message="CHECK_THAT(v, Equals( v2 ))">
FAILED:
	CHECK_THAT( v, Equals( v2 ) )
with expansion:
	{ 1, 2, 3 } Equals: { 1, 2 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(v2, Equals( v ))">
FAILED:
	CHECK_THAT( v2, Equals( v ) )
with expansion:
	{ 1, 2 } Equals: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(empty, Equals( v ))">
FAILED:
	CHECK_THAT( empty, Equals( v ) )
with expansion:
	{  } Equals: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(v, Equals( empty ))">
FAILED:
	CHECK_THAT( v, Equals( empty ) )
with expansion:
	{ 1, 2, 3 } Equals: {  }
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Vector matchers that fail/UnorderedEquals" duration="{duration}">
      <failure message="CHECK_THAT(v, UnorderedEquals( empty ))">
FAILED:
	CHECK_THAT( v, UnorderedEquals( empty ) )
with expansion:
	{ 1, 2, 3 } UnorderedEquals: {  }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(empty, UnorderedEquals( v ))">
FAILED:
	CHECK_THAT( empty, UnorderedEquals( v ) )
with expansion:
	{  } UnorderedEquals: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(permuted, UnorderedEquals( v ))">
FAILED:
	CHECK_THAT( permuted, UnorderedEquals( v ) )
with expansion:
	{ 1, 3 } UnorderedEquals: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
      <failure message="CHECK_THAT(permuted, UnorderedEquals( v ))">
FAILED:
	CHECK_THAT( permuted, UnorderedEquals( v ) )
with expansion:
	{ 3, 1 } UnorderedEquals: { 1, 2, 3 }
at Matchers.tests.cpp:<line number>
      </failure>
    </testCase>
  </file>
  <file path="tests/<exe-name>/UsageTests/MatchersRanges.tests.cpp">
    <testCase name="Basic use of the Contains range matcher/Different argument ranges, same element type, default comparison" duration="{duration}"/>
    <testCase name="Basic use of the Contains range matcher/Different argument ranges, same element type, custom comparison" duration="{duration}"/>
    <testCase name="Basic use of the Contains range matcher/Different element type, custom comparisons" duration="{duration}"/>
    <testCase name="Basic use of the Contains range matcher/Can handle type that requires ADL-found free function begin and end" duration="{duration}"/>
    <testCase name="Basic use of the Contains range matcher/Initialization with move only types" duration="{duration}"/>
    <testCase name="Basic use of the Contains range matcher/Matching using matcher" duration="{duration}"/>
    <testCase name="Basic use of the Empty range matcher/Simple, std-provided containers" duration="{duration}"/>
    <testCase name="Basic use of the Empty range matcher/Type with empty" duration="{duration}"/>
    <testCase name="Basic use of the Empty range matcher/Type requires ADL found empty free function" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Container conversions/Two equal containers of different container types" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Container conversions/Two equal containers of different container types (differ in array N)" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Container conversions/Two equal containers of different container types and value types" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Container conversions/Two equal containers, one random access, one not" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Value type/Two equal containers of different value types" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Value type/Two non-equal containers of different value types" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Ranges with begin that needs ADL" duration="{duration}"/>
    <testCase name="Type conversions of RangeEquals and similar/Custom predicate/Two equal non-empty containers (close enough)" duration="{duration}"/>
    <testCase name="Usage of AllMatch range matcher/Basic usage" duration="{duration}"/>
    <testCase name="Usage of AllMatch range matcher/Type requires ADL found begin and end" duration="{duration}"/>
    <testCase name="Usage of AllMatch range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of AllMatch range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Basic usage/All true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Basic usage/Empty evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Basic usage/One false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Basic usage/All false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Contained type is convertible to bool/All true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Contained type is convertible to bool/One false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Contained type is convertible to bool/All false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of AllTrue range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of AnyMatch range matcher/Basic usage" duration="{duration}"/>
    <testCase name="Usage of AnyMatch range matcher/Type requires ADL found begin and end" duration="{duration}"/>
    <testCase name="Usage of AnyMatch range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of AnyMatch range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Basic usage/All true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Basic usage/Empty evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Basic usage/One true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Basic usage/All false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Contained type is convertible to bool/All true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Contained type is convertible to bool/One true evaluates to true" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Contained type is convertible to bool/All false evaluates to false" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of AnyTrue range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of NoneMatch range matcher/Basic usage" duration="{duration}"/>
    <testCase name="Usage of NoneMatch range matcher/Type requires ADL found begin and end" duration="{duration}"/>
    <testCase name="Usage of NoneMatch range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of NoneMatch range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Basic usage/All true evaluates to false" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Basic usage/Empty evaluates to true" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Basic usage/One true evaluates to false" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Basic usage/All false evaluates to true" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Contained type is convertible to bool/All true evaluates to false" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Contained type is convertible to bool/One true evaluates to false" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Contained type is convertible to bool/All false evaluates to true" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Shortcircuiting/All are read" duration="{duration}"/>
    <testCase name="Usage of NoneTrue range matcher/Shortcircuiting/Short-circuited" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Empty container matches empty container" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Empty container does not match non-empty container" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Two equal 1-length non-empty containers" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Two equal-sized, equal, non-empty containers" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Two equal-sized, non-equal, non-empty containers" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Basic usage/Two non-equal-sized, non-empty containers (with same first elements)" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Custom predicate/Two equal non-empty containers (close enough)" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Custom predicate/Two non-equal non-empty containers (close enough)" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Ranges that need ADL begin/end" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Check short-circuiting behaviour/Check short-circuits on failure" duration="{duration}"/>
    <testCase name="Usage of RangeEquals range matcher/Check short-circuiting behaviour/All elements are checked on success" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Empty container matches empty container" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Empty container does not match non-empty container" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Two equal 1-length non-empty containers" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Two equal-sized, equal, non-empty containers" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Two equal-sized, non-equal, non-empty containers" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Basic usage/Two non-equal-sized, non-empty containers" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Custom predicate/Two equal non-empty containers (close enough)" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Custom predicate/Two non-equal non-empty containers (close enough)" duration="{duration}"/>
    <testCase name="Usage of UnorderedRangeEquals range matcher/Ranges that need ADL begin/end" duration="{duration}"/>
    <testCase name="Usage of the SizeIs range matcher/Some with stdlib containers" duration="{duration}"/>
    <testCase name="Usage of the SizeIs range matcher/Type requires ADL found size free function" duration="{duration}"/>
    <testCase name="Usage of the SizeIs range matcher/Type has size member" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Message.tests.cpp">
    <testCase name="#1455 - INFO and WARN can start with a linebreak" duration="{duration}"/>
    <testCase name="CAPTURE can deal with complex expressions" duration="{duration}"/>
    <testCase name="CAPTURE can deal with complex expressions involving commas" duration="{duration}"/>
    <testCase name="CAPTURE parses string and character constants" duration="{duration}"/>
    <testCase name="FAIL aborts the test" duration="{duration}">
      <failure message="FAIL()">
FAILED:
This is a failure
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="FAIL does not require an argument" duration="{duration}">
      <failure message="FAIL()">
FAILED:
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="FAIL_CHECK does not abort the test" duration="{duration}">
      <failure message="FAIL_CHECK()">
FAILED:
This is a failure
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="INFO and WARN do not abort tests" duration="{duration}"/>
    <testCase name="INFO gets logged on failure" duration="{duration}">
      <failure message="REQUIRE(a == 1)">
FAILED:
	REQUIRE( a == 1 )
with expansion:
	2 == 1
this message should be logged
so should this
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="INFO gets logged on failure, even if captured before successful assertions" duration="{duration}">
      <failure message="CHECK(a == 1)">
FAILED:
	CHECK( a == 1 )
with expansion:
	2 == 1
this message may be logged later
this message should be logged
at Message.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(a == 0)">
FAILED:
	CHECK( a == 0 )
with expansion:
	2 == 0
this message may be logged later
this message should be logged
and this, but later
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="INFO is reset for each loop" duration="{duration}">
      <failure message="REQUIRE(i &lt; 10)">
FAILED:
	REQUIRE( i &lt; 10 )
with expansion:
	10 &lt; 10
current counter 10
i := 10
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Output from all sections is reported/one" duration="{duration}">
      <failure message="FAIL()">
FAILED:
Message from section one
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Output from all sections is reported/two" duration="{duration}">
      <failure message="FAIL()">
FAILED:
Message from section two
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="SUCCEED counts as a test pass" duration="{duration}"/>
    <testCase name="SUCCEED does not require an argument" duration="{duration}"/>
    <testCase name="Standard output from all sections is reported/two" duration="{duration}"/>
    <testCase name="The NO_FAIL macro reports a failure but does not fail the test" duration="{duration}"/>
    <testCase name="just failure" duration="{duration}">
      <failure message="FAIL()">
FAILED:
Previous info should not be seen
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="just failure after unscoped info" duration="{duration}">
      <failure message="FAIL()">
FAILED:
previous unscoped info SHOULD not be seen
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="mix info, unscoped info and warning" duration="{duration}"/>
    <testCase name="not prints unscoped info from previous failures" duration="{duration}">
      <failure message="REQUIRE(false)">
FAILED:
	REQUIRE( false )
this SHOULD be seen
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="print unscoped info if passing unscoped info is printed" duration="{duration}"/>
    <testCase name="prints unscoped info on failure" duration="{duration}">
      <failure message="REQUIRE(false)">
FAILED:
	REQUIRE( false )
this SHOULD be seen
this SHOULD also be seen
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="prints unscoped info only for the first assertion" duration="{duration}">
      <failure message="CHECK(false)">
FAILED:
	CHECK( false )
this SHOULD be seen only ONCE
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="sends information to INFO" duration="{duration}">
      <failure message="REQUIRE(false)">
FAILED:
	REQUIRE( false )
hi
i := 7
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="stacks unscoped info in loops" duration="{duration}">
      <failure message="CHECK(false)">
FAILED:
	CHECK( false )
Count 1 to 3...
1
2
3
at Message.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(false)">
FAILED:
	CHECK( false )
Count 4 to 6...
4
5
6
at Message.tests.cpp:<line number>
      </failure>
    </testCase>
  </file>
  <file path="tests/<exe-name>/UsageTests/Misc.tests.cpp">
    <testCase name="# A test name that starts with a #" duration="{duration}"/>
    <testCase name="#1175 - Hidden Test" duration="{duration}"/>
    <testCase name="#1954 - 7 arg template test case sig compiles - 1, 1, 1, 1, 1, 0, 0" duration="{duration}"/>
    <testCase name="#1954 - 7 arg template test case sig compiles - 5, 1, 1, 1, 1, 0, 0" duration="{duration}"/>
    <testCase name="#1954 - 7 arg template test case sig compiles - 5, 3, 1, 1, 1, 0, 0" duration="{duration}"/>
    <testCase name="#835 -- errno should not be touched by Catch2" duration="{duration}">
      <skipped message="CHECK(f() == 0)">
FAILED:
	CHECK( f() == 0 )
with expansion:
	1 == 0
at Misc.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="#961 -- Dynamically created sections should all be reported/Looped section 0" duration="{duration}"/>
    <testCase name="#961 -- Dynamically created sections should all be reported/Looped section 1" duration="{duration}"/>
    <testCase name="#961 -- Dynamically created sections should all be reported/Looped section 2" duration="{duration}"/>
    <testCase name="#961 -- Dynamically created sections should all be reported/Looped section 3" duration="{duration}"/>
    <testCase name="#961 -- Dynamically created sections should all be reported/Looped section 4" duration="{duration}"/>
    <testCase name="A Template product test case - Foo&lt;float>" duration="{duration}"/>
    <testCase name="A Template product test case - Foo&lt;int>" duration="{duration}"/>
    <testCase name="A Template product test case - std::vector&lt;float>" duration="{duration}"/>
    <testCase name="A Template product test case - std::vector&lt;int>" duration="{duration}"/>
    <testCase name="A Template product test case with array signature - Bar&lt;float, 42>" duration="{duration}"/>
    <testCase name="A Template product test case with array signature - Bar&lt;int, 9>" duration="{duration}"/>
    <testCase name="A Template product test case with array signature - std::array&lt;float, 42>" duration="{duration}"/>
    <testCase name="A Template product test case with array signature - std::array&lt;int, 9>" duration="{duration}"/>
    <testCase name="A couple of nested sections followed by a failure" duration="{duration}">
      <failure message="FAIL()">
FAILED:
to infinity and beyond
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="A couple of nested sections followed by a failure/Outer/Inner" duration="{duration}"/>
    <testCase name="Factorials are computed" duration="{duration}"/>
    <testCase name="ManuallyRegistered" duration="{duration}"/>
    <testCase name="Nice descriptive name" duration="{duration}"/>
    <testCase name="Product with differing arities - std::tuple&lt;int, double, float>" duration="{duration}"/>
    <testCase name="Product with differing arities - std::tuple&lt;int, double>" duration="{duration}"/>
    <testCase name="Product with differing arities - std::tuple&lt;int>" duration="{duration}"/>
    <testCase name="Sends stuff to stdout and stderr" duration="{duration}"/>
    <testCase name="Tabs and newlines show in output" duration="{duration}">
      <failure message="CHECK(s1 == s2)">
FAILED:
	CHECK( s1 == s2 )
with expansion:
	"if ($b == 10) {
		$a	= 20;
}"
==
"if ($b == 10) {
	$a = 20;
}
"
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="Template test case with test types specified inside non-copyable and non-movable std::tuple - NonCopyableAndNonMovableTypes - 0" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside non-copyable and non-movable std::tuple - NonCopyableAndNonMovableTypes - 1" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside non-default-constructible std::tuple - MyNonDefaultConstructibleTypes - 0" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside non-default-constructible std::tuple - MyNonDefaultConstructibleTypes - 1" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside std::tuple - MyTypes - 0" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside std::tuple - MyTypes - 1" duration="{duration}"/>
    <testCase name="Template test case with test types specified inside std::tuple - MyTypes - 2" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - float/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - int/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::string/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTest: vectors can be sized and resized - std::tuple&lt;int,float>/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - (std::tuple&lt;int, float>), 6/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - float,4/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - int,5/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="TemplateTestSig: vectors can be sized and resized - std::string,15/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="Testing checked-if" duration="{duration}"/>
    <testCase name="Testing checked-if 2" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Misc.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="Testing checked-if 3" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Misc.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="This test 'should' fail but doesn't" duration="{duration}"/>
    <testCase name="checkedElse" duration="{duration}"/>
    <testCase name="checkedElse, failing" duration="{duration}">
      <failure message="REQUIRE(testCheckedElse( false ))">
FAILED:
	REQUIRE( testCheckedElse( false ) )
with expansion:
	false
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="checkedIf" duration="{duration}"/>
    <testCase name="checkedIf, failing" duration="{duration}">
      <failure message="REQUIRE(testCheckedIf( false ))">
FAILED:
	REQUIRE( testCheckedIf( false ) )
with expansion:
	false
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="even more nested SECTION tests/c/d (leaf)" duration="{duration}"/>
    <testCase name="even more nested SECTION tests/c/e (leaf)" duration="{duration}"/>
    <testCase name="even more nested SECTION tests/f (leaf)" duration="{duration}"/>
    <testCase name="long long" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 0" duration="{duration}">
      <failure message="CHECK(b > a)">
FAILED:
	CHECK( b > a )
with expansion:
	0 > 1
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="looped SECTION tests/b is currently: 1" duration="{duration}">
      <failure message="CHECK(b > a)">
FAILED:
	CHECK( b > a )
with expansion:
	1 > 1
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="looped SECTION tests/b is currently: 2" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 3" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 4" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 5" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 6" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 7" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 8" duration="{duration}"/>
    <testCase name="looped SECTION tests/b is currently: 9" duration="{duration}"/>
    <testCase name="looped tests" duration="{duration}">
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[0] (1) is even
at Misc.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[1] (1) is even
at Misc.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[3] (3) is even
at Misc.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[4] (5) is even
at Misc.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[6] (13) is even
at Misc.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(( fib[i] % 2 ) == 0)">
FAILED:
	CHECK( ( fib[i] % 2 ) == 0 )
with expansion:
	1 == 0
Testing if fib[7] (21) is even
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="more nested SECTION tests/equal/doesn't equal" duration="{duration}">
      <failure message="REQUIRE(a == b)">
FAILED:
	REQUIRE( a == b )
with expansion:
	1 == 2
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="more nested SECTION tests/doesn't equal/not equal" duration="{duration}"/>
    <testCase name="more nested SECTION tests/doesn't equal/less than" duration="{duration}"/>
    <testCase name="nested SECTION tests/doesn't equal" duration="{duration}"/>
    <testCase name="nested SECTION tests/doesn't equal/not equal" duration="{duration}"/>
    <testCase name="not allowed" duration="{duration}"/>
    <testCase name="null strings" duration="{duration}"/>
    <testCase name="random SECTION tests/doesn't equal" duration="{duration}"/>
    <testCase name="random SECTION tests/not equal" duration="{duration}"/>
    <testCase name="send a single char to INFO" duration="{duration}">
      <failure message="REQUIRE(false)">
FAILED:
	REQUIRE( false )
3
at Misc.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="toString on const wchar_t const pointer returns the string contents" duration="{duration}"/>
    <testCase name="toString on const wchar_t pointer returns the string contents" duration="{duration}"/>
    <testCase name="toString on wchar_t const pointer returns the string contents" duration="{duration}"/>
    <testCase name="toString on wchar_t returns the string contents" duration="{duration}"/>
    <testCase name="vectors can be sized and resized" duration="{duration}"/>
    <testCase name="vectors can be sized and resized/resizing bigger changes size and capacity" duration="{duration}"/>
    <testCase name="vectors can be sized and resized/resizing smaller changes size but not capacity" duration="{duration}"/>
    <testCase name="vectors can be sized and resized/resizing smaller changes size but not capacity/We can use the 'swap trick' to reset the capacity" duration="{duration}"/>
    <testCase name="vectors can be sized and resized/reserving bigger changes capacity but not size" duration="{duration}"/>
    <testCase name="vectors can be sized and resized/reserving smaller does not change size or capacity" duration="{duration}"/>
    <testCase name="xmlentitycheck/embedded xml: &lt;test>it should be possible to embed xml characters, such as &lt;, &quot; or &amp;, or even whole &lt;xml>documents&lt;/xml> within an attribute&lt;/test>" duration="{duration}"/>
    <testCase name="xmlentitycheck/encoded chars: these should all be encoded: &amp;&amp;&amp;&quot;&quot;&quot;&lt;&lt;&lt;&amp;&quot;&lt;&lt;&amp;&quot;" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Skip.tests.cpp">
    <testCase name="Empty generators can SKIP in constructor" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
This generator is empty
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="a succeeding test can still be skipped" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="dynamic skipping works with generators" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
skipping because answer = 41
at Skip.tests.cpp:<line number>
      </skipped>
      <skipped message="SKIP()">
SKIPPED
skipping because answer = 43
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="failed assertions before SKIP cause test case to fail" duration="{duration}">
      <skipped message="CHECK(3 == 4)">
FAILED:
	CHECK( 3 == 4 )
at Skip.tests.cpp:<line number>
      </skipped>
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="failing for some generator values causes entire test case to fail" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Skip.tests.cpp:<line number>
      </skipped>
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
      <skipped message="FAIL()">
FAILED:
at Skip.tests.cpp:<line number>
      </skipped>
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="failing in some unskipped sections causes entire test case to fail/skipped" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="failing in some unskipped sections causes entire test case to fail/not skipped" duration="{duration}">
      <skipped message="FAIL()">
FAILED:
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="nested sections can be skipped dynamically at runtime/B2/B" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="nested sections can be skipped dynamically at runtime/B" duration="{duration}"/>
    <testCase name="sections can be skipped dynamically at runtime/not skipped" duration="{duration}"/>
    <testCase name="sections can be skipped dynamically at runtime/skipped" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="sections can be skipped dynamically at runtime/also not skipped" duration="{duration}"/>
    <testCase name="skipped tests can optionally provide a reason" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
skipping because answer = 43
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
    <testCase name="tests can be skipped dynamically at runtime" duration="{duration}">
      <skipped message="SKIP()">
SKIPPED
at Skip.tests.cpp:<line number>
      </skipped>
    </testCase>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringChrono.tests.cpp">
    <testCase name="Stringifying std::chrono::duration helpers" duration="{duration}"/>
    <testCase name="Stringifying std::chrono::duration with weird ratios" duration="{duration}"/>
    <testCase name="Stringifying std::chrono::time_point&lt;system_clock>" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringGeneral.tests.cpp">
    <testCase name="Capture and info messages/Capture should stringify like assertions" duration="{duration}"/>
    <testCase name="Capture and info messages/Info should NOT stringify the way assertions do" duration="{duration}"/>
    <testCase name="Character pretty printing/Specifically escaped" duration="{duration}"/>
    <testCase name="Character pretty printing/General chars" duration="{duration}"/>
    <testCase name="Character pretty printing/Low ASCII" duration="{duration}"/>
    <testCase name="Exception as a value (e.g. in REQUIRE_THROWS_MATCHES) can be stringified" duration="{duration}"/>
    <testCase name="Precision of floating point stringification can be set/Floats" duration="{duration}"/>
    <testCase name="Precision of floating point stringification can be set/Double" duration="{duration}"/>
    <testCase name="Static arrays are convertible to string/Single item" duration="{duration}"/>
    <testCase name="Static arrays are convertible to string/Multiple" duration="{duration}"/>
    <testCase name="Static arrays are convertible to string/Non-trivial inner items" duration="{duration}"/>
    <testCase name="std::map is convertible string/empty" duration="{duration}"/>
    <testCase name="std::map is convertible string/single item" duration="{duration}"/>
    <testCase name="std::map is convertible string/several items" duration="{duration}"/>
    <testCase name="std::set is convertible string/empty" duration="{duration}"/>
    <testCase name="std::set is convertible string/single item" duration="{duration}"/>
    <testCase name="std::set is convertible string/several items" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringPair.tests.cpp">
    <testCase name="pair&lt;pair&lt;int,const char *,pair&lt;std::string,int> > -> toString" duration="{duration}"/>
    <testCase name="std::pair&lt;int,const std::string> -> toString" duration="{duration}"/>
    <testCase name="std::pair&lt;int,std::string> -> toString" duration="{duration}"/>
    <testCase name="std::vector&lt;std::pair&lt;std::string,int> > -> toString" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringTuple.tests.cpp">
    <testCase name="tuple&lt;>" duration="{duration}"/>
    <testCase name="tuple&lt;float,int>" duration="{duration}"/>
    <testCase name="tuple&lt;int>" duration="{duration}"/>
    <testCase name="tuple&lt;string,string>" duration="{duration}"/>
    <testCase name="tuple&lt;tuple&lt;int>,tuple&lt;>,float>" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringVector.tests.cpp">
    <testCase name="array&lt;int, N> -> toString" duration="{duration}"/>
    <testCase name="vec&lt;vec&lt;string,alloc>> -> toString" duration="{duration}"/>
    <testCase name="vector&lt;bool> -> toString" duration="{duration}"/>
    <testCase name="vector&lt;int,allocator> -> toString" duration="{duration}"/>
    <testCase name="vector&lt;int> -> toString" duration="{duration}"/>
    <testCase name="vector&lt;string> -> toString" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/ToStringWhich.tests.cpp">
    <testCase name="stringify ranges" duration="{duration}"/>
    <testCase name="stringify( has_maker )" duration="{duration}"/>
    <testCase name="stringify( has_maker_and_operator )" duration="{duration}"/>
    <testCase name="stringify( has_neither )" duration="{duration}"/>
    <testCase name="stringify( has_operator )" duration="{duration}"/>
    <testCase name="stringify( has_template_operator )" duration="{duration}"/>
    <testCase name="stringify( vectors&lt;has_maker> )" duration="{duration}"/>
    <testCase name="stringify( vectors&lt;has_maker_and_operator> )" duration="{duration}"/>
    <testCase name="stringify( vectors&lt;has_operator> )" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/Tricky.tests.cpp">
    <testCase name="#1514: stderr/stdout is not captured in tests aborted by an exception" duration="{duration}">
      <failure message="FAIL()">
FAILED:
1514
at Tricky.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="(unimplemented) static bools can be evaluated/compare to true" duration="{duration}"/>
    <testCase name="(unimplemented) static bools can be evaluated/compare to false" duration="{duration}"/>
    <testCase name="(unimplemented) static bools can be evaluated/negation" duration="{duration}"/>
    <testCase name="(unimplemented) static bools can be evaluated/double negation" duration="{duration}"/>
    <testCase name="(unimplemented) static bools can be evaluated/direct" duration="{duration}"/>
    <testCase name="A failing expression with a non streamable type is still captured" duration="{duration}">
      <failure message="CHECK(&amp;o1 == &amp;o2)">
FAILED:
	CHECK( &amp;o1 == &amp;o2 )
with expansion:
	0x<hex digits> == 0x<hex digits>
at Tricky.tests.cpp:<line number>
      </failure>
      <failure message="CHECK(o1 == o2)">
FAILED:
	CHECK( o1 == o2 )
with expansion:
	{?} == {?}
at Tricky.tests.cpp:<line number>
      </failure>
    </testCase>
    <testCase name="An expression with side-effects should only be evaluated once" duration="{duration}"/>
    <testCase name="Assertions then sections" duration="{duration}"/>
    <testCase name="Assertions then sections/A section" duration="{duration}"/>
    <testCase name="Assertions then sections/A section/Another section" duration="{duration}"/>
    <testCase name="Assertions then sections/A section/Another other section" duration="{duration}"/>
    <testCase name="Commas in various macros are allowed" duration="{duration}"/>
    <testCase name="Comparing function pointers" duration="{duration}"/>
    <testCase name="Objects that evaluated in boolean contexts can be checked" duration="{duration}"/>
    <testCase name="Test enum bit values" duration="{duration}"/>
    <testCase name="X/level/0/a" duration="{duration}"/>
    <testCase name="X/level/0/b" duration="{duration}"/>
    <testCase name="X/level/1/a" duration="{duration}"/>
    <testCase name="X/level/1/b" duration="{duration}"/>
    <testCase name="boolean member" duration="{duration}"/>
    <testCase name="non streamable - with conv. op" duration="{duration}"/>
    <testCase name="non-copyable objects" duration="{duration}"/>
    <testCase name="null_ptr" duration="{duration}"/>
    <testCase name="pointer to class" duration="{duration}"/>
  </file>
  <file path="tests/<exe-name>/UsageTests/VariadicMacros.tests.cpp">
    <testCase name="Anonymous test case 1" duration="{duration}"/>
    <testCase name="Test case with one argument" duration="{duration}"/>
    <testCase name="Variadic macros/Section with one argument" duration="{duration}"/>
  </file>
</testExecutions>
