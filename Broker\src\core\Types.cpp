#include "core/Types.h"
#include <unordered_map>
#include <algorithm>
#include <cctype>

namespace RoboQuant::Broker {

namespace {
    // Helper function to convert string to lowercase
    std::string to_lower(std::string str) {
        std::transform(str.begin(), str.end(), str.begin(),
                      [](unsigned char c) { return std::tolower(c); });
        return str;
    }
}

// String conversion implementations
std::string to_string(BrokerType type) {
    switch (type) {
        case BrokerType::CTP: return "CTP";
        case BrokerType::IB: return "IB";
        case BrokerType::FIX: return "FIX";
        case BrokerType::Simulation: return "Simulation";
        case BrokerType::XTP: return "XTP";
        case BrokerType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(AssetType type) {
    switch (type) {
        case AssetType::Stock: return "Stock";
        case AssetType::Future: return "Future";
        case AssetType::Option: return "Option";
        case AssetType::Bond: return "Bond";
        case AssetType::Fund: return "Fund";
        case AssetType::Currency: return "Currency";
        case AssetType::Crypto: return "Crypto";
        case AssetType::Index: return "Index";
        case AssetType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(OrderSide side) {
    switch (side) {
        case OrderSide::Buy: return "Buy";
        case OrderSide::Sell: return "Sell";
        default: return "Unknown";
    }
}

std::string to_string(OrderType type) {
    switch (type) {
        case OrderType::Market: return "Market";
        case OrderType::Limit: return "Limit";
        case OrderType::Stop: return "Stop";
        case OrderType::StopLimit: return "StopLimit";
        case OrderType::FAK: return "FAK";
        case OrderType::FOK: return "FOK";
        case OrderType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(OrderStatus status) {
    switch (status) {
        case OrderStatus::PendingNew: return "PendingNew";
        case OrderStatus::New: return "New";
        case OrderStatus::PartiallyFilled: return "PartiallyFilled";
        case OrderStatus::Filled: return "Filled";
        case OrderStatus::PendingCancel: return "PendingCancel";
        case OrderStatus::Cancelled: return "Cancelled";
        case OrderStatus::Rejected: return "Rejected";
        case OrderStatus::Expired: return "Expired";
        case OrderStatus::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(PositionEffect effect) {
    switch (effect) {
        case PositionEffect::Open: return "Open";
        case PositionEffect::Close: return "Close";
        case PositionEffect::CloseToday: return "CloseToday";
        case PositionEffect::CloseYesterday: return "CloseYesterday";
        case PositionEffect::ForceClose: return "ForceClose";
        case PositionEffect::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(TimeInForce tif) {
    switch (tif) {
        case TimeInForce::Day: return "Day";
        case TimeInForce::GTC: return "GTC";
        case TimeInForce::IOC: return "IOC";
        case TimeInForce::FOK: return "FOK";
        case TimeInForce::GTD: return "GTD";
        case TimeInForce::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(AccountType type) {
    switch (type) {
        case AccountType::Cash: return "Cash";
        case AccountType::Margin: return "Margin";
        case AccountType::Future: return "Future";
        case AccountType::Option: return "Option";
        case AccountType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(ConnectionStatus status) {
    switch (status) {
        case ConnectionStatus::Disconnected: return "Disconnected";
        case ConnectionStatus::Connecting: return "Connecting";
        case ConnectionStatus::Connected: return "Connected";
        case ConnectionStatus::Authenticated: return "Authenticated";
        case ConnectionStatus::Error: return "Error";
        default: return "Unknown";
    }
}

std::string to_string(EventType type) {
    switch (type) {
        case EventType::OrderUpdate: return "OrderUpdate";
        case EventType::TradeUpdate: return "TradeUpdate";
        case EventType::AccountUpdate: return "AccountUpdate";
        case EventType::PositionUpdate: return "PositionUpdate";
        case EventType::ConnectionUpdate: return "ConnectionUpdate";
        case EventType::ErrorEvent: return "ErrorEvent";
        case EventType::MarketDataUpdate: return "MarketDataUpdate";
        case EventType::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

std::string to_string(ErrorCode code) {
    switch (code) {
        case ErrorCode::Success: return "Success";
        case ErrorCode::InvalidParameter: return "InvalidParameter";
        case ErrorCode::OrderNotFound: return "OrderNotFound";
        case ErrorCode::AccountNotFound: return "AccountNotFound";
        case ErrorCode::InsufficientFunds: return "InsufficientFunds";
        case ErrorCode::ConnectionError: return "ConnectionError";
        case ErrorCode::AuthenticationError: return "AuthenticationError";
        case ErrorCode::PermissionDenied: return "PermissionDenied";
        case ErrorCode::OrderRejected: return "OrderRejected";
        case ErrorCode::BrokerError: return "BrokerError";
        case ErrorCode::NetworkError: return "NetworkError";
        case ErrorCode::TimeoutError: return "TimeoutError";
        case ErrorCode::InternalError: return "InternalError";
        case ErrorCode::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

// Parsing implementations
BrokerType parse_broker_type(const std::string& str) {
    static const std::unordered_map<std::string, BrokerType> map = {
        {"ctp", BrokerType::CTP},
        {"ib", BrokerType::IB},
        {"fix", BrokerType::FIX},
        {"simulation", BrokerType::Simulation},
        {"xtp", BrokerType::XTP}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : BrokerType::Unknown;
}

AssetType parse_asset_type(const std::string& str) {
    static const std::unordered_map<std::string, AssetType> map = {
        {"stock", AssetType::Stock},
        {"future", AssetType::Future},
        {"option", AssetType::Option},
        {"bond", AssetType::Bond},
        {"fund", AssetType::Fund},
        {"currency", AssetType::Currency},
        {"crypto", AssetType::Crypto},
        {"index", AssetType::Index}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : AssetType::Unknown;
}

OrderSide parse_order_side(const std::string& str) {
    std::string lower = to_lower(str);
    if (lower == "buy" || lower == "b" || lower == "1") return OrderSide::Buy;
    if (lower == "sell" || lower == "s" || lower == "2") return OrderSide::Sell;
    return OrderSide::Buy; // Default fallback
}

OrderType parse_order_type(const std::string& str) {
    static const std::unordered_map<std::string, OrderType> map = {
        {"market", OrderType::Market},
        {"limit", OrderType::Limit},
        {"stop", OrderType::Stop},
        {"stoplimit", OrderType::StopLimit},
        {"fak", OrderType::FAK},
        {"fok", OrderType::FOK}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : OrderType::Unknown;
}

OrderStatus parse_order_status(const std::string& str) {
    static const std::unordered_map<std::string, OrderStatus> map = {
        {"pendingnew", OrderStatus::PendingNew},
        {"new", OrderStatus::New},
        {"partiallyfilled", OrderStatus::PartiallyFilled},
        {"filled", OrderStatus::Filled},
        {"pendingcancel", OrderStatus::PendingCancel},
        {"cancelled", OrderStatus::Cancelled},
        {"rejected", OrderStatus::Rejected},
        {"expired", OrderStatus::Expired}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : OrderStatus::Unknown;
}

PositionEffect parse_position_effect(const std::string& str) {
    static const std::unordered_map<std::string, PositionEffect> map = {
        {"open", PositionEffect::Open},
        {"close", PositionEffect::Close},
        {"closetoday", PositionEffect::CloseToday},
        {"closeyesterday", PositionEffect::CloseYesterday},
        {"forceclose", PositionEffect::ForceClose}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : PositionEffect::Unknown;
}

TimeInForce parse_time_in_force(const std::string& str) {
    static const std::unordered_map<std::string, TimeInForce> map = {
        {"day", TimeInForce::Day},
        {"gtc", TimeInForce::GTC},
        {"ioc", TimeInForce::IOC},
        {"fok", TimeInForce::FOK},
        {"gtd", TimeInForce::GTD}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : TimeInForce::Unknown;
}

AccountType parse_account_type(const std::string& str) {
    static const std::unordered_map<std::string, AccountType> map = {
        {"cash", AccountType::Cash},
        {"margin", AccountType::Margin},
        {"future", AccountType::Future},
        {"option", AccountType::Option}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : AccountType::Unknown;
}

ConnectionStatus parse_connection_status(const std::string& str) {
    static const std::unordered_map<std::string, ConnectionStatus> map = {
        {"disconnected", ConnectionStatus::Disconnected},
        {"connecting", ConnectionStatus::Connecting},
        {"connected", ConnectionStatus::Connected},
        {"authenticated", ConnectionStatus::Authenticated},
        {"error", ConnectionStatus::Error}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : ConnectionStatus::Disconnected;
}

EventType parse_event_type(const std::string& str) {
    static const std::unordered_map<std::string, EventType> map = {
        {"orderupdate", EventType::OrderUpdate},
        {"tradeupdate", EventType::TradeUpdate},
        {"accountupdate", EventType::AccountUpdate},
        {"positionupdate", EventType::PositionUpdate},
        {"connectionupdate", EventType::ConnectionUpdate},
        {"errorevent", EventType::ErrorEvent},
        {"marketdataupdate", EventType::MarketDataUpdate}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : EventType::Unknown;
}

ErrorCode parse_error_code(const std::string& str) {
    static const std::unordered_map<std::string, ErrorCode> map = {
        {"success", ErrorCode::Success},
        {"invalidparameter", ErrorCode::InvalidParameter},
        {"ordernotfound", ErrorCode::OrderNotFound},
        {"accountnotfound", ErrorCode::AccountNotFound},
        {"insufficientfunds", ErrorCode::InsufficientFunds},
        {"connectionerror", ErrorCode::ConnectionError},
        {"authenticationerror", ErrorCode::AuthenticationError},
        {"permissiondenied", ErrorCode::PermissionDenied},
        {"orderrejected", ErrorCode::OrderRejected},
        {"brokererror", ErrorCode::BrokerError},
        {"networkerror", ErrorCode::NetworkError},
        {"timeouterror", ErrorCode::TimeoutError},
        {"internalerror", ErrorCode::InternalError}
    };
    
    auto it = map.find(to_lower(str));
    return it != map.end() ? it->second : ErrorCode::Unknown;
}

} // namespace RoboQuant::Broker
