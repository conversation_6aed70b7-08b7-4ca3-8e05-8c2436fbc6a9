/**
 * @file ExpressionEngine.cpp
 * @brief Implementation of expression engine classes
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/ExpressionEngine.h"
#include <sstream>
#include <regex>
#include <cmath>
#include <algorithm>

namespace RoboQuant::Trading {

// ExpressionContext Implementation
void ExpressionContext::set_variable(const std::string& name, ExpressionValue value) {
    std::unique_lock lock(variables_mutex_);
    variables_[name] = std::move(value);
}

std::optional<ExpressionValue> ExpressionContext::get_variable(const std::string& name) const {
    std::shared_lock lock(variables_mutex_);
    auto it = variables_.find(name);
    return it != variables_.end() ? std::make_optional(it->second) : std::nullopt;
}

bool ExpressionContext::has_variable(const std::string& name) const {
    std::shared_lock lock(variables_mutex_);
    return variables_.find(name) != variables_.end();
}

void ExpressionContext::remove_variable(const std::string& name) {
    std::unique_lock lock(variables_mutex_);
    variables_.erase(name);
}

void ExpressionContext::clear_variables() {
    std::unique_lock lock(variables_mutex_);
    variables_.clear();
}

void ExpressionContext::set_variables(const std::unordered_map<std::string, ExpressionValue>& variables) {
    std::unique_lock lock(variables_mutex_);
    for (const auto& [name, value] : variables) {
        variables_[name] = value;
    }
}

std::unordered_map<std::string, ExpressionValue> ExpressionContext::get_all_variables() const {
    std::shared_lock lock(variables_mutex_);
    return variables_;
}

// Simple AST Node Implementations
class LiteralNode : public ExpressionNode {
public:
    explicit LiteralNode(ExpressionValue value) : value_(std::move(value)) {}
    
    ExpressionValue evaluate(const ExpressionContext& context) const override {
        return value_;
    }
    
    std::string to_string() const override {
        if (std::holds_alternative<double>(value_)) {
            return std::to_string(std::get<double>(value_));
        } else if (std::holds_alternative<bool>(value_)) {
            return std::get<bool>(value_) ? "true" : "false";
        } else {
            return "\"" + std::get<std::string>(value_) + "\"";
        }
    }

private:
    ExpressionValue value_;
};

class VariableNode : public ExpressionNode {
public:
    explicit VariableNode(std::string name) : name_(std::move(name)) {}
    
    ExpressionValue evaluate(const ExpressionContext& context) const override {
        auto value = context.get_variable(name_);
        if (!value) {
            throw std::runtime_error("Undefined variable: " + name_);
        }
        return *value;
    }
    
    std::string to_string() const override {
        return name_;
    }

private:
    std::string name_;
};

class BinaryOpNode : public ExpressionNode {
public:
    enum class Operator { Add, Subtract, Multiply, Divide, Greater, Less, Equal, And, Or };
    
    BinaryOpNode(std::unique_ptr<ExpressionNode> left, Operator op, std::unique_ptr<ExpressionNode> right)
        : left_(std::move(left)), op_(op), right_(std::move(right)) {}
    
    ExpressionValue evaluate(const ExpressionContext& context) const override {
        auto left_val = left_->evaluate(context);
        auto right_val = right_->evaluate(context);
        
        switch (op_) {
            case Operator::Add:
                return evaluate_add(left_val, right_val);
            case Operator::Subtract:
                return evaluate_subtract(left_val, right_val);
            case Operator::Multiply:
                return evaluate_multiply(left_val, right_val);
            case Operator::Divide:
                return evaluate_divide(left_val, right_val);
            case Operator::Greater:
                return evaluate_greater(left_val, right_val);
            case Operator::Less:
                return evaluate_less(left_val, right_val);
            case Operator::Equal:
                return evaluate_equal(left_val, right_val);
            case Operator::And:
                return evaluate_and(left_val, right_val);
            case Operator::Or:
                return evaluate_or(left_val, right_val);
            default:
                throw std::runtime_error("Unknown binary operator");
        }
    }
    
    std::string to_string() const override {
        std::string op_str;
        switch (op_) {
            case Operator::Add: op_str = "+"; break;
            case Operator::Subtract: op_str = "-"; break;
            case Operator::Multiply: op_str = "*"; break;
            case Operator::Divide: op_str = "/"; break;
            case Operator::Greater: op_str = ">"; break;
            case Operator::Less: op_str = "<"; break;
            case Operator::Equal: op_str = "=="; break;
            case Operator::And: op_str = "&&"; break;
            case Operator::Or: op_str = "||"; break;
        }
        return "(" + left_->to_string() + " " + op_str + " " + right_->to_string() + ")";
    }

private:
    std::unique_ptr<ExpressionNode> left_;
    Operator op_;
    std::unique_ptr<ExpressionNode> right_;
    
    ExpressionValue evaluate_add(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            return std::get<double>(left) + std::get<double>(right);
        }
        throw std::runtime_error("Type error in addition");
    }
    
    ExpressionValue evaluate_subtract(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            return std::get<double>(left) - std::get<double>(right);
        }
        throw std::runtime_error("Type error in subtraction");
    }
    
    ExpressionValue evaluate_multiply(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            return std::get<double>(left) * std::get<double>(right);
        }
        throw std::runtime_error("Type error in multiplication");
    }
    
    ExpressionValue evaluate_divide(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            double divisor = std::get<double>(right);
            if (std::abs(divisor) < 1e-10) {
                throw std::runtime_error("Division by zero");
            }
            return std::get<double>(left) / divisor;
        }
        throw std::runtime_error("Type error in division");
    }
    
    ExpressionValue evaluate_greater(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            return std::get<double>(left) > std::get<double>(right);
        }
        throw std::runtime_error("Type error in comparison");
    }
    
    ExpressionValue evaluate_less(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<double>(left) && std::holds_alternative<double>(right)) {
            return std::get<double>(left) < std::get<double>(right);
        }
        throw std::runtime_error("Type error in comparison");
    }
    
    ExpressionValue evaluate_equal(const ExpressionValue& left, const ExpressionValue& right) const {
        if (left.index() != right.index()) {
            return false;
        }
        
        if (std::holds_alternative<double>(left)) {
            return std::abs(std::get<double>(left) - std::get<double>(right)) < 1e-10;
        } else if (std::holds_alternative<bool>(left)) {
            return std::get<bool>(left) == std::get<bool>(right);
        } else {
            return std::get<std::string>(left) == std::get<std::string>(right);
        }
    }
    
    ExpressionValue evaluate_and(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<bool>(left) && std::holds_alternative<bool>(right)) {
            return std::get<bool>(left) && std::get<bool>(right);
        }
        throw std::runtime_error("Type error in logical AND");
    }
    
    ExpressionValue evaluate_or(const ExpressionValue& left, const ExpressionValue& right) const {
        if (std::holds_alternative<bool>(left) && std::holds_alternative<bool>(right)) {
            return std::get<bool>(left) || std::get<bool>(right);
        }
        throw std::runtime_error("Type error in logical OR");
    }
};

class FunctionCallNode : public ExpressionNode {
public:
    FunctionCallNode(std::string name, std::vector<std::unique_ptr<ExpressionNode>> args)
        : name_(std::move(name)), args_(std::move(args)) {}
    
    ExpressionValue evaluate(const ExpressionContext& context) const override {
        // This is a simplified implementation
        // In a real implementation, you would look up the function in a function registry
        
        std::vector<ExpressionValue> arg_values;
        for (const auto& arg : args_) {
            arg_values.push_back(arg->evaluate(context));
        }
        
        // Built-in functions
        if (name_ == "abs" && arg_values.size() == 1) {
            if (std::holds_alternative<double>(arg_values[0])) {
                return std::abs(std::get<double>(arg_values[0]));
            }
        } else if (name_ == "max" && arg_values.size() == 2) {
            if (std::holds_alternative<double>(arg_values[0]) && std::holds_alternative<double>(arg_values[1])) {
                return std::max(std::get<double>(arg_values[0]), std::get<double>(arg_values[1]));
            }
        } else if (name_ == "min" && arg_values.size() == 2) {
            if (std::holds_alternative<double>(arg_values[0]) && std::holds_alternative<double>(arg_values[1])) {
                return std::min(std::get<double>(arg_values[0]), std::get<double>(arg_values[1]));
            }
        }
        
        throw std::runtime_error("Unknown function: " + name_);
    }
    
    std::string to_string() const override {
        std::string result = name_ + "(";
        for (size_t i = 0; i < args_.size(); ++i) {
            if (i > 0) result += ", ";
            result += args_[i]->to_string();
        }
        result += ")";
        return result;
    }

private:
    std::string name_;
    std::vector<std::unique_ptr<ExpressionNode>> args_;
};

// CompiledExpression Implementation
CompiledExpression::CompiledExpression(std::unique_ptr<ExpressionNode> root) 
    : root_(std::move(root)) {}

ExpressionValue CompiledExpression::evaluate(const ExpressionContext& context) const {
    if (!root_) {
        throw std::runtime_error("Invalid compiled expression");
    }
    return root_->evaluate(context);
}

std::string CompiledExpression::to_string() const {
    return root_ ? root_->to_string() : "null";
}

// Simple Expression Parser Implementation
class SimpleExpressionParser {
public:
    std::unique_ptr<ExpressionNode> parse(const std::string& expression) {
        tokens_ = tokenize(expression);
        pos_ = 0;
        return parse_expression();
    }

private:
    std::vector<std::string> tokens_;
    size_t pos_;
    
    std::vector<std::string> tokenize(const std::string& expr) {
        std::vector<std::string> tokens;
        std::regex token_regex(R"(\d+\.?\d*|[a-zA-Z_][a-zA-Z0-9_]*|[+\-*/()><]=?|&&|\|\||==|!=|[,])");
        
        std::sregex_iterator iter(expr.begin(), expr.end(), token_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::string token = iter->str();
            if (!token.empty() && token != " ") {
                tokens.push_back(token);
            }
        }
        
        return tokens;
    }
    
    std::unique_ptr<ExpressionNode> parse_expression() {
        return parse_or();
    }
    
    std::unique_ptr<ExpressionNode> parse_or() {
        auto left = parse_and();
        
        while (pos_ < tokens_.size() && tokens_[pos_] == "||") {
            pos_++; // consume ||
            auto right = parse_and();
            left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Or, std::move(right));
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_and() {
        auto left = parse_equality();
        
        while (pos_ < tokens_.size() && tokens_[pos_] == "&&") {
            pos_++; // consume &&
            auto right = parse_equality();
            left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::And, std::move(right));
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_equality() {
        auto left = parse_comparison();
        
        while (pos_ < tokens_.size() && (tokens_[pos_] == "==" || tokens_[pos_] == "!=")) {
            std::string op = tokens_[pos_++];
            auto right = parse_comparison();
            
            if (op == "==") {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Equal, std::move(right));
            }
            // Note: != would need to be implemented as NOT(Equal)
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_comparison() {
        auto left = parse_addition();
        
        while (pos_ < tokens_.size() && (tokens_[pos_] == ">" || tokens_[pos_] == "<" || 
                                        tokens_[pos_] == ">=" || tokens_[pos_] == "<=")) {
            std::string op = tokens_[pos_++];
            auto right = parse_addition();
            
            if (op == ">") {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Greater, std::move(right));
            } else if (op == "<") {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Less, std::move(right));
            }
            // >= and <= would need additional operators
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_addition() {
        auto left = parse_multiplication();
        
        while (pos_ < tokens_.size() && (tokens_[pos_] == "+" || tokens_[pos_] == "-")) {
            std::string op = tokens_[pos_++];
            auto right = parse_multiplication();
            
            if (op == "+") {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Add, std::move(right));
            } else {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Subtract, std::move(right));
            }
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_multiplication() {
        auto left = parse_primary();
        
        while (pos_ < tokens_.size() && (tokens_[pos_] == "*" || tokens_[pos_] == "/")) {
            std::string op = tokens_[pos_++];
            auto right = parse_primary();
            
            if (op == "*") {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Multiply, std::move(right));
            } else {
                left = std::make_unique<BinaryOpNode>(std::move(left), BinaryOpNode::Operator::Divide, std::move(right));
            }
        }
        
        return left;
    }
    
    std::unique_ptr<ExpressionNode> parse_primary() {
        if (pos_ >= tokens_.size()) {
            throw std::runtime_error("Unexpected end of expression");
        }
        
        std::string token = tokens_[pos_++];
        
        // Number literal
        if (std::regex_match(token, std::regex(R"(\d+\.?\d*)"))) {
            return std::make_unique<LiteralNode>(std::stod(token));
        }
        
        // Boolean literals
        if (token == "true") {
            return std::make_unique<LiteralNode>(true);
        }
        if (token == "false") {
            return std::make_unique<LiteralNode>(false);
        }
        
        // Parentheses
        if (token == "(") {
            auto expr = parse_expression();
            if (pos_ >= tokens_.size() || tokens_[pos_] != ")") {
                throw std::runtime_error("Missing closing parenthesis");
            }
            pos_++; // consume )
            return expr;
        }
        
        // Function call or variable
        if (std::regex_match(token, std::regex(R"([a-zA-Z_][a-zA-Z0-9_]*)"))) {
            // Check if it's a function call
            if (pos_ < tokens_.size() && tokens_[pos_] == "(") {
                pos_++; // consume (
                std::vector<std::unique_ptr<ExpressionNode>> args;
                
                if (pos_ < tokens_.size() && tokens_[pos_] != ")") {
                    args.push_back(parse_expression());
                    
                    while (pos_ < tokens_.size() && tokens_[pos_] == ",") {
                        pos_++; // consume ,
                        args.push_back(parse_expression());
                    }
                }
                
                if (pos_ >= tokens_.size() || tokens_[pos_] != ")") {
                    throw std::runtime_error("Missing closing parenthesis in function call");
                }
                pos_++; // consume )
                
                return std::make_unique<FunctionCallNode>(token, std::move(args));
            } else {
                // Variable
                return std::make_unique<VariableNode>(token);
            }
        }
        
        throw std::runtime_error("Unexpected token: " + token);
    }
};

// ExpressionEngine Implementation
class ExpressionEngine::Impl {
public:
    SimpleExpressionParser parser;
    std::optional<ExpressionEngine::ParseError> last_error;
};

ExpressionEngine::ExpressionEngine() : pimpl_(std::make_unique<Impl>()) {}

ExpressionEngine::~ExpressionEngine() = default;

void ExpressionEngine::register_function(std::unique_ptr<ExpressionFunction> function) {
    // This would be implemented with a function registry
    // For now, this is a placeholder
}

void ExpressionEngine::unregister_function(const std::string& name) {
    // This would be implemented with a function registry
    // For now, this is a placeholder
}

bool ExpressionEngine::has_function(const std::string& name) const {
    // This would be implemented with a function registry
    // For now, return true for built-in functions
    return name == "abs" || name == "max" || name == "min";
}

std::vector<std::string> ExpressionEngine::get_registered_functions() const {
    // This would be implemented with a function registry
    return {"abs", "max", "min"};
}

void ExpressionEngine::register_builtin_functions() {
    // This would register all built-in functions
    // For now, this is a placeholder
}

void ExpressionEngine::register_math_functions() {
    // This would register math functions
    // For now, this is a placeholder
}

void ExpressionEngine::register_string_functions() {
    // This would register string functions
    // For now, this is a placeholder
}

void ExpressionEngine::register_trading_functions() {
    // This would register trading-specific functions
    // For now, this is a placeholder
}

std::vector<std::string> ExpressionEngine::get_variables_in_expression(const std::string& expression) const {
    // This would parse the expression and extract variable names
    // For now, return empty vector
    return {};
}

std::unique_ptr<CompiledExpression> ExpressionEngine::compile(const std::string& expression) {
    try {
        auto root = pimpl_->parser.parse(expression);
        return std::make_unique<CompiledExpression>(std::move(root));
    } catch (const std::exception& e) {
        pimpl_->last_error = ParseError{e.what(), 0, expression};
        return nullptr;
    }
}

ExpressionValue ExpressionEngine::evaluate(const std::string& expression, const ExpressionContext& context) {
    auto compiled = compile(expression);
    if (!compiled) {
        throw std::runtime_error("Failed to compile expression: " + expression);
    }
    return compiled->evaluate(context);
}

bool ExpressionEngine::is_valid_expression(const std::string& expression) const {
    try {
        auto compiled = const_cast<ExpressionEngine*>(this)->compile(expression);
        return compiled != nullptr;
    } catch (...) {
        return false;
    }
}

std::optional<ExpressionEngine::ParseError> ExpressionEngine::get_last_error() const {
    return pimpl_->last_error;
}

// ExpressionCache Implementation
ExpressionCache::ExpressionCache(size_t max_size) : max_size_(max_size) {}

void ExpressionCache::put(const std::string& expression, std::unique_ptr<CompiledExpression> compiled) {
    std::unique_lock lock(cache_mutex_);
    
    if (cache_.size() >= max_size_) {
        evict_lru();
    }
    
    // Remove from LRU list if already exists
    auto lru_it = lru_map_.find(expression);
    if (lru_it != lru_map_.end()) {
        lru_list_.erase(lru_it->second);
        lru_map_.erase(lru_it);
    }
    
    // Add to cache and LRU list
    cache_[expression] = std::move(compiled);
    lru_list_.push_front(expression);
    lru_map_[expression] = lru_list_.begin();
}

CompiledExpression* ExpressionCache::get(const std::string& expression) const {
    std::unique_lock lock(cache_mutex_);
    
    auto it = cache_.find(expression);
    if (it != cache_.end()) {
        // Move to front of LRU list
        auto lru_it = lru_map_.find(expression);
        if (lru_it != lru_map_.end()) {
            lru_list_.erase(lru_it->second);
            lru_list_.push_front(expression);
            lru_map_[expression] = lru_list_.begin();
        }
        
        hit_count_.fetch_add(1);
        return it->second.get();
    }
    
    miss_count_.fetch_add(1);
    return nullptr;
}

void ExpressionCache::evict_lru() {
    if (!lru_list_.empty()) {
        std::string oldest = lru_list_.back();
        lru_list_.pop_back();
        lru_map_.erase(oldest);
        cache_.erase(oldest);
    }
}

double ExpressionCache::hit_rate() const {
    size_t hits = hit_count_.load();
    size_t misses = miss_count_.load();
    size_t total = hits + misses;
    return total > 0 ? static_cast<double>(hits) / total : 0.0;
}

// ExpressionManager Implementation
ExpressionManager::ExpressionManager(size_t cache_size) : cache_(cache_size) {}

ExpressionValue ExpressionManager::evaluate(const std::string& expression, const ExpressionContext& context) {
    // Check cache first
    auto* cached = cache_.get(expression);
    if (cached) {
        return cached->evaluate(context);
    }
    
    // Compile and cache
    auto compiled = engine_.compile(expression);
    if (!compiled) {
        throw std::runtime_error("Failed to compile expression: " + expression);
    }
    
    auto result = compiled->evaluate(context);
    cache_.put(expression, std::move(compiled));
    
    return result;
}

} // namespace RoboQuant::Trading
