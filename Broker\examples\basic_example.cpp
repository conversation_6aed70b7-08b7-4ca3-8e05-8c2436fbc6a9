#include <iostream>
#include <thread>
#include <chrono>

// 包含 Broker_Modern 头文件
#include "core/Types.h"
#include "core/EventSystem.h"
#include "orders/Order.h"
#include "orders/OrderManager.h"
#include "accounts/Account.h"
#include "brokers/BrokerInterface.h"
#include "brokers/SimulationBroker.h"

using namespace RoboQuant::Broker;

int main() {
    std::cout << "=== Broker_Modern 基本示例 ===\n\n";
    
    try {
        // 1. 注册模拟交易接口
        register_simulation_broker();
        std::cout << "? 模拟交易接口已注册\n";
        
        // 2. 启动事件系统
        auto& event_dispatcher = get_event_dispatcher();
        event_dispatcher.start();
        std::cout << "? 事件系统已启动\n";
        
        // 3. 创建模拟交易接口
        auto broker = BrokerRegistry::instance().create_broker(BrokerType::Simulation);
        if (!broker) {
            std::cerr << "? 无法创建模拟交易接口\n";
            return 1;
        }
        std::cout << "? 模拟交易接口已创建: " << broker->get_broker_name() << "\n";
        
        // 4. 连接到模拟交易接口
        ConnectionInfo conn_info(BrokerType::Simulation, "localhost", 0);
        auto connect_result = broker->connect(conn_info);
        if (!connect_result) {
            std::cerr << "? 连接失败: " << connect_result.error().message << "\n";
            return 1;
        }
        std::cout << "? 已连接到模拟交易接口\n";
        
        // 5. 认证
        auto auth_result = broker->authenticate("test_user", "test_password");
        if (!auth_result) {
            std::cerr << "? 认证失败: " << auth_result.error().message << "\n";
            return 1;
        }
        std::cout << "? 认证成功\n";
        
        // 6. 查询账户信息
        auto accounts_result = broker->query_accounts();
        if (!accounts_result) {
            std::cerr << "? 查询账户失败: " << accounts_result.error().message << "\n";
            return 1;
        }
        
        auto accounts = accounts_result.value();
        std::cout << "? 查询到 " << accounts.size() << " 个账户:\n";
        for (const auto& account : accounts) {
            std::cout << "  - " << account.to_string() << "\n";
        }
        
        // 7. 创建订单管理器
        OrderManager order_manager;
        order_manager.set_broker_interface(broker);
        order_manager.start_event_handling();
        std::cout << "? 订单管理器已创建并启动\n";
        
        // 8. 设置订单回调
        order_manager.set_order_callback([](const Order& order) {
            std::cout << "? 订单更新: " << order.order_id() 
                     << " 状态: " << to_string(order.status())
                     << " 已成交: " << order.filled_quantity() << "/" << order.quantity() << "\n";
        });
        
        // 9. 创建并提交订单
        std::cout << "\n--- 提交订单 ---\n";
        
        OrderRequest request;
        request.asset_id = "000001.SZ";
        request.side = OrderSide::Buy;
        request.type = OrderType::Limit;
        request.quantity = 1000;
        request.price = 10.50;
        request.account_id = accounts[0].account_id();
        request.strategy_id = "basic_example";
        
        auto submit_result = order_manager.submit_order(request);
        if (!submit_result) {
            std::cerr << "? 订单提交失败: " << submit_result.error().message << "\n";
            return 1;
        }
        
        auto order_id = submit_result.value();
        std::cout << "? 订单已提交: " << order_id << "\n";
        
        // 10. 启动模拟交易
        auto sim_broker = std::dynamic_pointer_cast<SimulationBroker>(broker);
        if (sim_broker) {
            sim_broker->start_simulation();
            std::cout << "? 模拟交易已启动\n";
        }
        
        // 11. 等待订单处理
        std::cout << "\n--- 等待订单处理 ---\n";
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 12. 查询订单状态
        auto order_result = order_manager.get_order(order_id);
        if (order_result) {
            const auto& order = *order_result;
            std::cout << "? 最终订单状态:\n";
            std::cout << "  订单ID: " << order.order_id() << "\n";
            std::cout << "  资产: " << order.asset_id() << "\n";
            std::cout << "  方向: " << to_string(order.side()) << "\n";
            std::cout << "  数量: " << order.quantity() << "\n";
            std::cout << "  已成交: " << order.filled_quantity() << "\n";
            std::cout << "  状态: " << to_string(order.status()) << "\n";
            if (order.filled_quantity() > 0) {
                std::cout << "  平均成交价: " << order.average_fill_price() << "\n";
            }
        }
        
        // 13. 查询活跃订单
        auto active_orders = order_manager.get_active_orders();
        std::cout << "\n? 活跃订单数量: " << active_orders.size() << "\n";
        
        // 14. 查询交易记录
        auto trades_result = broker->query_trades(accounts[0].account_id());
        if (trades_result) {
            auto trades = trades_result.value();
            std::cout << "? 交易记录数量: " << trades.size() << "\n";
            for (const auto& trade : trades) {
                std::cout << "  - " << trade.trade_id() 
                         << " " << to_string(trade.side())
                         << " " << trade.quantity() 
                         << " @ " << trade.price() << "\n";
            }
        }
        
        // 15. 统计信息
        std::cout << "\n? 统计信息:\n";
        std::cout << "  总订单数: " << order_manager.total_orders() << "\n";
        std::cout << "  活跃订单数: " << order_manager.active_orders_count() << "\n";
        std::cout << "  已成交订单数: " << order_manager.filled_orders_count() << "\n";
        std::cout << "  已取消订单数: " << order_manager.cancelled_orders_count() << "\n";
        
        // 16. 清理
        if (sim_broker) {
            sim_broker->stop_simulation();
        }
        order_manager.stop_event_handling();
        broker->disconnect();
        event_dispatcher.stop();
        
        std::cout << "\n? 示例程序执行完成\n";
        
    } catch (const std::exception& e) {
        std::cerr << "? 异常: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}
