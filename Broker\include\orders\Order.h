#pragma once

#include "core/Types.h"
#include <nlohmann/json.hpp>
#include <optional>
#include <unordered_map>

namespace RoboQuant::Broker {

// Order request structure
struct OrderRequest {
    AssetId asset_id;
    OrderSide side;
    OrderType type;
    Quantity quantity;
    std::optional<Price> price;
    std::optional<Price> stop_price;
    TimeInForce time_in_force{TimeInForce::Day};
    PositionEffect position_effect{PositionEffect::Open};
    AccountId account_id;
    std::string strategy_id;
    std::unordered_map<std::string, std::string> metadata;
    
    // Validation
    bool is_valid() const;
    std::string validation_error() const;
    
    // Serialization
    nlohmann::json to_json() const;
    static OrderRequest from_json(const nlohmann::json& j);
};

// Order class
class Order {
public:
    // Constructors
    Order() = default;
    explicit Order(const OrderRequest& request);
    Order(OrderId order_id, const OrderRequest& request);
    
    // Copy and move
    Order(const Order&) = default;
    Order& operator=(const Order&) = default;
    Order(Order&&) = default;
    Order& operator=(Order&&) = default;
    
    // Getters
    const OrderId& order_id() const noexcept { return order_id_; }
    const AssetId& asset_id() const noexcept { return asset_id_; }
    OrderSide side() const noexcept { return side_; }
    OrderType type() const noexcept { return type_; }
    Quantity quantity() const noexcept { return quantity_; }
    Quantity filled_quantity() const noexcept { return filled_quantity_; }
    Quantity remaining_quantity() const noexcept { return quantity_ - filled_quantity_; }
    
    std::optional<Price> price() const noexcept { return price_; }
    std::optional<Price> stop_price() const noexcept { return stop_price_; }
    Price average_fill_price() const noexcept { return average_fill_price_; }
    
    OrderStatus status() const noexcept { return status_; }
    TimeInForce time_in_force() const noexcept { return time_in_force_; }
    PositionEffect position_effect() const noexcept { return position_effect_; }
    
    const AccountId& account_id() const noexcept { return account_id_; }
    const std::string& strategy_id() const noexcept { return strategy_id_; }
    const std::string& broker_order_id() const noexcept { return broker_order_id_; }
    
    TimePoint create_time() const noexcept { return create_time_; }
    TimePoint update_time() const noexcept { return update_time_; }
    std::optional<TimePoint> fill_time() const noexcept { return fill_time_; }
    
    const std::string& last_error() const noexcept { return last_error_; }
    const std::unordered_map<std::string, std::string>& metadata() const noexcept { return metadata_; }
    
    // Setters (for internal use)
    void set_broker_order_id(const std::string& broker_id) { broker_order_id_ = broker_id; }
    void set_status(OrderStatus status);
    void set_error(const std::string& error);
    
    // Fill management
    void add_fill(Quantity fill_quantity, Price fill_price);
    bool is_filled() const noexcept { return status_ == OrderStatus::Filled; }
    bool is_partially_filled() const noexcept { return status_ == OrderStatus::PartiallyFilled; }
    bool is_active() const noexcept;
    bool is_terminal() const noexcept;
    
    // Validation
    bool is_valid() const;
    std::string validation_error() const;
    
    // Serialization
    nlohmann::json to_json() const;
    static Order from_json(const nlohmann::json& j);
    
    // Comparison
    bool operator==(const Order& other) const noexcept;
    bool operator!=(const Order& other) const noexcept { return !(*this == other); }

private:
    void update_timestamp() { update_time_ = std::chrono::system_clock::now(); }
    void calculate_average_fill_price();
    
    // Core order data
    OrderId order_id_;
    AssetId asset_id_;
    OrderSide side_{OrderSide::Buy};
    OrderType type_{OrderType::Market};
    Quantity quantity_{0};
    Quantity filled_quantity_{0};
    std::optional<Price> price_;
    std::optional<Price> stop_price_;
    Price average_fill_price_{0.0};
    
    // Order state
    OrderStatus status_{OrderStatus::PendingNew};
    TimeInForce time_in_force_{TimeInForce::Day};
    PositionEffect position_effect_{PositionEffect::Open};
    
    // Account and strategy info
    AccountId account_id_;
    std::string strategy_id_;
    std::string broker_order_id_;
    
    // Timestamps
    TimePoint create_time_;
    TimePoint update_time_;
    std::optional<TimePoint> fill_time_;
    
    // Error tracking
    std::string last_error_;
    
    // Additional metadata
    std::unordered_map<std::string, std::string> metadata_;
    
    // Fill tracking
    struct Fill {
        Quantity quantity;
        Price price;
        TimePoint timestamp;
        
        Fill(Quantity q, Price p, TimePoint t = std::chrono::system_clock::now())
            : quantity(q), price(p), timestamp(t) {}
    };
    std::vector<Fill> fills_;
};

// Order comparison functors
struct OrderIdHash {
    std::size_t operator()(const OrderId& id) const noexcept {
        return std::hash<std::string>{}(id);
    }
};

struct OrderIdEqual {
    bool operator()(const OrderId& lhs, const OrderId& rhs) const noexcept {
        return lhs == rhs;
    }
};

// Utility functions
OrderId generate_order_id();
bool is_buy_order(const Order& order) noexcept;
bool is_sell_order(const Order& order) noexcept;
bool is_market_order(const Order& order) noexcept;
bool is_limit_order(const Order& order) noexcept;

// Order validation
Result<void> validate_order_request(const OrderRequest& request);
Result<void> validate_order(const Order& order);

} // namespace RoboQuant::Broker

// JSON serialization support
namespace nlohmann {
    template<>
    struct adl_serializer<RoboQuant::Broker::OrderRequest> {
        static void to_json(json& j, const RoboQuant::Broker::OrderRequest& req) {
            j = req.to_json();
        }
        
        static void from_json(const json& j, RoboQuant::Broker::OrderRequest& req) {
            req = RoboQuant::Broker::OrderRequest::from_json(j);
        }
    };
    
    template<>
    struct adl_serializer<RoboQuant::Broker::Order> {
        static void to_json(json& j, const RoboQuant::Broker::Order& order) {
            j = order.to_json();
        }
        
        static void from_json(const json& j, RoboQuant::Broker::Order& order) {
            order = RoboQuant::Broker::Order::from_json(j);
        }
    };
}
