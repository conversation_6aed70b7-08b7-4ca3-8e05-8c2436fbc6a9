#pragma once

#include "core/Types.h"
#include "orders/Order.h"
#include "accounts/Account.h"
#include <functional>
#include <memory>
#include <future>

namespace RoboQuant::Broker {

// Forward declarations
class Position;
class Trade;

// Callback types
using OrderUpdateCallback = std::function<void(const Order&)>;
using TradeCallback = std::function<void(const Trade&)>;
using AccountUpdateCallback = std::function<void(const Account&)>;
using PositionUpdateCallback = std::function<void(const Position&)>;
using ConnectionStatusCallback = std::function<void(BrokerType, ConnectionStatus, const std::string&)>;
using ErrorCallback = std::function<void(ErrorCode, const std::string&, const std::any&)>;

// Broker capabilities
struct BrokerCapabilities {
    bool supports_market_orders{true};
    bool supports_limit_orders{true};
    bool supports_stop_orders{false};
    bool supports_stop_limit_orders{false};
    bool supports_fak_orders{false};
    bool supports_fok_orders{false};
    bool supports_order_modification{false};
    bool supports_partial_fills{true};
    bool supports_position_tracking{true};
    bool supports_real_time_data{true};
    bool supports_historical_data{false};
    bool supports_multiple_accounts{false};
    
    std::vector<AssetType> supported_asset_types;
    std::vector<std::string> supported_exchanges;
    
    nlohmann::json to_json() const;
    static BrokerCapabilities from_json(const nlohmann::json& j);
};

// Broker configuration
struct BrokerConfig {
    BrokerType broker_type{BrokerType::Unknown};
    ConnectionInfo connection_info;
    std::unordered_map<std::string, std::string> settings;
    bool auto_reconnect{true};
    Duration reconnect_interval{std::chrono::seconds(30)};
    Duration heartbeat_interval{std::chrono::seconds(60)};
    
    nlohmann::json to_json() const;
    static BrokerConfig from_json(const nlohmann::json& j);
};

// Abstract broker interface
class BrokerInterface {
public:
    virtual ~BrokerInterface() = default;
    
    // Broker information
    virtual BrokerType get_broker_type() const = 0;
    virtual std::string get_broker_name() const = 0;
    virtual std::string get_version() const = 0;
    virtual BrokerCapabilities get_capabilities() const = 0;
    
    // Connection management
    virtual Result<void> connect(const ConnectionInfo& info) = 0;
    virtual Result<void> disconnect() = 0;
    virtual bool is_connected() const = 0;
    virtual ConnectionStatus get_connection_status() const = 0;
    virtual Result<void> authenticate(const std::string& username, const std::string& password) = 0;
    virtual bool is_authenticated() const = 0;
    
    // Order management
    virtual Result<void> submit_order(const Order& order) = 0;
    virtual Result<void> cancel_order(const Order& order) = 0;
    virtual Result<void> modify_order(const Order& order, const OrderRequest& new_request) = 0;
    virtual Result<std::vector<Order>> query_orders(const AccountId& account_id = "") = 0;
    virtual Result<Order> query_order(const OrderId& order_id) = 0;
    
    // Account management
    virtual Result<std::vector<Account>> query_accounts() = 0;
    virtual Result<Account> query_account(const AccountId& account_id) = 0;
    virtual Result<std::vector<Position>> query_positions(const AccountId& account_id = "") = 0;
    virtual Result<std::vector<Trade>> query_trades(const AccountId& account_id = "", 
                                                   const TimePoint& from = {},
                                                   const TimePoint& to = {}) = 0;
    
    // Market data (optional)
    virtual Result<void> subscribe_market_data(const AssetId& asset_id) { 
        return ErrorCode::PermissionDenied; 
    }
    virtual Result<void> unsubscribe_market_data(const AssetId& asset_id) { 
        return ErrorCode::PermissionDenied; 
    }
    
    // Callback registration
    virtual void set_order_update_callback(OrderUpdateCallback callback) = 0;
    virtual void set_trade_callback(TradeCallback callback) = 0;
    virtual void set_account_update_callback(AccountUpdateCallback callback) = 0;
    virtual void set_position_update_callback(PositionUpdateCallback callback) = 0;
    virtual void set_connection_status_callback(ConnectionStatusCallback callback) = 0;
    virtual void set_error_callback(ErrorCallback callback) = 0;
    
    // Configuration
    virtual Result<void> configure(const BrokerConfig& config) = 0;
    virtual BrokerConfig get_config() const = 0;
    
    // Health check
    virtual Result<void> health_check() = 0;
    virtual TimePoint last_heartbeat() const = 0;
    
    // Async operations
    virtual std::future<Result<void>> submit_order_async(const Order& order) = 0;
    virtual std::future<Result<void>> cancel_order_async(const Order& order) = 0;
    virtual std::future<Result<std::vector<Order>>> query_orders_async(const AccountId& account_id = "") = 0;
    virtual std::future<Result<std::vector<Account>>> query_accounts_async() = 0;
};

using BrokerInterfacePtr = std::shared_ptr<BrokerInterface>;

// Broker factory interface
class BrokerFactory {
public:
    virtual ~BrokerFactory() = default;
    virtual BrokerType get_broker_type() const = 0;
    virtual std::string get_broker_name() const = 0;
    virtual BrokerInterfacePtr create_broker() = 0;
    virtual BrokerInterfacePtr create_broker(const BrokerConfig& config) = 0;
    virtual bool supports_config(const BrokerConfig& config) const = 0;
};

using BrokerFactoryPtr = std::shared_ptr<BrokerFactory>;

// Broker registry for plugin management
class BrokerRegistry {
public:
    static BrokerRegistry& instance();
    
    // Factory registration
    void register_factory(BrokerFactoryPtr factory);
    void unregister_factory(BrokerType broker_type);
    
    // Broker creation
    BrokerInterfacePtr create_broker(BrokerType broker_type);
    BrokerInterfacePtr create_broker(const BrokerConfig& config);
    
    // Query capabilities
    std::vector<BrokerType> get_supported_broker_types() const;
    std::vector<std::string> get_supported_broker_names() const;
    BrokerFactoryPtr get_factory(BrokerType broker_type) const;
    bool is_supported(BrokerType broker_type) const;
    
private:
    BrokerRegistry() = default;
    std::unordered_map<BrokerType, BrokerFactoryPtr> factories_;
    mutable std::shared_mutex factories_mutex_;
};

// Base broker implementation with common functionality
class BaseBroker : public BrokerInterface {
public:
    explicit BaseBroker(BrokerType type, const std::string& name);
    virtual ~BaseBroker() = default;
    
    // Broker information
    BrokerType get_broker_type() const override { return broker_type_; }
    std::string get_broker_name() const override { return broker_name_; }
    std::string get_version() const override { return "1.0.0"; }
    
    // Connection status
    bool is_connected() const override { return connection_status_ == ConnectionStatus::Connected || 
                                                connection_status_ == ConnectionStatus::Authenticated; }
    ConnectionStatus get_connection_status() const override { return connection_status_; }
    bool is_authenticated() const override { return connection_status_ == ConnectionStatus::Authenticated; }
    
    // Callback registration
    void set_order_update_callback(OrderUpdateCallback callback) override { order_callback_ = callback; }
    void set_trade_callback(TradeCallback callback) override { trade_callback_ = callback; }
    void set_account_update_callback(AccountUpdateCallback callback) override { account_callback_ = callback; }
    void set_position_update_callback(PositionUpdateCallback callback) override { position_callback_ = callback; }
    void set_connection_status_callback(ConnectionStatusCallback callback) override { connection_callback_ = callback; }
    void set_error_callback(ErrorCallback callback) override { error_callback_ = callback; }
    
    // Configuration
    Result<void> configure(const BrokerConfig& config) override;
    BrokerConfig get_config() const override { return config_; }
    
    // Health check
    TimePoint last_heartbeat() const override { return last_heartbeat_; }
    
    // Async operations (default implementations using std::async)
    std::future<Result<void>> submit_order_async(const Order& order) override;
    std::future<Result<void>> cancel_order_async(const Order& order) override;
    std::future<Result<std::vector<Order>>> query_orders_async(const AccountId& account_id = "") override;
    std::future<Result<std::vector<Account>>> query_accounts_async() override;

protected:
    // Helper methods for derived classes
    void set_connection_status(ConnectionStatus status);
    void notify_order_update(const Order& order);
    void notify_trade(const Trade& trade);
    void notify_account_update(const Account& account);
    void notify_position_update(const Position& position);
    void notify_error(ErrorCode code, const std::string& message, const std::any& context = {});
    void update_heartbeat();
    
    BrokerType broker_type_;
    std::string broker_name_;
    ConnectionStatus connection_status_{ConnectionStatus::Disconnected};
    BrokerConfig config_;
    TimePoint last_heartbeat_;
    
    // Callbacks
    OrderUpdateCallback order_callback_;
    TradeCallback trade_callback_;
    AccountUpdateCallback account_callback_;
    PositionUpdateCallback position_callback_;
    ConnectionStatusCallback connection_callback_;
    ErrorCallback error_callback_;
    
    mutable std::shared_mutex state_mutex_;
};

// Utility functions
std::string broker_type_to_string(BrokerType type);
BrokerType string_to_broker_type(const std::string& str);
bool is_broker_available(BrokerType type);
std::vector<BrokerType> get_available_brokers();

// Broker plugin loading
Result<void> load_broker_plugin(const std::string& plugin_path);
Result<void> unload_broker_plugin(BrokerType broker_type);
std::vector<std::string> get_loaded_plugins();

} // namespace RoboQuant::Broker
