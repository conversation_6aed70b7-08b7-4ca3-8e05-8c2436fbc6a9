# RoboQuant Integration Configuration Template

@PACKAGE_INIT@

set(ROBOQUANT_INTEGRATION_VERSION "@PROJECT_VERSION@")
set(ROBOQUANT_INTEGRATION_VERSION_MAJOR "@PROJECT_VERSION_MAJOR@")
set(ROBOQUANT_INTEGRATION_VERSION_MINOR "@PROJECT_VERSION_MINOR@")
set(ROBOQUANT_INTEGRATION_VERSION_PATCH "@PROJECT_VERSION_PATCH@")

# Dependencies
set(ROBOQUANT_INTEGRATION_TORCH_FOUND @TORCH_FOUND@)
set(RO<PERSON>QUANT_INTEGRATION_LEVELDB_FOUND @LEVELDB_FOUND@)

# Build configuration
set(ROBOQUANT_INTEGRATION_BUILD_TYPE "@CMAKE_BUILD_TYPE@")
set(ROBOQUANT_INTEGRATION_CXX_STANDARD "@CMAKE_CXX_STANDARD@")

# Installation paths
set_and_check(ROBOQUANT_INTEGRATION_INCLUDE_DIR "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
set_and_check(ROBOQUANT_INTEGRATION_LIB_DIR "@PACKAGE_CMAKE_INSTALL_LIBDIR@")
set_and_check(ROBOQUANT_INTEGRATION_BIN_DIR "@PACKAGE_CMAKE_INSTALL_BINDIR@")

# Import targets
include("${CMAKE_CURRENT_LIST_DIR}/RoboQuantIntegrationTargets.cmake")

# Check required components
check_required_components(RoboQuantIntegration)

# Provide information about found components
if(ROBOQUANT_INTEGRATION_TORCH_FOUND)
    message(STATUS "RoboQuant Integration: LibTorch support enabled")
endif()

if(ROBOQUANT_INTEGRATION_LEVELDB_FOUND)
    message(STATUS "RoboQuant Integration: LevelDB support enabled")
endif()

message(STATUS "RoboQuant Integration ${ROBOQUANT_INTEGRATION_VERSION} found")
