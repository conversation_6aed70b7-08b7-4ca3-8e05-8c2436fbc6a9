cmake_minimum_required(VERSION 3.20)

find_package(GTest QUIET)

if(NOT GTest_FOUND)
    message(STATUS "GTest not found, creating simple test framework")
    
    add_executable(broker_modern_tests
        simple_tests.cpp
        test_types.cpp
        test_order.cpp
        test_account.cpp
        test_simulation_broker.cpp
        test_ctp_broker.cpp
        test_event_system.cpp
        test_configuration.cpp
    )
    
    target_link_libraries(broker_modern_tests
        broker_modern
        Boost::system
        Boost::thread
        nlohmann_json::nlohmann_json
        spdlog::spdlog
    )
    
    set_target_properties(broker_modern_tests PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )
    
    add_test(NAME BrokerModernTests COMMAND broker_modern_tests)
    
else()
    message(STATUS "Using GTest for testing")
    
    add_executable(broker_modern_gtests
        gtest_main.cpp
        gtest_types.cpp
        gtest_order.cpp
        gtest_account.cpp
        gtest_simulation_broker.cpp
        gtest_event_system.cpp
        gtest_configuration.cpp
    )
    
    target_link_libraries(broker_modern_gtests
        broker_modern
        GTest::gtest
        GTest::gtest_main
        Boost::system
        Boost::thread
        nlohmann_json::nlohmann_json
        spdlog::spdlog
    )
    
    set_target_properties(broker_modern_gtests PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )
    
    include(GoogleTest)
    gtest_discover_tests(broker_modern_gtests)
endif()

add_executable(broker_modern_benchmarks
    benchmarks.cpp
)

target_link_libraries(broker_modern_benchmarks
    broker_modern
    Boost::system
    Boost::thread
    nlohmann_json::nlohmann_json
    spdlog::spdlog
)

set_target_properties(broker_modern_benchmarks PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
)
