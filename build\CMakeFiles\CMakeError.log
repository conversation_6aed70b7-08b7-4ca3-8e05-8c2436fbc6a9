Performing C++ SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: E:/lab/RoboQuant/Experiments/build/CMakeFiles/CMakeScratch/TryCompile-edp0gb

Run Build Command(s):d:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_a355b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
  鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
  cl /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_a355b.dir\Debug\\" /Fd"cmTC_a355b.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-edp0gb\src.cxx"

  src.cxx

E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-edp0gb\src.cxx(15,3): error C3861: 鈥減thread_atfork鈥? 鎵句笉鍒版爣璇嗙 [E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-edp0gb\cmTC_a355b.vcxproj]



Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}


