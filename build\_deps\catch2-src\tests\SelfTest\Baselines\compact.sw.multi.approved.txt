Filters: "*" ~[!nonportable] ~[!benchmark] ~[approvals]
RNG seed: 1
Misc.tests.cpp:<line number>: passed: with 1 message: 'yay'
Compilation.tests.cpp:<line number>: passed: y.v == 0 for: 0 == 0
Compilation.tests.cpp:<line number>: passed: 0 == y.v for: 0 == 0
Compilation.tests.cpp:<line number>: passed: t1 == t2 for: {?} == {?}
Compilation.tests.cpp:<line number>: passed: t1 != t2 for: {?} != {?}
Compilation.tests.cpp:<line number>: passed: t1 < t2 for: {?} < {?}
Compilation.tests.cpp:<line number>: passed: t1 > t2 for: {?} > {?}
Compilation.tests.cpp:<line number>: passed: t1 <= t2 for: {?} <= {?}
Compilation.tests.cpp:<line number>: passed: t1 >= t2 for: {?} >= {?}
Misc.tests.cpp:<line number>: passed:
Compilation.tests.cpp:<line number>: passed: std::memcmp(uarr, "123", sizeof(uarr)) == 0 for: 0 == 0 with 2 messages: 'uarr := "123"' and 'sarr := "456"'
Compilation.tests.cpp:<line number>: passed: std::memcmp(sarr, "456", sizeof(sarr)) == 0 for: 0 == 0 with 2 messages: 'uarr := "123"' and 'sarr := "456"'
Compilation.tests.cpp:<line number>: passed:
Compilation.tests.cpp:<line number>: passed:
Compilation.tests.cpp:<line number>: passed: h1 == h2 for: [1403 helper] == [1403 helper]
Message.tests.cpp:<line number>: warning: '
This info message starts with a linebreak' with 1 message: '
This warning message starts with a linebreak'
Tricky.tests.cpp:<line number>: failed: explicitly with 1 message: '1514'
Compilation.tests.cpp:<line number>: passed: std::is_same<TypeList<int>, TypeList<int>>::value for: true
TestSpec.tests.cpp:<line number>: passed: spec.matches(*fakeTestCase("spec . char")) for: true
TestSpec.tests.cpp:<line number>: passed: spec.matches(*fakeTestCase("spec , char")) for: true
TestSpec.tests.cpp:<line number>: passed: !(spec.matches(*fakeTestCase(R"(spec \, char)"))) for: !false
TestSpec.tests.cpp:<line number>: passed: spec.matches(*fakeTestCase(R"(spec {a} char)")) for: true
TestSpec.tests.cpp:<line number>: passed: spec.matches(*fakeTestCase(R"(spec [a] char)")) for: true
TestSpec.tests.cpp:<line number>: passed: !(spec.matches(*fakeTestCase("differs but has similar tag", "[a]"))) for: !false
TestSpec.tests.cpp:<line number>: passed: spec.matches(*fakeTestCase(R"(spec \ char)")) for: true
Generators.tests.cpp:<line number>: passed: counter < 7 for: 3 < 7
Generators.tests.cpp:<line number>: passed: counter < 7 for: 6 < 7
Generators.tests.cpp:<line number>: passed: i != j for: 1 != 3
Generators.tests.cpp:<line number>: passed: i != j for: 1 != 4
Generators.tests.cpp:<line number>: passed: i != j for: 2 != 3
Generators.tests.cpp:<line number>: passed: i != j for: 2 != 4
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'A'
PartTracker.tests.cpp:<line number>: passed: m for: 1
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: 1
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: m for: 1
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'A'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 1' and 'j := 3' and 'k := 5'
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'B'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 1' and 'j := 3' and 'k := 6'
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'B'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 1' and 'j := 4' and 'k := 5'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 1' and 'j := 4' and 'k := 6'
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'A'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 2' and 'j := 3' and 'k := 5'
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'B'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 2' and 'j := 3' and 'k := 6'
PartTracker.tests.cpp:<line number>: passed: with 1 message: 'B'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 2' and 'j := 4' and 'k := 5'
PartTracker.tests.cpp:<line number>: passed: with 3 messages: 'i := 2' and 'j := 4' and 'k := 6'
PartTracker.tests.cpp:<line number>: passed: m for: 1
PartTracker.tests.cpp:<line number>: passed: n for: 1
PartTracker.tests.cpp:<line number>: passed: m for: 1
PartTracker.tests.cpp:<line number>: passed: n for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 1
PartTracker.tests.cpp:<line number>: passed: n for: 3
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: n for: 1
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: n for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 2
PartTracker.tests.cpp:<line number>: passed: n for: 3
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: n for: 1
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: n for: 2
PartTracker.tests.cpp:<line number>: passed: m for: 3
PartTracker.tests.cpp:<line number>: passed: n for: 3
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed:
Matchers.tests.cpp:<line number>: passed: smallest_non_zero, WithinULP( -smallest_non_zero, 2 ) for: 0.0 is within 2 ULPs of -4.9406564584124654e-324 ([-1.4821969375237396e-323, 4.9406564584124654e-324])
Matchers.tests.cpp:<line number>: passed: smallest_non_zero, !WithinULP( -smallest_non_zero, 1 ) for: 0.0 not is within 1 ULPs of -4.9406564584124654e-324 ([-9.8813129168249309e-324, -0.0000000000000000e+00])
Matchers.tests.cpp:<line number>: passed: smallest_non_zero, WithinULP( -smallest_non_zero, 2 ) for: 0.0f is within 2 ULPs of -1.40129846e-45f ([-4.20389539e-45, 1.40129846e-45])
Matchers.tests.cpp:<line number>: passed: smallest_non_zero, !WithinULP( -smallest_non_zero, 1 ) for: 0.0f not is within 1 ULPs of -1.40129846e-45f ([-2.80259693e-45, -0.00000000e+00])
Generators.tests.cpp:<line number>: failed: unexpected exception with message: 'failure to init'
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'answer := 42' with 1 message: 'expected exception'
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'answer := 42'; expression was: thisThrows() with 1 message: 'expected exception'
Exception.tests.cpp:<line number>: passed: thisThrows() with 1 message: 'answer := 42'
Compilation.tests.cpp:<line number>: passed: 42 == f for: 42 == {?}
Compilation.tests.cpp:<line number>: passed: a == t for: 3 == 3
Compilation.tests.cpp:<line number>: passed: a == t for: 3 == 3
Compilation.tests.cpp:<line number>: passed: throws_int(true)
Compilation.tests.cpp:<line number>: passed: throws_int(true), int
Compilation.tests.cpp:<line number>: passed: throws_int(false)
Compilation.tests.cpp:<line number>: passed: "aaa", Catch::Matchers::EndsWith("aaa") for: "aaa" ends with: "aaa"
Compilation.tests.cpp:<line number>: passed: templated_tests<int>(3) for: true
Misc.tests.cpp:<line number>: failed: f() == 0 for: 1 == 0
Misc.tests.cpp:<line number>: passed: errno_after == 1 for: 1 == 1
Compilation.tests.cpp:<line number>: passed: x == 4 for: {?} == 4 with 1 message: 'dummy := 0'
Misc.tests.cpp:<line number>: passed: with 1 message: 'Everything is OK'
Misc.tests.cpp:<line number>: passed: with 1 message: 'Everything is OK'
Misc.tests.cpp:<line number>: passed: with 1 message: 'Everything is OK'
Misc.tests.cpp:<line number>: passed: with 1 message: 'Everything is OK'
Misc.tests.cpp:<line number>: passed: with 1 message: 'Everything is OK'
Condition.tests.cpp:<line number>: failed: false != false
Condition.tests.cpp:<line number>: failed: true != true
Condition.tests.cpp:<line number>: failed: !true for: false
Condition.tests.cpp:<line number>: failed: !(true) for: !true
Condition.tests.cpp:<line number>: failed: !trueValue for: false
Condition.tests.cpp:<line number>: failed: !(trueValue) for: !true
Condition.tests.cpp:<line number>: failed: !(1 == 1) for: false
Condition.tests.cpp:<line number>: failed: !(1 == 1)
Condition.tests.cpp:<line number>: passed: false == false
Condition.tests.cpp:<line number>: passed: true == true
Condition.tests.cpp:<line number>: passed: !false for: true
Condition.tests.cpp:<line number>: passed: !(false) for: !false
Condition.tests.cpp:<line number>: passed: !falseValue for: true
Condition.tests.cpp:<line number>: passed: !(falseValue) for: !false
Condition.tests.cpp:<line number>: passed: !(1 == 2) for: true
Condition.tests.cpp:<line number>: passed: !(1 == 2)
Tricky.tests.cpp:<line number>: passed: is_true<true>::value == true for: true == true
Tricky.tests.cpp:<line number>: passed: true == is_true<true>::value for: true == true
Tricky.tests.cpp:<line number>: passed: is_true<false>::value == false for: false == false
Tricky.tests.cpp:<line number>: passed: false == is_true<false>::value for: false == false
Tricky.tests.cpp:<line number>: passed: !is_true<false>::value for: true
Tricky.tests.cpp:<line number>: passed: !!is_true<true>::value for: true
Tricky.tests.cpp:<line number>: passed: is_true<true>::value for: true
Tricky.tests.cpp:<line number>: passed: !(is_true<false>::value) for: !false
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 1 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 1 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 2 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 2 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 4
Generators.tests.cpp:<line number>: passed: y < z for: 4 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 5
Generators.tests.cpp:<line number>: passed: y < z for: 5 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 9
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 7
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 7
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 8
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 8
Generators.tests.cpp:<line number>: passed: x < y for: 3 < 6
Generators.tests.cpp:<line number>: passed: y < z for: 6 < 9
Generators.tests.cpp:<line number>: passed: x < z for: 3 < 9
Class.tests.cpp:<line number>: failed: s == "world" for: "hello" == "world"
Class.tests.cpp:<line number>: passed: s == "hello" for: "hello" == "hello"
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>::m_a.size() == 1 for: 0 == 1
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>::m_a.size() == 1 for: 0 == 1
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>::m_a.size() == 1 for: 0 == 1
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>::m_a.size() == 1 for: 0 == 1
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>::m_a.size() == 0 for: 0 == 0
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>::m_a.size() == 0 for: 0 == 0
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>::m_a.size() == 0 for: 0 == 0
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>::m_a.size() == 0 for: 0 == 0
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>{}.m_a.size() < 2 for: 6 < 2
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>{}.m_a.size() < 2 for: 2 < 2
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>{}.m_a.size() < 2 for: 6 < 2
Class.tests.cpp:<line number>: failed: Template_Fixture_2<TestType>{}.m_a.size() < 2 for: 2 < 2
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>{}.m_a.size() >= 2 for: 6 >= 2
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>{}.m_a.size() >= 2 for: 2 >= 2
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>{}.m_a.size() >= 2 for: 6 >= 2
Class.tests.cpp:<line number>: passed: Template_Fixture_2<TestType>{}.m_a.size() >= 2 for: 2 >= 2
Class.tests.cpp:<line number>: failed: Template_Fixture<TestType>::m_a == 2 for: 1.0 == 2
Class.tests.cpp:<line number>: failed: Template_Fixture<TestType>::m_a == 2 for: 1.0f == 2
Class.tests.cpp:<line number>: failed: Template_Fixture<TestType>::m_a == 2 for: 1 == 2
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1.0 == 1
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1.0f == 1
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1 == 1
Class.tests.cpp:<line number>: failed: Nttp_Fixture<V>::value == 0 for: 1 == 0
Class.tests.cpp:<line number>: failed: Nttp_Fixture<V>::value == 0 for: 3 == 0
Class.tests.cpp:<line number>: failed: Nttp_Fixture<V>::value == 0 for: 6 == 0
Class.tests.cpp:<line number>: passed: Nttp_Fixture<V>::value > 0 for: 1 > 0
Class.tests.cpp:<line number>: passed: Nttp_Fixture<V>::value > 0 for: 3 > 0
Class.tests.cpp:<line number>: passed: Nttp_Fixture<V>::value > 0 for: 6 > 0
Class.tests.cpp:<line number>: failed: m_a == 2 for: 1 == 2
Class.tests.cpp:<line number>: passed: m_a == 1 for: 1 == 1
Misc.tests.cpp:<line number>: passed: x.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: x.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: x.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: x.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: x.size() > 0 for: 42 > 0
Misc.tests.cpp:<line number>: passed: x.size() > 0 for: 9 > 0
Misc.tests.cpp:<line number>: passed: x.size() > 0 for: 42 > 0
Misc.tests.cpp:<line number>: passed: x.size() > 0 for: 9 > 0
Approx.tests.cpp:<line number>: passed: d == 1.23_a for: 1.23 == Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: d != 1.22_a for: 1.23 != Approx( 1.22 )
Approx.tests.cpp:<line number>: passed: -d == -1.23_a for: -1.23 == Approx( -1.23 )
Approx.tests.cpp:<line number>: passed: d == 1.2_a .epsilon(.1) for: 1.23 == Approx( 1.2 )
Approx.tests.cpp:<line number>: passed: d != 1.2_a .epsilon(.001) for: 1.23 != Approx( 1.2 )
Approx.tests.cpp:<line number>: passed: d == 1_a .epsilon(.3) for: 1.23 == Approx( 1.0 )
Misc.tests.cpp:<line number>: passed: with 1 message: 'that's not flying - that's failing in style'
Misc.tests.cpp:<line number>: failed: explicitly with 1 message: 'to infinity and beyond'
Tricky.tests.cpp:<line number>: failed: &o1 == &o2 for: 0x<hex digits> == 0x<hex digits>
Tricky.tests.cpp:<line number>: failed: o1 == o2 for: {?} == {?}
Approx.tests.cpp:<line number>: passed: 104.0 != Approx(100.0) for: 104.0 != Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: 104.0 == Approx(100.0).margin(5) for: 104.0 == Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: 104.0 == Approx(100.0).margin(4) for: 104.0 == Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: 104.0 != Approx(100.0).margin(3) for: 104.0 != Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: 100.3 != Approx(100.0) for: 100.3 != Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: 100.3 == Approx(100.0).margin(0.5) for: 100.3 == Approx( 100.0 )
Tricky.tests.cpp:<line number>: passed: i++ == 7 for: 7 == 7
Tricky.tests.cpp:<line number>: passed: i++ == 8 for: 8 == 8
Exception.tests.cpp:<line number>: passed: 1 == 1
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'unexpected exception'; expression was: {Unknown expression after the reported line}
VariadicMacros.tests.cpp:<line number>: passed: with 1 message: 'anonymous test case'
Approx.tests.cpp:<line number>: passed: Approx(0).margin(0)
Approx.tests.cpp:<line number>: passed: Approx(0).margin(1234656)
Approx.tests.cpp:<line number>: passed: Approx(0).margin(-2), std::domain_error
Approx.tests.cpp:<line number>: passed: Approx(0).epsilon(0)
Approx.tests.cpp:<line number>: passed: Approx(0).epsilon(1)
Approx.tests.cpp:<line number>: passed: Approx(0).epsilon(-0.001), std::domain_error
Approx.tests.cpp:<line number>: passed: Approx(0).epsilon(1.0001), std::domain_error
Approx.tests.cpp:<line number>: passed: 0.25f == Approx(0.0f).margin(0.25f) for: 0.25f == Approx( 0.0 )
Approx.tests.cpp:<line number>: passed: 0.0f == Approx(0.25f).margin(0.25f) for: 0.0f == Approx( 0.25 )
Approx.tests.cpp:<line number>: passed: 0.5f == Approx(0.25f).margin(0.25f) for: 0.5f == Approx( 0.25 )
Approx.tests.cpp:<line number>: passed: 245.0f == Approx(245.25f).margin(0.25f) for: 245.0f == Approx( 245.25 )
Approx.tests.cpp:<line number>: passed: 245.5f == Approx(245.25f).margin(0.25f) for: 245.5f == Approx( 245.25 )
Approx.tests.cpp:<line number>: passed: divide( 22, 7 ) == Approx( 3.141 ).epsilon( 0.001 ) for: 3.1428571429 == Approx( 3.141 )
Approx.tests.cpp:<line number>: passed: divide( 22, 7 ) != Approx( 3.141 ).epsilon( 0.0001 ) for: 3.1428571429 != Approx( 3.141 )
Approx.tests.cpp:<line number>: passed: d != Approx( 1.231 ) for: 1.23 != Approx( 1.231 )
Approx.tests.cpp:<line number>: passed: d == Approx( 1.231 ).epsilon( 0.1 ) for: 1.23 == Approx( 1.231 )
Approx.tests.cpp:<line number>: passed: 1.23f == Approx( 1.23f ) for: 1.23f == Approx( 1.2300000191 )
Approx.tests.cpp:<line number>: passed: 0.0f == Approx( 0.0f ) for: 0.0f == Approx( 0.0 )
Approx.tests.cpp:<line number>: passed: 1 == Approx( 1 ) for: 1 == Approx( 1.0 )
Approx.tests.cpp:<line number>: passed: 0 == Approx( 0 ) for: 0 == Approx( 0.0 )
Approx.tests.cpp:<line number>: passed: 1.0f == Approx( 1 ) for: 1.0f == Approx( 1.0 )
Approx.tests.cpp:<line number>: passed: 0 == Approx( dZero) for: 0 == Approx( 0.0 )
Approx.tests.cpp:<line number>: passed: 0 == Approx( dSmall ).margin( 0.001 ) for: 0 == Approx( 0.00001 )
Approx.tests.cpp:<line number>: passed: 1.234f == Approx( dMedium ) for: 1.234f == Approx( 1.234 )
Approx.tests.cpp:<line number>: passed: dMedium == Approx( 1.234f ) for: 1.234 == Approx( 1.2339999676 )
Matchers.tests.cpp:<line number>: passed: 1, Predicate<int>( alwaysTrue, "always true" ) for: 1 matches predicate: "always true"
Matchers.tests.cpp:<line number>: passed: 1, !Predicate<int>( alwaysFalse, "always false" ) for: 1 not matches predicate: "always false"
Matchers.tests.cpp:<line number>: passed: "Hello olleH", Predicate<std::string>( []( std::string const& str ) -> bool { return str.front() == str.back(); }, "First and last character should be equal" ) for: "Hello olleH" matches predicate: "First and last character should be equal"
Matchers.tests.cpp:<line number>: passed: "This wouldn't pass", !Predicate<std::string>( []( std::string const& str ) -> bool { return str.front() == str.back(); } ) for: "This wouldn't pass" not matches undescribed predicate
Compilation.tests.cpp:<line number>: passed: lhs | rhs for: Val: 1 | Val: 2
Compilation.tests.cpp:<line number>: passed: !(lhs & rhs) for: !(Val: 1 & Val: 2)
Compilation.tests.cpp:<line number>: passed: HasBitOperators{ 1 } & HasBitOperators{ 1 } for: Val: 1 & Val: 1
Compilation.tests.cpp:<line number>: passed: lhs ^ rhs for: Val: 1 ^ Val: 2
Compilation.tests.cpp:<line number>: passed: !(lhs ^ lhs) for: !(Val: 1 ^ Val: 1)
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: true
MatchersRanges.tests.cpp:<line number>: passed: a, Contains(1) for: { 1, 2, 3 } contains element 1
MatchersRanges.tests.cpp:<line number>: passed: b, Contains(1) for: { 0, 1, 2 } contains element 1
MatchersRanges.tests.cpp:<line number>: passed: c, !Contains(1) for: { 4, 5, 6 } not contains element 1
MatchersRanges.tests.cpp:<line number>: passed: a, Contains(0, close_enough) for: { 1, 2, 3 } contains element 0
MatchersRanges.tests.cpp:<line number>: passed: b, Contains(0, close_enough) for: { 0, 1, 2 } contains element 0
MatchersRanges.tests.cpp:<line number>: passed: c, !Contains(0, close_enough) for: { 4, 5, 6 } not contains element 0
MatchersRanges.tests.cpp:<line number>: passed: a, Contains(4, [](auto&& lhs, size_t sz) { return lhs.size() == sz; }) for: { "abc", "abcd", "abcde" } contains element 4
MatchersRanges.tests.cpp:<line number>: passed: in, Contains(1) for: { 1, 2, 3, 4, 5 } contains element 1
MatchersRanges.tests.cpp:<line number>: passed: in, !Contains(8) for: { 1, 2, 3, 4, 5 } not contains element 8
MatchersRanges.tests.cpp:<line number>: passed: in, Contains(MoveOnlyTestElement{ 2 }) for: { 1, 2, 3 } contains element 2
MatchersRanges.tests.cpp:<line number>: passed: in, !Contains(MoveOnlyTestElement{ 9 }) for: { 1, 2, 3 } not contains element 9
MatchersRanges.tests.cpp:<line number>: passed: in, Contains(Catch::Matchers::WithinAbs(0.5, 0.5)) for: { 1.0, 2.0, 3.0, 0.0 } contains element matching is within 0.5 of 0.5
MatchersRanges.tests.cpp:<line number>: passed: empty_array, IsEmpty() for: {  } is empty
MatchersRanges.tests.cpp:<line number>: passed: non_empty_array, !IsEmpty() for: { 0.0 } not is empty
MatchersRanges.tests.cpp:<line number>: passed: empty_vec, IsEmpty() for: {  } is empty
MatchersRanges.tests.cpp:<line number>: passed: non_empty_vec, !IsEmpty() for: { 'a', 'b', 'c' } not is empty
MatchersRanges.tests.cpp:<line number>: passed: inner_lists_are_empty, !IsEmpty() for: { {  } } not is empty
MatchersRanges.tests.cpp:<line number>: passed: inner_lists_are_empty.front(), IsEmpty() for: {  } is empty
MatchersRanges.tests.cpp:<line number>: passed: has_empty{}, !IsEmpty() for: {?} not is empty
MatchersRanges.tests.cpp:<line number>: passed: unrelated::ADL_empty{}, IsEmpty() for: {?} is empty
Message.tests.cpp:<line number>: passed: with 7 messages: 'a := 1' and 'b := 2' and 'c := 3' and 'a + b := 3' and 'a+b := 3' and 'c > b := true' and 'a == 1 := true'
Message.tests.cpp:<line number>: passed: with 7 messages: 'std::vector<int>{1, 2, 3}[0, 1, 2] := 3' and 'std::vector<int>{1, 2, 3}[(0, 1)] := 2' and 'std::vector<int>{1, 2, 3}[0] := 1' and '(helper_1436<int, int>{12, -12}) := { 12, -12 }' and '(helper_1436<int, int>(-12, 12)) := { -12, 12 }' and '(1, 2) := 2' and '(2, 3) := 3'
Message.tests.cpp:<line number>: passed: with 11 messages: '("comma, in string", "escaped, \", ") := "escaped, ", "' and '"single quote in string,'," := "single quote in string,',"' and '"some escapes, \\,\\\\" := "some escapes, \,\\"' and '"some, ), unmatched, } prenheses {[<" := "some, ), unmatched, } prenheses {[<"' and ''"' := '"'' and ''\'' := '''' and '',' := ','' and ''}' := '}'' and '')' := ')'' and ''(' := '('' and ''{' := '{''
ToStringGeneral.tests.cpp:<line number>: passed: true with 1 message: 'i := 2'
ToStringGeneral.tests.cpp:<line number>: passed: true with 1 message: '3'
Details.tests.cpp:<line number>: passed: eq( "", "" ) for: true
Details.tests.cpp:<line number>: passed: !(eq( "", "a" )) for: !false
Details.tests.cpp:<line number>: passed: eq( "a", "a" ) for: true
Details.tests.cpp:<line number>: passed: eq( "a", "A" ) for: true
Details.tests.cpp:<line number>: passed: eq( "A", "a" ) for: true
Details.tests.cpp:<line number>: passed: eq( "A", "A" ) for: true
Details.tests.cpp:<line number>: passed: !(eq( "a", "b" )) for: !false
Details.tests.cpp:<line number>: passed: !(eq( "a", "B" )) for: !false
Details.tests.cpp:<line number>: passed: lt( "", "a" ) for: true
Details.tests.cpp:<line number>: passed: !(lt( "a", "a" )) for: !false
Details.tests.cpp:<line number>: passed: !(lt( "", "" )) for: !false
Details.tests.cpp:<line number>: passed: lt( "a", "b" ) for: true
Details.tests.cpp:<line number>: passed: lt( "a", "B" ) for: true
Details.tests.cpp:<line number>: passed: lt( "A", "b" ) for: true
Details.tests.cpp:<line number>: passed: lt( "A", "B" ) for: true
ToStringGeneral.tests.cpp:<line number>: passed: tab == '\t' for: '\t' == '\t'
ToStringGeneral.tests.cpp:<line number>: passed: newline == '\n' for: '\n' == '\n'
ToStringGeneral.tests.cpp:<line number>: passed: carr_return == '\r' for: '\r' == '\r'
ToStringGeneral.tests.cpp:<line number>: passed: form_feed == '\f' for: '\f' == '\f'
ToStringGeneral.tests.cpp:<line number>: passed: space == ' ' for: ' ' == ' '
ToStringGeneral.tests.cpp:<line number>: passed: c == chars[i] for: 'a' == 'a'
ToStringGeneral.tests.cpp:<line number>: passed: c == chars[i] for: 'z' == 'z'
ToStringGeneral.tests.cpp:<line number>: passed: c == chars[i] for: 'A' == 'A'
ToStringGeneral.tests.cpp:<line number>: passed: c == chars[i] for: 'Z' == 'Z'
ToStringGeneral.tests.cpp:<line number>: passed: null_terminator == '\0' for: 0 == 0
ToStringGeneral.tests.cpp:<line number>: passed: c == i for: 2 == 2
ToStringGeneral.tests.cpp:<line number>: passed: c == i for: 3 == 3
ToStringGeneral.tests.cpp:<line number>: passed: c == i for: 4 == 4
ToStringGeneral.tests.cpp:<line number>: passed: c == i for: 5 == 5
Clara.tests.cpp:<line number>: passed: name.empty() for: true
Clara.tests.cpp:<line number>: passed: name == "foo" for: "foo" == "foo"
Clara.tests.cpp:<line number>: passed: !(parse_result) for: !{?}
Clara.tests.cpp:<line number>: passed: parse_result for: {?}
Clara.tests.cpp:<line number>: passed: res == std::vector<std::string>{ "aaa", "bbb" } for: { "aaa", "bbb" } == { "aaa", "bbb" }
ColourImpl.tests.cpp:<line number>: passed: streamWrapper.str().empty() for: true
ColourImpl.tests.cpp:<line number>: passed: streamWrapper.str() == "1\nUsing code: 2\n2\nUsing code: 0\n3\n" for: "1
Using code: 2
2
Using code: 0
3
"
==
"1
Using code: 2
2
Using code: 0
3
"
ColourImpl.tests.cpp:<line number>: passed: streamWrapper.str() == "Using code: 2\nA\nB\nUsing code: 0\nC\n" for: "Using code: 2
A
B
Using code: 0
C
"
==
"Using code: 2
A
B
Using code: 0
C
"
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( ( MatcherA() && MatcherB() ) && MatcherC() ), Catch::Matchers::Detail:: MatchAllOfGeneric<MatcherA, MatcherB, MatcherC>>::value'
Matchers.tests.cpp:<line number>: passed: 1, ( MatcherA() && MatcherB() ) && MatcherC() for: 1 ( equals: (int) 1 or (string) "1" and equals: (long long) 1 and equals: (T) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( MatcherA() && ( MatcherB() && MatcherC() ) ), Catch::Matchers::Detail:: MatchAllOfGeneric<MatcherA, MatcherB, MatcherC>>::value'
Matchers.tests.cpp:<line number>: passed: 1, MatcherA() && ( MatcherB() && MatcherC() ) for: 1 ( equals: (int) 1 or (string) "1" and equals: (long long) 1 and equals: (T) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( ( MatcherA() && MatcherB() ) && ( MatcherC() && MatcherD() ) ), Catch::Matchers::Detail:: MatchAllOfGeneric<MatcherA, MatcherB, MatcherC, MatcherD>>:: value'
Matchers.tests.cpp:<line number>: passed: 1, ( MatcherA() && MatcherB() ) && ( MatcherC() && MatcherD() ) for: 1 ( equals: (int) 1 or (string) "1" and equals: (long long) 1 and equals: (T) 1 and equals: true )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( ( MatcherA() || MatcherB() ) || MatcherC() ), Catch::Matchers::Detail:: MatchAnyOfGeneric<MatcherA, MatcherB, MatcherC>>::value'
Matchers.tests.cpp:<line number>: passed: 1, ( MatcherA() || MatcherB() ) || MatcherC() for: 1 ( equals: (int) 1 or (string) "1" or equals: (long long) 1 or equals: (T) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( MatcherA() || ( MatcherB() || MatcherC() ) ), Catch::Matchers::Detail:: MatchAnyOfGeneric<MatcherA, MatcherB, MatcherC>>::value'
Matchers.tests.cpp:<line number>: passed: 1, MatcherA() || ( MatcherB() || MatcherC() ) for: 1 ( equals: (int) 1 or (string) "1" or equals: (long long) 1 or equals: (T) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( ( MatcherA() || MatcherB() ) || ( MatcherC() || MatcherD() ) ), Catch::Matchers::Detail:: MatchAnyOfGeneric<MatcherA, MatcherB, MatcherC, MatcherD>>:: value'
Matchers.tests.cpp:<line number>: passed: 1, ( MatcherA() || MatcherB() ) || ( MatcherC() || MatcherD() ) for: 1 ( equals: (int) 1 or (string) "1" or equals: (long long) 1 or equals: (T) 1 or equals: true )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( !MatcherA() ), Catch::Matchers::Detail::MatchNotOfGeneric<MatcherA>>::value'
Matchers.tests.cpp:<line number>: passed: 0, !MatcherA() for: 0 not equals: (int) 1 or (string) "1"
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same<decltype( !!MatcherA() ), MatcherA const&>::value'
Matchers.tests.cpp:<line number>: passed: 1, !!MatcherA() for: 1 equals: (int) 1 or (string) "1"
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( !!!MatcherA() ), Catch::Matchers::Detail::MatchNotOfGeneric<MatcherA>>::value'
Matchers.tests.cpp:<line number>: passed: 0, !!!MatcherA() for: 0 not equals: (int) 1 or (string) "1"
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same<decltype( !!!!MatcherA() ), MatcherA const&>::value'
Matchers.tests.cpp:<line number>: passed: 1, !!!!MatcherA() for: 1 equals: (int) 1 or (string) "1"
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same<decltype( StartsWith( "foo" ) || ( StartsWith( "bar" ) && EndsWith( "bar" ) && !EndsWith( "foo" ) ) ), Catch::Matchers::Detail::MatchAnyOf<std::string>>::value'
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same<decltype( MatcherA() || MatcherB() ), Catch::Matchers::Detail:: MatchAnyOfGeneric<MatcherA, MatcherB>>::value'
Matchers.tests.cpp:<line number>: passed: 1, MatcherA() || MatcherB() for: 1 ( equals: (int) 1 or (string) "1" or equals: (long long) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same<decltype( MatcherA() && MatcherB() ), Catch::Matchers::Detail:: MatchAllOfGeneric<MatcherA, MatcherB>>::value'
Matchers.tests.cpp:<line number>: passed: 1, MatcherA() && MatcherB() for: 1 ( equals: (int) 1 or (string) "1" and equals: (long long) 1 )
Matchers.tests.cpp:<line number>: passed: with 1 message: 'std::is_same< decltype( MatcherA() || !MatcherB() ), Catch::Matchers::Detail::MatchAnyOfGeneric< MatcherA, Catch::Matchers::Detail::MatchNotOfGeneric<MatcherB>>>::value'
Matchers.tests.cpp:<line number>: passed: 1, MatcherA() || !MatcherB() for: 1 ( equals: (int) 1 or (string) "1" or not equals: (long long) 1 )
Matchers.tests.cpp:<line number>: passed: vec, Predicate<std::vector<int>>( []( auto const& v ) { return std::all_of( v.begin(), v.end(), []( int elem ) { return elem % 2 == 1; } ); }, "All elements are odd" ) && !EqualsRange( a ) for: { 1, 3, 5 } ( matches predicate: "All elements are odd" and not Equals: { 5, 3, 1 } )
Matchers.tests.cpp:<line number>: passed: str, StartsWith( "foo" ) && EqualsRange( arr ) && EndsWith( "bar" ) for: "foobar" ( starts with: "foo" and Equals: { 'f', 'o', 'o', 'b', 'a', 'r' } and ends with: "bar" )
Matchers.tests.cpp:<line number>: passed: str, StartsWith( "foo" ) && !EqualsRange( bad_arr ) && EndsWith( "bar" ) for: "foobar" ( starts with: "foo" and not Equals: { 'o', 'o', 'f', 'b', 'a', 'r' } and ends with: "bar" )
Matchers.tests.cpp:<line number>: passed: str, EqualsRange( arr ) && StartsWith( "foo" ) && EndsWith( "bar" ) for: "foobar" ( Equals: { 'f', 'o', 'o', 'b', 'a', 'r' } and starts with: "foo" and ends with: "bar" )
Matchers.tests.cpp:<line number>: passed: str, !EqualsRange( bad_arr ) && StartsWith( "foo" ) && EndsWith( "bar" ) for: "foobar" ( not Equals: { 'o', 'o', 'f', 'b', 'a', 'r' } and starts with: "foo" and ends with: "bar" )
Matchers.tests.cpp:<line number>: passed: str, EqualsRange( bad_arr ) || ( StartsWith( "foo" ) && EndsWith( "bar" ) ) for: "foobar" ( Equals: { 'o', 'o', 'f', 'b', 'a', 'r' } or ( starts with: "foo" and ends with: "bar" ) )
Matchers.tests.cpp:<line number>: passed: str, ( StartsWith( "foo" ) && EndsWith( "bar" ) ) || EqualsRange( bad_arr ) for: "foobar" ( ( starts with: "foo" and ends with: "bar" ) or Equals: { 'o', 'o', 'f', 'b', 'a', 'r' } )
Matchers.tests.cpp:<line number>: passed: container, EqualsRange( a ) || EqualsRange( b ) || EqualsRange( c ) for: { 1, 2, 3 } ( Equals: { 1, 2, 3 } or Equals: { 0, 1, 2 } or Equals: { 4, 5, 6 } )
Tricky.tests.cpp:<line number>: passed: std::vector<constructor_throws>{constructor_throws{}, constructor_throws{}}
Tricky.tests.cpp:<line number>: passed: std::vector<constructor_throws>{constructor_throws{}, constructor_throws{}}
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2, 3} == std::vector<int>{1, 2, 3}
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2, 3} == std::vector<int>{1, 2, 3}
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2} == std::vector<int>{1, 2} for: { 1, 2 } == { 1, 2 }
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2} == std::vector<int>{1, 2} for: { 1, 2 } == { 1, 2 }
Tricky.tests.cpp:<line number>: passed: !(std::vector<int>{1, 2} == std::vector<int>{1, 2, 3}) for: !({ 1, 2 } == { 1, 2, 3 })
Tricky.tests.cpp:<line number>: passed: !(std::vector<int>{1, 2} == std::vector<int>{1, 2, 3}) for: !({ 1, 2 } == { 1, 2, 3 })
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2} == std::vector<int>{1, 2} for: { 1, 2 } == { 1, 2 }
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2} == std::vector<int>{1, 2} for: { 1, 2 } == { 1, 2 }
Tricky.tests.cpp:<line number>: passed: true
Tricky.tests.cpp:<line number>: passed: std::vector<int>{1, 2} == std::vector<int>{1, 2} for: { 1, 2 } == { 1, 2 }
Tricky.tests.cpp:<line number>: passed: a for: 0x<hex digits>
Tricky.tests.cpp:<line number>: passed: a == &foo for: 0x<hex digits> == 0x<hex digits>
RandomNumberGeneration.tests.cpp:<line number>: passed: SimplePcg32{} == SimplePcg32{} for: {?} == {?}
RandomNumberGeneration.tests.cpp:<line number>: passed: SimplePcg32{ 0 } != SimplePcg32{} for: {?} != {?}
RandomNumberGeneration.tests.cpp:<line number>: passed: !(SimplePcg32{ 1 } == SimplePcg32{ 2 }) for: !({?} == {?})
RandomNumberGeneration.tests.cpp:<line number>: passed: !(SimplePcg32{ 1 } != SimplePcg32{ 1 }) for: !({?} != {?})
Approx.tests.cpp:<line number>: passed: td == Approx(10.0) for: StrongDoubleTypedef(10) == Approx( 10.0 )
Approx.tests.cpp:<line number>: passed: Approx(10.0) == td for: Approx( 10.0 ) == StrongDoubleTypedef(10)
Approx.tests.cpp:<line number>: passed: td != Approx(11.0) for: StrongDoubleTypedef(10) != Approx( 11.0 )
Approx.tests.cpp:<line number>: passed: Approx(11.0) != td for: Approx( 11.0 ) != StrongDoubleTypedef(10)
Approx.tests.cpp:<line number>: passed: td <= Approx(10.0) for: StrongDoubleTypedef(10) <= Approx( 10.0 )
Approx.tests.cpp:<line number>: passed: td <= Approx(11.0) for: StrongDoubleTypedef(10) <= Approx( 11.0 )
Approx.tests.cpp:<line number>: passed: Approx(10.0) <= td for: Approx( 10.0 ) <= StrongDoubleTypedef(10)
Approx.tests.cpp:<line number>: passed: Approx(9.0) <= td for: Approx( 9.0 ) <= StrongDoubleTypedef(10)
Approx.tests.cpp:<line number>: passed: td >= Approx(9.0) for: StrongDoubleTypedef(10) >= Approx( 9.0 )
Approx.tests.cpp:<line number>: passed: td >= Approx(td) for: StrongDoubleTypedef(10) >= Approx( 10.0 )
Approx.tests.cpp:<line number>: passed: Approx(td) >= td for: Approx( 10.0 ) >= StrongDoubleTypedef(10)
Approx.tests.cpp:<line number>: passed: Approx(11.0) >= td for: Approx( 11.0 ) >= StrongDoubleTypedef(10)
Condition.tests.cpp:<line number>: passed: 54 == 6*9 for: 54 == 54
Condition.tests.cpp:<line number>: passed: ( -1 > 2u ) for: true
Condition.tests.cpp:<line number>: passed: -1 > 2u for: -1 > 2
Condition.tests.cpp:<line number>: passed: ( 2u < -1 ) for: true
Condition.tests.cpp:<line number>: passed: 2u < -1 for: 2 < -1
Condition.tests.cpp:<line number>: passed: ( minInt > 2u ) for: true
Condition.tests.cpp:<line number>: passed: minInt > 2u for: -2147483648 > 2
Condition.tests.cpp:<line number>: passed: i == 1 for: 1 == 1
Condition.tests.cpp:<line number>: passed: ui == 2 for: 2 == 2
Condition.tests.cpp:<line number>: passed: l == 3 for: 3 == 3
Condition.tests.cpp:<line number>: passed: ul == 4 for: 4 == 4
Condition.tests.cpp:<line number>: passed: c == 5 for: 5 == 5
Condition.tests.cpp:<line number>: passed: uc == 6 for: 6 == 6
Condition.tests.cpp:<line number>: passed: 1 == i for: 1 == 1
Condition.tests.cpp:<line number>: passed: 2 == ui for: 2 == 2
Condition.tests.cpp:<line number>: passed: 3 == l for: 3 == 3
Condition.tests.cpp:<line number>: passed: 4 == ul for: 4 == 4
Condition.tests.cpp:<line number>: passed: 5 == c for: 5 == 5
Condition.tests.cpp:<line number>: passed: 6 == uc for: 6 == 6
Condition.tests.cpp:<line number>: passed: (std::numeric_limits<uint32_t>::max)() > ul for: 4294967295 (0x<hex digits>) > 4
Matchers.tests.cpp:<line number>: passed: !(matcher.match( 1 )) for: !false
Matchers.tests.cpp:<line number>: passed: first.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: !second.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: matcher.match( 1 ) for: true
Matchers.tests.cpp:<line number>: passed: first.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: !second.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: !(matcher.match( 1 )) for: !false
Matchers.tests.cpp:<line number>: passed: first.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: !second.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: matcher.match( 1 ) for: true
Matchers.tests.cpp:<line number>: passed: first.matchCalled for: true
Matchers.tests.cpp:<line number>: passed: !second.matchCalled for: true
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), ContainsSubstring( "not there", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" contains: "not there" (case insensitive)
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), ContainsSubstring( "STRING" ) for: "this string contains 'abc' as a substring" contains: "STRING"
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: elem % 2 == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: call_count == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: make_data().size() == test_count for: 6 == 6
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "-" )->isConsole() for: true
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'custom exception - not std'; expression was: throwCustom()
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'custom exception - not std'; expression was: throwCustom(), std::exception
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'custom std exception'
Approx.tests.cpp:<line number>: passed: 101.000001 != Approx(100).epsilon(0.01) for: 101.000001 != Approx( 100.0 )
Approx.tests.cpp:<line number>: passed: std::pow(10, -5) != Approx(std::pow(10, -7)) for: 0.00001 != Approx( 0.0000001 )
ToString.tests.cpp:<line number>: passed: enumInfo->lookup(0) == "Value1" for: Value1 == "Value1"
ToString.tests.cpp:<line number>: passed: enumInfo->lookup(1) == "Value2" for: Value2 == "Value2"
ToString.tests.cpp:<line number>: passed: enumInfo->lookup(3) == "{** unexpected enum value **}" for: {** unexpected enum value **}
==
"{** unexpected enum value **}"
Skip.tests.cpp:<line number>: skipped: 'This generator is empty'
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "" )->isConsole() for: true
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), EndsWith( "Substring" ) for: "this string contains 'abc' as a substring" ends with: "Substring"
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), EndsWith( "this", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" ends with: "this" (case insensitive)
EnumToString.tests.cpp:<line number>: passed: stringify( EnumClass3::Value1 ) == "Value1" for: "Value1" == "Value1"
EnumToString.tests.cpp:<line number>: passed: stringify( EnumClass3::Value2 ) == "Value2" for: "Value2" == "Value2"
EnumToString.tests.cpp:<line number>: passed: stringify( EnumClass3::Value3 ) == "Value3" for: "Value3" == "Value3"
EnumToString.tests.cpp:<line number>: passed: stringify( EnumClass3::Value4 ) == "{** unexpected enum value **}" for: "{** unexpected enum value **}"
==
"{** unexpected enum value **}"
EnumToString.tests.cpp:<line number>: passed: stringify( ec3 ) == "Value2" for: "Value2" == "Value2"
EnumToString.tests.cpp:<line number>: passed: stringify( Bikeshed::Colours::Red ) == "Red" for: "Red" == "Red"
EnumToString.tests.cpp:<line number>: passed: stringify( Bikeshed::Colours::Blue ) == "Blue" for: "Blue" == "Blue"
Approx.tests.cpp:<line number>: passed: 101.01 != Approx(100).epsilon(0.01) for: 101.01 != Approx( 100.0 )
Condition.tests.cpp:<line number>: failed: data.int_seven == 6 for: 7 == 6
Condition.tests.cpp:<line number>: failed: data.int_seven == 8 for: 7 == 8
Condition.tests.cpp:<line number>: failed: data.int_seven == 0 for: 7 == 0
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one == Approx( 9.11f ) for: 9.1f == Approx( 9.1099996567 )
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one == Approx( 9.0f ) for: 9.1f == Approx( 9.0 )
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one == Approx( 1 ) for: 9.1f == Approx( 1.0 )
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one == Approx( 0 ) for: 9.1f == Approx( 0.0 )
Condition.tests.cpp:<line number>: failed: data.double_pi == Approx( 3.1415 ) for: 3.1415926535 == Approx( 3.1415 )
Condition.tests.cpp:<line number>: failed: data.str_hello == "goodbye" for: "hello" == "goodbye"
Condition.tests.cpp:<line number>: failed: data.str_hello == "hell" for: "hello" == "hell"
Condition.tests.cpp:<line number>: failed: data.str_hello == "hello1" for: "hello" == "hello1"
Condition.tests.cpp:<line number>: failed: data.str_hello.size() == 6 for: 5 == 6
Condition.tests.cpp:<line number>: failed: x == Approx( 1.301 ) for: 1.3 == Approx( 1.301 )
Condition.tests.cpp:<line number>: passed: data.int_seven == 7 for: 7 == 7
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one == Approx( 9.1f ) for: 9.1f == Approx( 9.1000003815 )
Condition.tests.cpp:<line number>: passed: data.double_pi == Approx( 3.1415926535 ) for: 3.1415926535 == Approx( 3.1415926535 )
Condition.tests.cpp:<line number>: passed: data.str_hello == "hello" for: "hello" == "hello"
Condition.tests.cpp:<line number>: passed: "hello" == data.str_hello for: "hello" == "hello"
Condition.tests.cpp:<line number>: passed: data.str_hello.size() == 5 for: 5 == 5
Condition.tests.cpp:<line number>: passed: x == Approx( 1.3 ) for: 1.3 == Approx( 1.3 )
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), Equals( "this string contains 'abc' as a substring" ) for: "this string contains 'abc' as a substring" equals: "this string contains 'abc' as a substring"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), Equals( "this string contains 'ABC' as a substring", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" equals: "this string contains 'abc' as a substring" (case insensitive)
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), Equals( "this string contains 'ABC' as a substring" ) for: "this string contains 'abc' as a substring" equals: "this string contains 'ABC' as a substring"
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), Equals( "something else", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" equals: "something else" (case insensitive)
ToStringGeneral.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(WhatException{}) == "This exception has overridden what() method" for: "This exception has overridden what() method"
==
"This exception has overridden what() method"
ToStringGeneral.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(OperatorException{}) == "OperatorException" for: "OperatorException" == "OperatorException"
ToStringGeneral.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(StringMakerException{}) == "StringMakerException" for: "StringMakerException"
==
"StringMakerException"
Matchers.tests.cpp:<line number>: failed: expected exception, got none; expression was: doesNotThrow(), SpecialException, ExceptionMatcher{ 1 }
Matchers.tests.cpp:<line number>: failed: expected exception, got none; expression was: doesNotThrow(), SpecialException, ExceptionMatcher{ 1 }
Matchers.tests.cpp:<line number>: failed: unexpected exception with message: 'Unknown exception'; expression was: throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 }
Matchers.tests.cpp:<line number>: failed: unexpected exception with message: 'Unknown exception'; expression was: throwsAsInt( 1 ), SpecialException, ExceptionMatcher{ 1 }
Matchers.tests.cpp:<line number>: failed: throwsSpecialException( 3 ), SpecialException, ExceptionMatcher{ 1 } for: SpecialException::what special exception has value of 1
Matchers.tests.cpp:<line number>: failed: throwsSpecialException( 4 ), SpecialException, ExceptionMatcher{ 1 } for: SpecialException::what special exception has value of 1
Matchers.tests.cpp:<line number>: passed: throwsSpecialException( 1 ), SpecialException, ExceptionMatcher{ 1 } for: SpecialException::what special exception has value of 1
Matchers.tests.cpp:<line number>: passed: throwsSpecialException( 2 ), SpecialException, ExceptionMatcher{ 2 } for: SpecialException::what special exception has value of 2
Matchers.tests.cpp:<line number>: passed: throwsDerivedException(), DerivedException, MessageMatches( StartsWith( "Derived" ) ) for: DerivedException::what  matches "starts with: "Derived""
Matchers.tests.cpp:<line number>: passed: throwsDerivedException(), DerivedException, MessageMatches( EndsWith( "::what" ) ) for: DerivedException::what  matches "ends with: "::what""
Matchers.tests.cpp:<line number>: passed: throwsDerivedException(), DerivedException, MessageMatches( !StartsWith( "::what" ) ) for: DerivedException::what  matches "not starts with: "::what""
Matchers.tests.cpp:<line number>: passed: throwsSpecialException( 2 ), SpecialException, MessageMatches( StartsWith( "Special" ) ) for: SpecialException::what  matches "starts with: "Special""
Exception.tests.cpp:<line number>: passed: thisThrows(), "expected exception" for: "expected exception" equals: "expected exception"
Exception.tests.cpp:<line number>: passed: thisThrows(), Equals( "expecteD Exception", Catch::CaseSensitive::No ) for: "expected exception" equals: "expected exception" (case insensitive)
Exception.tests.cpp:<line number>: passed: thisThrows(), StartsWith( "expected" ) for: "expected exception" starts with: "expected"
Exception.tests.cpp:<line number>: passed: thisThrows(), EndsWith( "exception" ) for: "expected exception" ends with: "exception"
Exception.tests.cpp:<line number>: passed: thisThrows(), ContainsSubstring( "except" ) for: "expected exception" contains: "except"
Exception.tests.cpp:<line number>: passed: thisThrows(), ContainsSubstring( "exCept", Catch::CaseSensitive::No ) for: "expected exception" contains: "except" (case insensitive)
Matchers.tests.cpp:<line number>: passed: throwsDerivedException(), DerivedException, Message( "DerivedException::what" ) for: DerivedException::what exception message matches "DerivedException::what"
Matchers.tests.cpp:<line number>: passed: throwsDerivedException(), DerivedException, !Message( "derivedexception::what" ) for: DerivedException::what not exception message matches "derivedexception::what"
Matchers.tests.cpp:<line number>: passed: throwsSpecialException( 2 ), SpecialException, !Message( "DerivedException::what" ) for: SpecialException::what not exception message matches "DerivedException::what"
Matchers.tests.cpp:<line number>: passed: throwsSpecialException( 2 ), SpecialException, Message( "SpecialException::what" ) for: SpecialException::what exception message matches "SpecialException::what"
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'expected exception'; expression was: thisThrows(), std::string
Exception.tests.cpp:<line number>: failed: expected exception, got none; expression was: thisDoesntThrow(), std::domain_error
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'expected exception'; expression was: thisThrows()
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'This is a failure'
Message.tests.cpp:<line number>: failed: explicitly
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'This is a failure'
Message.tests.cpp:<line number>: warning: 'This message appears in the output'
Misc.tests.cpp:<line number>: passed: Factorial(0) == 1 for: 1 == 1
Misc.tests.cpp:<line number>: passed: Factorial(1) == 1 for: 1 == 1
Misc.tests.cpp:<line number>: passed: Factorial(2) == 2 for: 2 == 2
Misc.tests.cpp:<line number>: passed: Factorial(3) == 6 for: 6 == 6
Misc.tests.cpp:<line number>: passed: Factorial(10) == 3628800 for: 3628800 (0x<hex digits>) == 3628800 (0x<hex digits>)
GeneratorsImpl.tests.cpp:<line number>: passed: filter( []( int ) { return false; }, value( 3 ) ), Catch::GeneratorException
Matchers.tests.cpp:<line number>: passed: 10., WithinRel( 11.1, 0.1 ) for: 10.0 and 11.1 are within 10% of each other
Matchers.tests.cpp:<line number>: passed: 10., !WithinRel( 11.2, 0.1 ) for: 10.0 not and 11.2 are within 10% of each other
Matchers.tests.cpp:<line number>: passed: 1., !WithinRel( 0., 0.99 ) for: 1.0 not and 0 are within 99% of each other
Matchers.tests.cpp:<line number>: passed: -0., WithinRel( 0. ) for: -0.0 and 0 are within 2.22045e-12% of each other
Matchers.tests.cpp:<line number>: passed: v1, WithinRel( v2 ) for: 0.0 and 2.22507e-308 are within 2.22045e-12% of each other
Matchers.tests.cpp:<line number>: passed: 1., WithinAbs( 1., 0 ) for: 1.0 is within 0.0 of 1.0
Matchers.tests.cpp:<line number>: passed: 0., WithinAbs( 1., 1 ) for: 0.0 is within 1.0 of 1.0
Matchers.tests.cpp:<line number>: passed: 0., !WithinAbs( 1., 0.99 ) for: 0.0 not is within 0.99 of 1.0
Matchers.tests.cpp:<line number>: passed: 0., !WithinAbs( 1., 0.99 ) for: 0.0 not is within 0.99 of 1.0
Matchers.tests.cpp:<line number>: passed: 11., !WithinAbs( 10., 0.5 ) for: 11.0 not is within 0.5 of 10.0
Matchers.tests.cpp:<line number>: passed: 10., !WithinAbs( 11., 0.5 ) for: 10.0 not is within 0.5 of 11.0
Matchers.tests.cpp:<line number>: passed: -10., WithinAbs( -10., 0.5 ) for: -10.0 is within 0.5 of -10.0
Matchers.tests.cpp:<line number>: passed: -10., WithinAbs( -9.6, 0.5 ) for: -10.0 is within 0.5 of -9.6
Matchers.tests.cpp:<line number>: passed: 1., WithinULP( 1., 0 ) for: 1.0 is within 0 ULPs of 1.0000000000000000e+00 ([1.0000000000000000e+00, 1.0000000000000000e+00])
Matchers.tests.cpp:<line number>: passed: nextafter( 1., 2. ), WithinULP( 1., 1 ) for: 1.0 is within 1 ULPs of 1.0000000000000000e+00 ([9.9999999999999989e-01, 1.0000000000000002e+00])
Matchers.tests.cpp:<line number>: passed: 0., WithinULP( nextafter( 0., 1. ), 1 ) for: 0.0 is within 1 ULPs of 4.9406564584124654e-324 ([0.0000000000000000e+00, 9.8813129168249309e-324])
Matchers.tests.cpp:<line number>: passed: 1., WithinULP( nextafter( 1., 0. ), 1 ) for: 1.0 is within 1 ULPs of 9.9999999999999989e-01 ([9.9999999999999978e-01, 1.0000000000000000e+00])
Matchers.tests.cpp:<line number>: passed: 1., !WithinULP( nextafter( 1., 2. ), 0 ) for: 1.0 not is within 0 ULPs of 1.0000000000000002e+00 ([1.0000000000000002e+00, 1.0000000000000002e+00])
Matchers.tests.cpp:<line number>: passed: 1., WithinULP( 1., 0 ) for: 1.0 is within 0 ULPs of 1.0000000000000000e+00 ([1.0000000000000000e+00, 1.0000000000000000e+00])
Matchers.tests.cpp:<line number>: passed: -0., WithinULP( 0., 0 ) for: -0.0 is within 0 ULPs of 0.0000000000000000e+00 ([0.0000000000000000e+00, 0.0000000000000000e+00])
Matchers.tests.cpp:<line number>: passed: 1., WithinAbs( 1., 0.5 ) || WithinULP( 2., 1 ) for: 1.0 ( is within 0.5 of 1.0 or is within 1 ULPs of 2.0000000000000000e+00 ([1.9999999999999998e+00, 2.0000000000000004e+00]) )
Matchers.tests.cpp:<line number>: passed: 1., WithinAbs( 2., 0.5 ) || WithinULP( 1., 0 ) for: 1.0 ( is within 0.5 of 2.0 or is within 0 ULPs of 1.0000000000000000e+00 ([1.0000000000000000e+00, 1.0000000000000000e+00]) )
Matchers.tests.cpp:<line number>: passed: 0.0001, WithinAbs( 0., 0.001 ) || WithinRel( 0., 0.1 ) for: 0.0001 ( is within 0.001 of 0.0 or and 0 are within 10% of each other )
Matchers.tests.cpp:<line number>: passed: WithinAbs( 1., 0. )
Matchers.tests.cpp:<line number>: passed: WithinAbs( 1., -1. ), std::domain_error
Matchers.tests.cpp:<line number>: passed: WithinULP( 1., 0 )
Matchers.tests.cpp:<line number>: passed: WithinRel( 1., 0. )
Matchers.tests.cpp:<line number>: passed: WithinRel( 1., -0.2 ), std::domain_error
Matchers.tests.cpp:<line number>: passed: WithinRel( 1., 1. ), std::domain_error
Matchers.tests.cpp:<line number>: passed: 1., !IsNaN() for: 1.0 not is NaN
Matchers.tests.cpp:<line number>: passed: 10.f, WithinRel( 11.1f, 0.1f ) for: 10.0f and 11.1 are within 10% of each other
Matchers.tests.cpp:<line number>: passed: 10.f, !WithinRel( 11.2f, 0.1f ) for: 10.0f not and 11.2 are within 10% of each other
Matchers.tests.cpp:<line number>: passed: 1.f, !WithinRel( 0.f, 0.99f ) for: 1.0f not and 0 are within 99% of each other
Matchers.tests.cpp:<line number>: passed: -0.f, WithinRel( 0.f ) for: -0.0f and 0 are within 0.00119209% of each other
Matchers.tests.cpp:<line number>: passed: v1, WithinRel( v2 ) for: 0.0f and 1.17549e-38 are within 0.00119209% of each other
Matchers.tests.cpp:<line number>: passed: 1.f, WithinAbs( 1.f, 0 ) for: 1.0f is within 0.0 of 1.0
Matchers.tests.cpp:<line number>: passed: 0.f, WithinAbs( 1.f, 1 ) for: 0.0f is within 1.0 of 1.0
Matchers.tests.cpp:<line number>: passed: 0.f, !WithinAbs( 1.f, 0.99f ) for: 0.0f not is within 0.9900000095 of 1.0
Matchers.tests.cpp:<line number>: passed: 0.f, !WithinAbs( 1.f, 0.99f ) for: 0.0f not is within 0.9900000095 of 1.0
Matchers.tests.cpp:<line number>: passed: 0.f, WithinAbs( -0.f, 0 ) for: 0.0f is within 0.0 of -0.0
Matchers.tests.cpp:<line number>: passed: 11.f, !WithinAbs( 10.f, 0.5f ) for: 11.0f not is within 0.5 of 10.0
Matchers.tests.cpp:<line number>: passed: 10.f, !WithinAbs( 11.f, 0.5f ) for: 10.0f not is within 0.5 of 11.0
Matchers.tests.cpp:<line number>: passed: -10.f, WithinAbs( -10.f, 0.5f ) for: -10.0f is within 0.5 of -10.0
Matchers.tests.cpp:<line number>: passed: -10.f, WithinAbs( -9.6f, 0.5f ) for: -10.0f is within 0.5 of -9.6000003815
Matchers.tests.cpp:<line number>: passed: 1.f, WithinULP( 1.f, 0 ) for: 1.0f is within 0 ULPs of 1.00000000e+00f ([1.00000000e+00, 1.00000000e+00])
Matchers.tests.cpp:<line number>: passed: -1.f, WithinULP( -1.f, 0 ) for: -1.0f is within 0 ULPs of -1.00000000e+00f ([-1.00000000e+00, -1.00000000e+00])
Matchers.tests.cpp:<line number>: passed: nextafter( 1.f, 2.f ), WithinULP( 1.f, 1 ) for: 1.0f is within 1 ULPs of 1.00000000e+00f ([9.99999940e-01, 1.00000012e+00])
Matchers.tests.cpp:<line number>: passed: 0.f, WithinULP( nextafter( 0.f, 1.f ), 1 ) for: 0.0f is within 1 ULPs of 1.40129846e-45f ([0.00000000e+00, 2.80259693e-45])
Matchers.tests.cpp:<line number>: passed: 1.f, WithinULP( nextafter( 1.f, 0.f ), 1 ) for: 1.0f is within 1 ULPs of 9.99999940e-01f ([9.99999881e-01, 1.00000000e+00])
Matchers.tests.cpp:<line number>: passed: 1.f, !WithinULP( nextafter( 1.f, 2.f ), 0 ) for: 1.0f not is within 0 ULPs of 1.00000012e+00f ([1.00000012e+00, 1.00000012e+00])
Matchers.tests.cpp:<line number>: passed: 1.f, WithinULP( 1.f, 0 ) for: 1.0f is within 0 ULPs of 1.00000000e+00f ([1.00000000e+00, 1.00000000e+00])
Matchers.tests.cpp:<line number>: passed: -0.f, WithinULP( 0.f, 0 ) for: -0.0f is within 0 ULPs of 0.00000000e+00f ([0.00000000e+00, 0.00000000e+00])
Matchers.tests.cpp:<line number>: passed: 1.f, WithinAbs( 1.f, 0.5 ) || WithinULP( 1.f, 1 ) for: 1.0f ( is within 0.5 of 1.0 or is within 1 ULPs of 1.00000000e+00f ([9.99999940e-01, 1.00000012e+00]) )
Matchers.tests.cpp:<line number>: passed: 1.f, WithinAbs( 2.f, 0.5 ) || WithinULP( 1.f, 0 ) for: 1.0f ( is within 0.5 of 2.0 or is within 0 ULPs of 1.00000000e+00f ([1.00000000e+00, 1.00000000e+00]) )
Matchers.tests.cpp:<line number>: passed: 0.0001f, WithinAbs( 0.f, 0.001f ) || WithinRel( 0.f, 0.1f ) for: 0.0001f ( is within 0.001 of 0.0 or and 0 are within 10% of each other )
Matchers.tests.cpp:<line number>: passed: WithinAbs( 1.f, 0.f )
Matchers.tests.cpp:<line number>: passed: WithinAbs( 1.f, -1.f ), std::domain_error
Matchers.tests.cpp:<line number>: passed: WithinULP( 1.f, 0 )
Matchers.tests.cpp:<line number>: passed: WithinULP( 1.f, static_cast<uint64_t>( -1 ) ), std::domain_error
Matchers.tests.cpp:<line number>: passed: WithinRel( 1.f, 0.f )
Matchers.tests.cpp:<line number>: passed: WithinRel( 1.f, -0.2f ), std::domain_error
Matchers.tests.cpp:<line number>: passed: WithinRel( 1.f, 1.f ), std::domain_error
Matchers.tests.cpp:<line number>: passed: 1., !IsNaN() for: 1.0 not is NaN
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: filter([] (int) {return false; }, value(1)), Catch::GeneratorException
Generators.tests.cpp:<line number>: passed: i < 4 for: 1 < 4
Generators.tests.cpp:<line number>: passed: i < 4 for: 2 < 4
Generators.tests.cpp:<line number>: passed: i < 4 for: 3 < 4
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: i % 2 == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: i.size() == 1 for: 1 == 1
Generators.tests.cpp:<line number>: passed: j > 0 for: 1 > 0
Generators.tests.cpp:<line number>: passed: j > 0 for: 2 > 0
Generators.tests.cpp:<line number>: passed: j > 0 for: 3 > 0
Generators.tests.cpp:<line number>: passed: j > 0 for: 1 > 0
Generators.tests.cpp:<line number>: passed: j > 0 for: 2 > 0
Generators.tests.cpp:<line number>: passed: j > 0 for: 3 > 0
Generators.tests.cpp:<line number>: passed: chunk2.size() == 2 for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() == chunk2.back() for: 1 == 1
Generators.tests.cpp:<line number>: passed: chunk2.size() == 2 for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() == chunk2.back() for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.size() == 2 for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() == chunk2.back() for: 3 == 3
Generators.tests.cpp:<line number>: passed: chunk2.size() == 2 for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() == chunk2.back() for: 1 == 1
Generators.tests.cpp:<line number>: passed: chunk2.front() < 3 for: 1 < 3
Generators.tests.cpp:<line number>: passed: chunk2.size() == 2 for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() == chunk2.back() for: 2 == 2
Generators.tests.cpp:<line number>: passed: chunk2.front() < 3 for: 2 < 3
Generators.tests.cpp:<line number>: passed: chunk2.size() == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: chunk2.size() == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: chunk2.size() == 0 for: 0 == 0
Generators.tests.cpp:<line number>: passed: chunk(2, value(1)), Catch::GeneratorException
Generators.tests.cpp:<line number>: passed: j < i for: -3 < 1
Generators.tests.cpp:<line number>: passed: j < i for: -2 < 1
Generators.tests.cpp:<line number>: passed: j < i for: -1 < 1
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 4 > 1
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 4 > 2
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 4 > 3
Generators.tests.cpp:<line number>: passed: j < i for: -3 < 2
Generators.tests.cpp:<line number>: passed: j < i for: -2 < 2
Generators.tests.cpp:<line number>: passed: j < i for: -1 < 2
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 8 > 1
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 8 > 2
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 8 > 3
Generators.tests.cpp:<line number>: passed: j < i for: -3 < 3
Generators.tests.cpp:<line number>: passed: j < i for: -2 < 3
Generators.tests.cpp:<line number>: passed: j < i for: -1 < 3
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 12 > 1
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 12 > 2
Generators.tests.cpp:<line number>: passed: 4u * i > str.size() for: 12 > 3
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 123 for: 123 == 123
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 4 for: 4 == 4
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 0 for: 0 == 0
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get().size() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == "aa" for: "aa" == "aa"
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == "bb" for: "bb" == "bb"
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == "cc" for: "cc" == "cc"
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: filter([](int) { return false; }, value(1)), Catch::GeneratorException
GeneratorsImpl.tests.cpp:<line number>: passed: filter([](int) { return false; }, values({ 1, 2, 3 })), Catch::GeneratorException
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2.0 for: 2.0 == 2.0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 4.0 for: 4.0 == 4.0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 6.0 for: 6.0 == 6.0
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2.0 for: 2.0 == 2.0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 4.0 for: 4.0 == 4.0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 6.0 for: 6.0 == 6.0
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 3 for: 3 == 3
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -2 for: -2 == -2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 0 for: 0 == 0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 1 for: 1 == 1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 0 for: 0 == 0
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -7 for: -7 == -7
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -7 for: -7 == -7
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -7 for: -7 == -7
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -1.0 == Approx( -1.0 ) with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.9 == Approx( -0.9 ) with 1 message: 'Current expected value is -0.9'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.9'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.8 == Approx( -0.8 ) with 1 message: 'Current expected value is -0.8'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.8'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.7 == Approx( -0.7 ) with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.6 == Approx( -0.6 ) with 1 message: 'Current expected value is -0.6'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.6'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.5 == Approx( -0.5 ) with 1 message: 'Current expected value is -0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.4 == Approx( -0.4 ) with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.3 == Approx( -0.3 ) with 1 message: 'Current expected value is -0.3'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.3'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.2 == Approx( -0.2 ) with 1 message: 'Current expected value is -0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.1 == Approx( -0.1 ) with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.0 == Approx( -0.0 ) with 1 message: 'Current expected value is -1.38778e-16'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -1.38778e-16'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.1 == Approx( 0.1 ) with 1 message: 'Current expected value is 0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.2 == Approx( 0.2 ) with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.3 == Approx( 0.3 ) with 1 message: 'Current expected value is 0.3'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.3'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.4 == Approx( 0.4 ) with 1 message: 'Current expected value is 0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.5 == Approx( 0.5 ) with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.6 == Approx( 0.6 ) with 1 message: 'Current expected value is 0.6'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.6'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.7 == Approx( 0.7 ) with 1 message: 'Current expected value is 0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.8 == Approx( 0.8 ) with 1 message: 'Current expected value is 0.8'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.8'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.9 == Approx( 0.9 ) with 1 message: 'Current expected value is 0.9'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.9'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx( rangeEnd ) for: 1.0 == Approx( 1.0 )
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -1.0 == Approx( -1.0 ) with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.7 == Approx( -0.7 ) with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.4 == Approx( -0.4 ) with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.1 == Approx( -0.1 ) with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.2 == Approx( 0.2 ) with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.5 == Approx( 0.5 ) with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -1.0 == Approx( -1.0 ) with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.7 == Approx( -0.7 ) with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.7'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.4 == Approx( -0.4 ) with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.4'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: -0.1 == Approx( -0.1 ) with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is -0.1'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.2 == Approx( 0.2 ) with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.2'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == Approx(expected) for: 0.5 == Approx( 0.5 ) with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true with 1 message: 'Current expected value is 0.5'
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 5 for: 5 == 5
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == 2 for: 2 == 2
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -1 for: -1 == -1
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -4 for: -4 == -4
GeneratorsImpl.tests.cpp:<line number>: passed: gen.next() for: true
GeneratorsImpl.tests.cpp:<line number>: passed: gen.get() == -7 for: -7 == -7
GeneratorsImpl.tests.cpp:<line number>: passed: !(gen.next()) for: !false
Approx.tests.cpp:<line number>: passed: d >= Approx( 1.22 ) for: 1.23 >= Approx( 1.22 )
Approx.tests.cpp:<line number>: passed: d >= Approx( 1.23 ) for: 1.23 >= Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: !(d >= Approx( 1.24 )) for: !(1.23 >= Approx( 1.24 ))
Approx.tests.cpp:<line number>: passed: d >= Approx( 1.24 ).epsilon(0.1) for: 1.23 >= Approx( 1.24 )
TestCaseInfoHasher.tests.cpp:<line number>: passed: h1( dummy ) != h2( dummy ) for: 3422778688 (0x<hex digits>)
!=
130711275 (0x<hex digits>)
TestCaseInfoHasher.tests.cpp:<line number>: passed: h1( dummy ) == h2( dummy ) for: 3422778688 (0x<hex digits>)
==
3422778688 (0x<hex digits>)
TestCaseInfoHasher.tests.cpp:<line number>: passed: h( dummy1 ) != h( dummy2 ) for: 2903002874 (0x<hex digits>)
!=
2668622104 (0x<hex digits>)
TestCaseInfoHasher.tests.cpp:<line number>: passed: h( dummy1 ) != h( dummy2 ) for: 2673152918 (0x<hex digits>)
!=
3916075712 (0x<hex digits>)
TestCaseInfoHasher.tests.cpp:<line number>: passed: h( dummy1 ) != h( dummy2 ) for: 2074929312 (0x<hex digits>)
!=
3429949824 (0x<hex digits>)
TestCaseInfoHasher.tests.cpp:<line number>: passed: h( dummy ) == h( dummy ) for: 3422778688 (0x<hex digits>)
==
3422778688 (0x<hex digits>)
Message.tests.cpp:<line number>: warning: 'this is a message' with 1 message: 'this is a warning'
Message.tests.cpp:<line number>: failed: a == 1 for: 2 == 1 with 2 messages: 'this message should be logged' and 'so should this'
Message.tests.cpp:<line number>: passed: a == 2 for: 2 == 2 with 1 message: 'this message may be logged later'
Message.tests.cpp:<line number>: failed: a == 1 for: 2 == 1 with 2 messages: 'this message may be logged later' and 'this message should be logged'
Message.tests.cpp:<line number>: failed: a == 0 for: 2 == 0 with 3 messages: 'this message may be logged later' and 'this message should be logged' and 'and this, but later'
Message.tests.cpp:<line number>: passed: a == 2 for: 2 == 2 with 4 messages: 'this message may be logged later' and 'this message should be logged' and 'and this, but later' and 'but not this'
Message.tests.cpp:<line number>: passed: i < 10 for: 0 < 10 with 2 messages: 'current counter 0' and 'i := 0'
Message.tests.cpp:<line number>: passed: i < 10 for: 1 < 10 with 2 messages: 'current counter 1' and 'i := 1'
Message.tests.cpp:<line number>: passed: i < 10 for: 2 < 10 with 2 messages: 'current counter 2' and 'i := 2'
Message.tests.cpp:<line number>: passed: i < 10 for: 3 < 10 with 2 messages: 'current counter 3' and 'i := 3'
Message.tests.cpp:<line number>: passed: i < 10 for: 4 < 10 with 2 messages: 'current counter 4' and 'i := 4'
Message.tests.cpp:<line number>: passed: i < 10 for: 5 < 10 with 2 messages: 'current counter 5' and 'i := 5'
Message.tests.cpp:<line number>: passed: i < 10 for: 6 < 10 with 2 messages: 'current counter 6' and 'i := 6'
Message.tests.cpp:<line number>: passed: i < 10 for: 7 < 10 with 2 messages: 'current counter 7' and 'i := 7'
Message.tests.cpp:<line number>: passed: i < 10 for: 8 < 10 with 2 messages: 'current counter 8' and 'i := 8'
Message.tests.cpp:<line number>: passed: i < 10 for: 9 < 10 with 2 messages: 'current counter 9' and 'i := 9'
Message.tests.cpp:<line number>: failed: i < 10 for: 10 < 10 with 2 messages: 'current counter 10' and 'i := 10'
Condition.tests.cpp:<line number>: failed: data.int_seven != 7 for: 7 != 7
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one != Approx( 9.1f ) for: 9.1f != Approx( 9.1000003815 )
Condition.tests.cpp:<line number>: failed: data.double_pi != Approx( 3.1415926535 ) for: 3.1415926535 != Approx( 3.1415926535 )
Condition.tests.cpp:<line number>: failed: data.str_hello != "hello" for: "hello" != "hello"
Condition.tests.cpp:<line number>: failed: data.str_hello.size() != 5 for: 5 != 5
Condition.tests.cpp:<line number>: passed: data.int_seven != 6 for: 7 != 6
Condition.tests.cpp:<line number>: passed: data.int_seven != 8 for: 7 != 8
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one != Approx( 9.11f ) for: 9.1f != Approx( 9.1099996567 )
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one != Approx( 9.0f ) for: 9.1f != Approx( 9.0 )
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one != Approx( 1 ) for: 9.1f != Approx( 1.0 )
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one != Approx( 0 ) for: 9.1f != Approx( 0.0 )
Condition.tests.cpp:<line number>: passed: data.double_pi != Approx( 3.1415 ) for: 3.1415926535 != Approx( 3.1415 )
Condition.tests.cpp:<line number>: passed: data.str_hello != "goodbye" for: "hello" != "goodbye"
Condition.tests.cpp:<line number>: passed: data.str_hello != "hell" for: "hello" != "hell"
Condition.tests.cpp:<line number>: passed: data.str_hello != "hello1" for: "hello" != "hello1"
Condition.tests.cpp:<line number>: passed: data.str_hello.size() != 6 for: 5 != 6
Compilation.tests.cpp:<line number>: passed: []() { return true; }() for: true
Approx.tests.cpp:<line number>: passed: d <= Approx( 1.24 ) for: 1.23 <= Approx( 1.24 )
Approx.tests.cpp:<line number>: passed: d <= Approx( 1.23 ) for: 1.23 <= Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: !(d <= Approx( 1.22 )) for: !(1.23 <= Approx( 1.22 ))
Approx.tests.cpp:<line number>: passed: d <= Approx( 1.22 ).epsilon(0.1) for: 1.23 <= Approx( 1.22 )
Misc.tests.cpp:<line number>: passed: with 1 message: 'was called'
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "string" ) && ContainsSubstring( "abc" ) && ContainsSubstring( "substring" ) && ContainsSubstring( "contains" ) for: "this string contains 'abc' as a substring" ( contains: "string" and contains: "abc" and contains: "substring" and contains: "contains" )
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "string" ) || ContainsSubstring( "different" ) || ContainsSubstring( "random" ) for: "this string contains 'abc' as a substring" ( contains: "string" or contains: "different" or contains: "random" )
Matchers.tests.cpp:<line number>: passed: testStringForMatching2(), ContainsSubstring( "string" ) || ContainsSubstring( "different" ) || ContainsSubstring( "random" ) for: "some completely different text that contains one common word" ( contains: "string" or contains: "different" or contains: "random" )
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ( ContainsSubstring( "string" ) || ContainsSubstring( "different" ) ) && ContainsSubstring( "substring" ) for: "this string contains 'abc' as a substring" ( ( contains: "string" or contains: "different" ) and contains: "substring" )
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), ( ContainsSubstring( "string" ) || ContainsSubstring( "different" ) ) && ContainsSubstring( "random" ) for: "this string contains 'abc' as a substring" ( ( contains: "string" or contains: "different" ) and contains: "random" )
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), !ContainsSubstring( "different" ) for: "this string contains 'abc' as a substring" not contains: "different"
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), !ContainsSubstring( "substring" ) for: "this string contains 'abc' as a substring" not contains: "substring"
Condition.tests.cpp:<line number>: failed: explicitly
Condition.tests.cpp:<line number>: failed: explicitly
Condition.tests.cpp:<line number>: failed: explicitly
Condition.tests.cpp:<line number>: failed: explicitly
Exception.tests.cpp:<line number>: passed: thisThrows(), "expected exception" for: "expected exception" equals: "expected exception"
Exception.tests.cpp:<line number>: failed: thisThrows(), "should fail" for: "expected exception" equals: "should fail"
Reporters.tests.cpp:<line number>: passed: records == expected for: { "Hello", "world", "Goodbye", "world" }
==
{ "Hello", "world", "Goodbye", "world" }
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == false for: false == false
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldRedirectStdOut == true for: true == true
Reporters.tests.cpp:<line number>: passed: multiReporter.getPreferences().shouldReportAllAssertions == true for: true == true
Generators.tests.cpp:<line number>: passed: values > -6 for: 3 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 4 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 5 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 6 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: -5 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: -4 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 90 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 91 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 92 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 93 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 94 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 95 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 96 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 97 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 98 > -6
Generators.tests.cpp:<line number>: passed: values > -6 for: 99 > -6
Misc.tests.cpp:<line number>: warning: 'This one ran'
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'custom exception'
Tricky.tests.cpp:<line number>: passed: True for: {?}
Tricky.tests.cpp:<line number>: passed: !False for: true
Tricky.tests.cpp:<line number>: passed: !(False) for: !{?}
Compilation.tests.cpp:<line number>: passed: with 1 message: 'std::is_void<void>::value'
Compilation.tests.cpp:<line number>: passed: with 1 message: '!(std::is_void<int>::value)'
Compilation.tests.cpp:<line number>: passed: with 1 message: 'std::is_void<void>::value'
Compilation.tests.cpp:<line number>: passed: with 1 message: '!(std::is_void<int>::value)'
Condition.tests.cpp:<line number>: failed: data.int_seven > 7 for: 7 > 7
Condition.tests.cpp:<line number>: failed: data.int_seven < 7 for: 7 < 7
Condition.tests.cpp:<line number>: failed: data.int_seven > 8 for: 7 > 8
Condition.tests.cpp:<line number>: failed: data.int_seven < 6 for: 7 < 6
Condition.tests.cpp:<line number>: failed: data.int_seven < 0 for: 7 < 0
Condition.tests.cpp:<line number>: failed: data.int_seven < -1 for: 7 < -1
Condition.tests.cpp:<line number>: failed: data.int_seven >= 8 for: 7 >= 8
Condition.tests.cpp:<line number>: failed: data.int_seven <= 6 for: 7 <= 6
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one < 9 for: 9.1f < 9
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one > 10 for: 9.1f > 10
Condition.tests.cpp:<line number>: failed: data.float_nine_point_one > 9.2 for: 9.1f > 9.2
Condition.tests.cpp:<line number>: failed: data.str_hello > "hello" for: "hello" > "hello"
Condition.tests.cpp:<line number>: failed: data.str_hello < "hello" for: "hello" < "hello"
Condition.tests.cpp:<line number>: failed: data.str_hello > "hellp" for: "hello" > "hellp"
Condition.tests.cpp:<line number>: failed: data.str_hello > "z" for: "hello" > "z"
Condition.tests.cpp:<line number>: failed: data.str_hello < "hellm" for: "hello" < "hellm"
Condition.tests.cpp:<line number>: failed: data.str_hello < "a" for: "hello" < "a"
Condition.tests.cpp:<line number>: failed: data.str_hello >= "z" for: "hello" >= "z"
Condition.tests.cpp:<line number>: failed: data.str_hello <= "a" for: "hello" <= "a"
Condition.tests.cpp:<line number>: passed: data.int_seven < 8 for: 7 < 8
Condition.tests.cpp:<line number>: passed: data.int_seven > 6 for: 7 > 6
Condition.tests.cpp:<line number>: passed: data.int_seven > 0 for: 7 > 0
Condition.tests.cpp:<line number>: passed: data.int_seven > -1 for: 7 > -1
Condition.tests.cpp:<line number>: passed: data.int_seven >= 7 for: 7 >= 7
Condition.tests.cpp:<line number>: passed: data.int_seven >= 6 for: 7 >= 6
Condition.tests.cpp:<line number>: passed: data.int_seven <= 7 for: 7 <= 7
Condition.tests.cpp:<line number>: passed: data.int_seven <= 8 for: 7 <= 8
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one > 9 for: 9.1f > 9
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one < 10 for: 9.1f < 10
Condition.tests.cpp:<line number>: passed: data.float_nine_point_one < 9.2 for: 9.1f < 9.2
Condition.tests.cpp:<line number>: passed: data.str_hello <= "hello" for: "hello" <= "hello"
Condition.tests.cpp:<line number>: passed: data.str_hello >= "hello" for: "hello" >= "hello"
Condition.tests.cpp:<line number>: passed: data.str_hello < "hellp" for: "hello" < "hellp"
Condition.tests.cpp:<line number>: passed: data.str_hello < "zebra" for: "hello" < "zebra"
Condition.tests.cpp:<line number>: passed: data.str_hello > "hellm" for: "hello" > "hellm"
Condition.tests.cpp:<line number>: passed: data.str_hello > "a" for: "hello" > "a"
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 4242248763 (0x<hex digits>)
==
4242248763 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1867888929 (0x<hex digits>)
==
1867888929 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1276619030 (0x<hex digits>)
==
1276619030 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1911218783 (0x<hex digits>)
==
1911218783 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1827115164 (0x<hex digits>)
==
1827115164 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1472234645 (0x<hex digits>)
==
1472234645 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 868832940 (0x<hex digits>)
==
868832940 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 570883446 (0x<hex digits>)
==
570883446 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 889299803 (0x<hex digits>)
==
889299803 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 4261393167 (0x<hex digits>)
==
4261393167 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 1472234645 (0x<hex digits>)
==
1472234645 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 868832940 (0x<hex digits>)
==
868832940 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 570883446 (0x<hex digits>)
==
570883446 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 889299803 (0x<hex digits>)
==
889299803 (0x<hex digits>)
RandomNumberGeneration.tests.cpp:<line number>: passed: rng() == 0x<hex digits> for: 4261393167 (0x<hex digits>)
==
4261393167 (0x<hex digits>)
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'Message from section one'
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'Message from section two'
Matchers.tests.cpp:<line number>: passed: ( EvilMatcher(), EvilMatcher() ), EvilCommaOperatorUsed
Matchers.tests.cpp:<line number>: passed: &EvilMatcher(), EvilAddressOfOperatorUsed
Matchers.tests.cpp:<line number>: passed: EvilMatcher() || ( EvilMatcher() && !EvilMatcher() )
Matchers.tests.cpp:<line number>: passed: ( EvilMatcher() && EvilMatcher() ) || !EvilMatcher()
Parse.tests.cpp:<line number>: passed: parseUInt( "0" ) == Optional<unsigned int>{ 0 } for: {?} == {?}
Parse.tests.cpp:<line number>: passed: parseUInt( "100" ) == Optional<unsigned int>{ 100 } for: {?} == {?}
Parse.tests.cpp:<line number>: passed: parseUInt( "4294967295" ) == Optional<unsigned int>{ 4294967295 } for: {?} == {?}
Parse.tests.cpp:<line number>: passed: parseUInt( "0x<hex digits>", 16 ) == Optional<unsigned int>{ 255 } for: {?} == {?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "!!KJHF*#" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "-1" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "4294967296" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "42949672964294967296429496729642949672964294967296" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "2 4" )) for: !{?}
Parse.tests.cpp:<line number>: passed: !(parseUInt( "0x<hex digits>", 10 )) for: !{?}
TestSpecParser.tests.cpp:<line number>: passed: spec.hasFilters() for: true
TestSpecParser.tests.cpp:<line number>: passed: spec.getInvalidSpecs().empty() for: true
TestSpecParser.tests.cpp:<line number>: passed: spec.matches( testCase ) for: true
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--shard-count=8" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.shardCount == 8 for: 8 == 8
CmdLine.tests.cpp:<line number>: passed: !(result) for: !{?}
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring( "Could not parse '-1' as shard count" ) for: "Could not parse '-1' as shard count" contains: "Could not parse '-1' as shard count"
CmdLine.tests.cpp:<line number>: passed: !(result) for: !{?}
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring( "Shard count must be positive" ) for: "Shard count must be positive" contains: "Shard count must be positive"
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--shard-index=2" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.shardIndex == 2 for: 2 == 2
CmdLine.tests.cpp:<line number>: passed: !(result) for: !{?}
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring( "Could not parse '-12' as shard index" ) for: "Could not parse '-12' as shard index" contains: "Could not parse '-12' as shard index"
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--shard-index=0" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.shardIndex == 0 for: 0 == 0
TestSpecParser.tests.cpp:<line number>: passed: spec.hasFilters() for: true with 1 message: 'tagString := "[tag with spaces]"'
TestSpecParser.tests.cpp:<line number>: passed: spec.getInvalidSpecs().empty() for: true with 1 message: 'tagString := "[tag with spaces]"'
TestSpecParser.tests.cpp:<line number>: passed: spec.matches( testCase ) for: true with 1 message: 'tagString := "[tag with spaces]"'
TestSpecParser.tests.cpp:<line number>: passed: spec.hasFilters() for: true with 1 message: 'tagString := "[I said "good day" sir!]"'
TestSpecParser.tests.cpp:<line number>: passed: spec.getInvalidSpecs().empty() for: true with 1 message: 'tagString := "[I said "good day" sir!]"'
TestSpecParser.tests.cpp:<line number>: passed: spec.matches( testCase ) for: true with 1 message: 'tagString := "[I said "good day" sir!]"'
CmdLine.tests.cpp:<line number>: passed: cli.parse( { "test", "-w", "NoAssertions" } ) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.warnings == WarnAbout::NoAssertions for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: !(cli.parse( { "test", "-w", "NoTests" } )) for: !{?}
CmdLine.tests.cpp:<line number>: passed: cli.parse( { "test", "--warn", "NoAssertions", "--warn", "UnmatchedTestSpec" } ) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.warnings == ( WarnAbout::NoAssertions | WarnAbout::UnmatchedTestSpec ) for: 3 == 3
Condition.tests.cpp:<line number>: passed: p == 0 for: 0 == 0
Condition.tests.cpp:<line number>: passed: p == pNULL for: 0 == 0
Condition.tests.cpp:<line number>: passed: p != 0 for: 0x<hex digits> != 0
Condition.tests.cpp:<line number>: passed: cp != 0 for: 0x<hex digits> != 0
Condition.tests.cpp:<line number>: passed: cpc != 0 for: 0x<hex digits> != 0
Condition.tests.cpp:<line number>: passed: returnsNull() == 0 for: {null string} == 0
Condition.tests.cpp:<line number>: passed: returnsConstNull() == 0 for: {null string} == 0
Condition.tests.cpp:<line number>: passed: 0 != p for: 0 != 0x<hex digits>
ToStringGeneral.tests.cpp:<line number>: passed: str1.size() == 3 + 5 for: 8 == 8
ToStringGeneral.tests.cpp:<line number>: passed: str2.size() == 3 + 10 for: 13 == 13
ToStringGeneral.tests.cpp:<line number>: passed: str1.size() == 2 + 5 for: 7 == 7
ToStringGeneral.tests.cpp:<line number>: passed: str2.size() == 2 + 15 for: 17 == 17
Matchers.tests.cpp:<line number>: passed: "foo", Predicate<const char*>( []( const char* const& ) { return true; } ) for: "foo" matches undescribed predicate
CmdLine.tests.cpp:<line number>: passed: result for: {?}
CmdLine.tests.cpp:<line number>: passed: config.processName == "" for: "" == ""
CmdLine.tests.cpp:<line number>: passed: result for: {?}
CmdLine.tests.cpp:<line number>: passed: config.processName == "test" for: "test" == "test"
CmdLine.tests.cpp:<line number>: passed: config.shouldDebugBreak == false for: false == false
CmdLine.tests.cpp:<line number>: passed: config.abortAfter == -1 for: -1 == -1
CmdLine.tests.cpp:<line number>: passed: config.noThrow == false for: false == false
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications.empty() for: true
CmdLine.tests.cpp:<line number>: passed: !(cfg.hasTestFilters()) for: !false
CmdLine.tests.cpp:<line number>: passed: cfg.getReporterSpecs().size() == 1 for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: cfg.getReporterSpecs()[0] == Catch::ReporterSpec{ expectedReporter, {}, {}, {} } for: {?} == {?}
CmdLine.tests.cpp:<line number>: passed: cfg.getProcessedReporterSpecs().size() == 1 for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: cfg.getProcessedReporterSpecs()[0] == Catch::ProcessedReporterSpec{ expectedReporter, std::string{}, Catch::ColourMode::PlatformDefault, {} } for: {?} == {?}
CmdLine.tests.cpp:<line number>: passed: result for: {?}
CmdLine.tests.cpp:<line number>: passed: cfg.hasTestFilters() for: true
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("notIncluded")) == false for: false == false
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("test1")) for: true
CmdLine.tests.cpp:<line number>: passed: result for: {?}
CmdLine.tests.cpp:<line number>: passed: cfg.hasTestFilters() for: true
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("test1")) == false for: false == false
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("alwaysIncluded")) for: true
CmdLine.tests.cpp:<line number>: passed: result for: {?}
CmdLine.tests.cpp:<line number>: passed: cfg.hasTestFilters() for: true
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("test1")) == false for: false == false
CmdLine.tests.cpp:<line number>: passed: cfg.testSpec().matches(*fakeTestCase("alwaysIncluded")) for: true
CmdLine.tests.cpp:<line number>: passed: result for: {?} with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "console", {}, {}, {} } } for: { {?} } == { {?} } with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: result for: {?} with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "xml", {}, {}, {} } } for: { {?} } == { {?} } with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: result for: {?} with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "junit", {}, {}, {} } } for: { {?} } == { {?} } with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: !result for: true
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring("Unrecognized reporter") for: "Unrecognized reporter, 'unsupported'. Check available with --list-reporters" contains: "Unrecognized reporter"
CmdLine.tests.cpp:<line number>: passed: result for: {?} with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "console", "out.txt"s, {}, {} } } for: { {?} } == { {?} } with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: result for: {?} with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "console", "C:\\Temp\\out.txt"s, {}, {} } } for: { {?} } == { {?} } with 1 message: 'result.errorMessage() := ""'
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "-r", "xml::out=output.xml", "-r", "junit::out=output-junit.xml" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "xml", "output.xml"s, {}, {} }, { "junit", "output-junit.xml"s, {}, {} } } for: { {?}, {?} } == { {?}, {?} }
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "-r", "xml::out=output.xml", "-r", "console" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.reporterSpecifications == vec_Specs{ { "xml", "output.xml"s, {}, {} }, { "console", {}, {}, {} } } for: { {?}, {?} } == { {?}, {?} }
CmdLine.tests.cpp:<line number>: passed: !result for: true
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring("Only one reporter may have unspecified output file.") for: "Only one reporter may have unspecified output file." contains: "Only one reporter may have unspecified output file."
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-b"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.shouldDebugBreak == true for: true == true
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--break"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.shouldDebugBreak for: true
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-a"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.abortAfter == 1 for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-x", "2"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.abortAfter == 2 for: 2 == 2
CmdLine.tests.cpp:<line number>: passed: !result for: true
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring("convert") && ContainsSubstring("oops") for: "Unable to convert 'oops' to destination type" ( contains: "convert" and contains: "oops" )
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--wait-for-keypress", std::get<0>(input)}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.waitForKeypress == std::get<1>(input) for: 0 == 0
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--wait-for-keypress", std::get<0>(input)}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.waitForKeypress == std::get<1>(input) for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--wait-for-keypress", std::get<0>(input)}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.waitForKeypress == std::get<1>(input) for: 2 == 2
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--wait-for-keypress", std::get<0>(input)}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.waitForKeypress == std::get<1>(input) for: 3 == 3
CmdLine.tests.cpp:<line number>: passed: !result for: true
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring("never") && ContainsSubstring("both") for: "keypress argument must be one of: never, start, exit or both. 'sometimes' not recognised" ( contains: "never" and contains: "both" )
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-e"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.noThrow for: true
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--nothrow"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.noThrow for: true
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-o", "filename.ext"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultOutputFilename == "filename.ext" for: "filename.ext" == "filename.ext"
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--out", "filename.ext"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultOutputFilename == "filename.ext" for: "filename.ext" == "filename.ext"
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "-abe"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.abortAfter == 1 for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: config.shouldDebugBreak for: true
CmdLine.tests.cpp:<line number>: passed: config.noThrow == true for: true == true
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultColourMode == ColourMode::PlatformDefault for: 0 == 0
CmdLine.tests.cpp:<line number>: passed: cli.parse( { "test", "--colour-mode", "default" } ) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultColourMode == ColourMode::PlatformDefault for: 0 == 0
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--colour-mode", "ansi"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultColourMode == ColourMode::ANSI for: 1 == 1
CmdLine.tests.cpp:<line number>: passed: cli.parse({"test", "--colour-mode", "none"}) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.defaultColourMode == ColourMode::None for: 3 == 3
CmdLine.tests.cpp:<line number>: passed: !result for: true
CmdLine.tests.cpp:<line number>: passed: result.errorMessage(), ContainsSubstring( "colour mode must be one of" ) for: "colour mode must be one of: default, ansi, win32, or none. 'wrong' is not recognised" contains: "colour mode must be one of"
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--benchmark-samples=200" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.benchmarkSamples == 200 for: 200 == 200
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--benchmark-resamples=20000" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.benchmarkResamples == 20000 for: 20000 (0x<hex digits>) == 20000 (0x<hex digits>)
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--benchmark-confidence-interval=0.99" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.benchmarkConfidenceInterval == Catch::Approx(0.99) for: 0.99 == Approx( 0.99 )
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--benchmark-no-analysis" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.benchmarkNoAnalysis for: true
CmdLine.tests.cpp:<line number>: passed: cli.parse({ "test", "--benchmark-warmup-time=10" }) for: {?}
CmdLine.tests.cpp:<line number>: passed: config.benchmarkWarmupTime == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: std::tuple_size<TestType>::value >= 1 for: 3 >= 1
Misc.tests.cpp:<line number>: passed: std::tuple_size<TestType>::value >= 1 for: 2 >= 1
Misc.tests.cpp:<line number>: passed: std::tuple_size<TestType>::value >= 1 for: 1 >= 1
RandomNumberGeneration.tests.cpp:<line number>: passed: Catch::generateRandomSeed(method)
RandomNumberGeneration.tests.cpp:<line number>: passed: Catch::generateRandomSeed(method)
RandomNumberGeneration.tests.cpp:<line number>: passed: Catch::generateRandomSeed(method)
RandomNumberGeneration.tests.cpp:<line number>: passed: Catch::generateRandomSeed(static_cast<Catch::GenerateFrom>(77))
ToString.tests.cpp:<line number>: passed: Catch::Detail::stringify(UsesSentinel{}) == "{  }" for: "{  }" == "{  }"
Decomposition.tests.cpp:<line number>: failed: truthy(false) for: Hey, its truthy!
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), Matches( "this STRING contains 'abc' as a substring" ) for: "this string contains 'abc' as a substring" matches "this STRING contains 'abc' as a substring" case sensitively
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), Matches( "contains 'abc' as a substring" ) for: "this string contains 'abc' as a substring" matches "contains 'abc' as a substring" case sensitively
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), Matches( "this string contains 'abc' as a" ) for: "this string contains 'abc' as a substring" matches "this string contains 'abc' as a" case sensitively
Reporters.tests.cpp:<line number>: passed: registry.registerReporter( "with::doublecolons", Catch::Detail::make_unique<TestReporterFactory>() ), "'::' is not allowed in reporter name: 'with::doublecolons'" for: "'::' is not allowed in reporter name: 'with::doublecolons'" equals: "'::' is not allowed in reporter name: 'with::doublecolons'"
Matchers.tests.cpp:<line number>: passed: actual, !UnorderedEquals( expected ) for: { 'a', 'b' } not UnorderedEquals: { 'c', 'b' }
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: Automake'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: Automake'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: Automake'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: compact'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: compact'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: compact'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: console'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: console'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: console'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "<?xml version="1.0" encoding="UTF-8"?>
All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: JUnit'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "<?xml version="1.0" encoding="UTF-8"?>
Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: JUnit'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "<?xml version="1.0" encoding="UTF-8"?>
All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: JUnit'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "<?xml version="1.0" encoding="UTF-8"?>
All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: SonarQube'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "<?xml version="1.0" encoding="UTF-8"?>
Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: SonarQube'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "<?xml version="1.0" encoding="UTF-8"?>
All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: SonarQube'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: TAP'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: TAP'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: TAP'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "fakeTag" with 1 message: 'Tested reporter: TeamCity'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "Available reporters:
  fake reporter:  fake description

" contains: "fake reporter" with 1 message: 'Tested reporter: TeamCity'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: TeamCity'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fakeTag"s) for: "<?xml version="1.0" encoding="UTF-8"?>
<TagsFromMatchingTests>
  <Tag>
    <Count>1</Count>
    <Aliases>
      <Alias>fakeTag</Alias>
    </Aliases>
  </Tag>
</TagsFromMatchingTests>" contains: "fakeTag" with 1 message: 'Tested reporter: XML'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("fake reporter"s) for: "<?xml version="1.0" encoding="UTF-8"?>
<AvailableReporters>
  <Reporter>
    <Name>fake reporter</Name>
    <Description>fake description</Description>
  </Reporter>
</AvailableReporters>" contains: "fake reporter" with 1 message: 'Tested reporter: XML'
Reporters.tests.cpp:<line number>: passed: !(factories.empty()) for: !false
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "<?xml version="1.0" encoding="UTF-8"?>
<MatchingTests>
  <TestCase>
    <Name>fake test name</Name>
    <ClassName/>
    <Tags>[fakeTestTag]</Tags>
    <SourceInfo>
      <File>fake-file.cpp</File>
      <Line>123456789</Line>
    </SourceInfo>
  </TestCase>
</MatchingTests>" ( contains: "fake test name" and contains: "fakeTestTag" ) with 1 message: 'Tested reporter: XML'
Reporters.tests.cpp:<line number>: passed:
Message.tests.cpp:<line number>: passed: with 1 message: 'this is a success'
Message.tests.cpp:<line number>: passed:
BDD.tests.cpp:<line number>: passed: before == 0 for: 0 == 0
BDD.tests.cpp:<line number>: passed: after > before for: 1 > 0
BDD.tests.cpp:<line number>: passed: itDoesThis() for: true
BDD.tests.cpp:<line number>: passed: itDoesThat() for: true
BDD.tests.cpp:<line number>: passed: with 1 message: 'boo!'
BDD.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
BDD.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
BDD.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
BDD.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
BDD.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
BDD.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
BDD.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
BDD.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Approx.tests.cpp:<line number>: passed: d == Approx( 1.23 ) for: 1.23 == Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: d != Approx( 1.22 ) for: 1.23 != Approx( 1.22 )
Approx.tests.cpp:<line number>: passed: d != Approx( 1.24 ) for: 1.23 != Approx( 1.24 )
Approx.tests.cpp:<line number>: passed: d == 1.23_a for: 1.23 == Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: d != 1.22_a for: 1.23 != Approx( 1.22 )
Approx.tests.cpp:<line number>: passed: Approx( d ) == 1.23 for: Approx( 1.23 ) == 1.23
Approx.tests.cpp:<line number>: passed: Approx( d ) != 1.22 for: Approx( 1.23 ) != 1.22
Approx.tests.cpp:<line number>: passed: Approx( d ) != 1.24 for: Approx( 1.23 ) != 1.24
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), StartsWith( "This String" ) for: "this string contains 'abc' as a substring" starts with: "This String"
Matchers.tests.cpp:<line number>: failed: testStringForMatching(), StartsWith( "string", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" starts with: "string" (case insensitive)
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify(singular) == "{ 1 }" for: "{ 1 }" == "{ 1 }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify(arr) == "{ 3, 2, 1 }" for: "{ 3, 2, 1 }" == "{ 3, 2, 1 }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify(arr) == R"({ { "1:1", "1:2", "1:3" }, { "2:1", "2:2" } })" for: "{ { "1:1", "1:2", "1:3" }, { "2:1", "2:2" } }"
==
"{ { "1:1", "1:2", "1:3" }, { "2:1", "2:2" } }"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "string" ) for: "this string contains 'abc' as a substring" contains: "string"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "string", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" contains: "string" (case insensitive)
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "abc" ) for: "this string contains 'abc' as a substring" contains: "abc"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), ContainsSubstring( "aBC", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" contains: "abc" (case insensitive)
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), StartsWith( "this" ) for: "this string contains 'abc' as a substring" starts with: "this"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), StartsWith( "THIS", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" starts with: "this" (case insensitive)
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), EndsWith( "substring" ) for: "this string contains 'abc' as a substring" ends with: "substring"
Matchers.tests.cpp:<line number>: passed: testStringForMatching(), EndsWith( " SuBsTrInG", Catch::CaseSensitive::No ) for: "this string contains 'abc' as a substring" ends with: " substring" (case insensitive)
String.tests.cpp:<line number>: passed: empty.empty() for: true
String.tests.cpp:<line number>: passed: empty.size() == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: std::strcmp( empty.data(), "" ) == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: s.empty() == false for: false == false
String.tests.cpp:<line number>: passed: s.size() == 5 for: 5 == 5
String.tests.cpp:<line number>: passed: std::strcmp( rawChars, "hello" ) == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: s.data() == rawChars for: "hello" == "hello"
String.tests.cpp:<line number>: passed: original == "original"
String.tests.cpp:<line number>: passed: original.data()
String.tests.cpp:<line number>: passed: original.begin() == copy.begin() for: "original string" == "original string"
String.tests.cpp:<line number>: passed: original.begin() == copy.begin() for: "original string" == "original string"
String.tests.cpp:<line number>: passed: ss.empty() == false for: false == false
String.tests.cpp:<line number>: passed: ss.size() == 5 for: 5 == 5
String.tests.cpp:<line number>: passed: std::strncmp( ss.data(), "hello", 5 ) == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: ss == "hello" for: hello == "hello"
String.tests.cpp:<line number>: passed: ss.size() == 6 for: 6 == 6
String.tests.cpp:<line number>: passed: std::strcmp( ss.data(), "world!" ) == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: s.data() == s2.data() for: "hello world!" == "hello world!"
String.tests.cpp:<line number>: passed: s.data() == ss.data() for: "hello world!" == "hello world!"
String.tests.cpp:<line number>: passed: s.substr(s.size() + 1, 123).empty() for: true
String.tests.cpp:<line number>: passed: std::strcmp(ss.data(), "world!") == 0 for: 0 == 0
String.tests.cpp:<line number>: passed: s.substr(1'000'000, 1).empty() for: true
String.tests.cpp:<line number>: passed: reinterpret_cast<char*>(buffer1) != reinterpret_cast<char*>(buffer2) for: "Hello" != "Hello"
String.tests.cpp:<line number>: passed: left == right for: Hello == Hello
String.tests.cpp:<line number>: passed: left != left.substr(0, 3) for: Hello != Hel
String.tests.cpp:<line number>: passed: sr == "a standard string" for: a standard string == "a standard string"
String.tests.cpp:<line number>: passed: sr.size() == stdStr.size() for: 17 == 17
String.tests.cpp:<line number>: passed: sr == "a standard string" for: a standard string == "a standard string"
String.tests.cpp:<line number>: passed: sr.size() == stdStr.size() for: 17 == 17
String.tests.cpp:<line number>: passed: sr == "a standard string" for: a standard string == "a standard string"
String.tests.cpp:<line number>: passed: sr.size() == stdStr.size() for: 17 == 17
String.tests.cpp:<line number>: passed: stdStr == "a stringref" for: "a stringref" == "a stringref"
String.tests.cpp:<line number>: passed: stdStr.size() == sr.size() for: 11 == 11
String.tests.cpp:<line number>: passed: stdStr == "a stringref" for: "a stringref" == "a stringref"
String.tests.cpp:<line number>: passed: stdStr.size() == sr.size() for: 11 == 11
String.tests.cpp:<line number>: passed: lhs == "some string += the stringref contents" for: "some string += the stringref contents"
==
"some string += the stringref contents"
String.tests.cpp:<line number>: passed: together == "abrakadabra" for: "abrakadabra" == "abrakadabra"
String.tests.cpp:<line number>: passed: with 1 message: 'empty.size() == 0'
String.tests.cpp:<line number>: passed: with 1 message: 'empty.begin() == empty.end()'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.size() == 3'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.data() == abc'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.begin() == abc'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.begin() != stringref.end()'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.substr(10, 0).empty()'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref.substr(2, 1).data() == abc + 2'
String.tests.cpp:<line number>: passed: with 1 message: 'stringref[1] == 'b''
String.tests.cpp:<line number>: passed: with 1 message: 'shortened.size() == 2'
String.tests.cpp:<line number>: passed: with 1 message: 'shortened.data() == abc'
String.tests.cpp:<line number>: passed: with 1 message: 'shortened.begin() != shortened.end()'
String.tests.cpp:<line number>: passed: with 1 message: '!(sr1.empty())'
String.tests.cpp:<line number>: passed: with 1 message: 'sr1.size() == 3'
String.tests.cpp:<line number>: passed: with 1 message: 'sr2.empty()'
String.tests.cpp:<line number>: passed: with 1 message: 'sr2.size() == 0'
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( with_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( no_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( with_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( no_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( with_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( no_null_terminator ) == R"("abc")"s for: ""abc"" == ""abc""
ToStringChrono.tests.cpp:<line number>: passed: minute == seconds for: 1 m == 60 s
ToStringChrono.tests.cpp:<line number>: passed: hour != seconds for: 1 h != 60 s
ToStringChrono.tests.cpp:<line number>: passed: micro != milli for: 1 us != 1 ms
ToStringChrono.tests.cpp:<line number>: passed: nano != micro for: 1 ns != 1 us
ToStringChrono.tests.cpp:<line number>: passed: half_minute != femto_second for: 1 [30/1]s != 1 fs
ToStringChrono.tests.cpp:<line number>: passed: pico_second != atto_second for: 1 ps != 1 as
ToStringChrono.tests.cpp:<line number>: passed: now != later for: {iso8601-timestamp}
!=
{iso8601-timestamp}
Misc.tests.cpp:<line number>: failed: s1 == s2 for: "if ($b == 10) {
		$a	= 20;
}"
==
"if ($b == 10) {
	$a = 20;
}
"
Tag.tests.cpp:<line number>: passed: what, ContainsSubstring( "[@zzz]" ) for: "error: tag alias, '[@zzz]' already registered.
	First seen at: file:2
	Redefined at: file:10" contains: "[@zzz]"
Tag.tests.cpp:<line number>: passed: what, ContainsSubstring( "file" ) for: "error: tag alias, '[@zzz]' already registered.
	First seen at: file:2
	Redefined at: file:10" contains: "file"
Tag.tests.cpp:<line number>: passed: what, ContainsSubstring( "2" ) for: "error: tag alias, '[@zzz]' already registered.
	First seen at: file:2
	Redefined at: file:10" contains: "2"
Tag.tests.cpp:<line number>: passed: what, ContainsSubstring( "10" ) for: "error: tag alias, '[@zzz]' already registered.
	First seen at: file:2
	Redefined at: file:10" contains: "10"
Tag.tests.cpp:<line number>: passed: registry.add( "[no ampersat]", "", Catch::SourceLineInfo( "file", 3 ) )
Tag.tests.cpp:<line number>: passed: registry.add( "[the @ is not at the start]", "", Catch::SourceLineInfo( "file", 3 ) )
Tag.tests.cpp:<line number>: passed: registry.add( "@no square bracket at start]", "", Catch::SourceLineInfo( "file", 3 ) )
Tag.tests.cpp:<line number>: passed: registry.add( "[@no square bracket at end", "", Catch::SourceLineInfo( "file", 3 ) )
Tag.tests.cpp:<line number>: passed: testCase.tags.size() == 2 for: 2 == 2
Tag.tests.cpp:<line number>: passed: testCase.tags, VectorContains( Tag( "tag with spaces" ) ) && VectorContains( Tag( "I said \"good day\" sir!"_catch_sr ) ) for: { {?}, {?} } ( Contains: {?} and Contains: {?} )
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1 == 1
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1 == 1
Class.tests.cpp:<line number>: passed: Template_Fixture<TestType>::m_a == 1 for: 1.0 == 1
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 1 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 4 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 1 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 4 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 4 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 1 > 0
Misc.tests.cpp:<line number>: passed: sizeof(TestType) > 0 for: 4 > 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.size() == 2 * V for: 12 == 12
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 12 >= 12
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 12 >= 12
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.size() == V for: 6 == 6
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 6 >= 6
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.size() == 2 * V for: 8 == 8
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 8 >= 8
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 8 >= 8
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.size() == V for: 4 == 4
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 4 >= 4
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 2 * V for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == V for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Misc.tests.cpp:<line number>: passed: v.size() == 2 * V for: 30 == 30
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 30 >= 30
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= 2 * V for: 30 >= 30
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Misc.tests.cpp:<line number>: passed: v.size() == V for: 15 == 15
Misc.tests.cpp:<line number>: passed: v.capacity() >= V for: 15 >= 15
Tag.tests.cpp:<line number>: passed: testCase.tags.size() == 1 for: 1 == 1
Tag.tests.cpp:<line number>: passed: testCase.tags[0] == Tag( "tag1" ) for: {?} == {?}
VariadicMacros.tests.cpp:<line number>: passed: with 1 message: 'no assertions'
Tricky.tests.cpp:<line number>: passed: 0x<hex digits> == bit30and31 for: 3221225472 (0x<hex digits>) == 3221225472
CmdLine.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed: true
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: failed - but was ok: false
Misc.tests.cpp:<line number>: passed: true
Misc.tests.cpp:<line number>: failed - but was ok: false
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed: true
Misc.tests.cpp:<line number>: failed: explicitly
Misc.tests.cpp:<line number>: failed - but was ok: false
Misc.tests.cpp:<line number>: failed: explicitly
Message.tests.cpp:<line number>: failed - but was ok: 1 == 2
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring("[fakeTag]"s) for: "All available tags:
   1  [fakeTag]
1 tag

" contains: "[fakeTag]"
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake reporter"s ) && ContainsSubstring( "fake description"s ) for: "Available reporters:
  fake reporter:  fake description

" ( contains: "fake reporter" and contains: "fake description" )
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fake test name"s ) && ContainsSubstring( "fakeTestTag"s ) for: "All available test cases:
  fake test name
      [fakeTestTag]
1 test case

" ( contains: "fake test name" and contains: "fakeTestTag" )
Reporters.tests.cpp:<line number>: passed: listingString, ContainsSubstring( "fakeListener"s ) && ContainsSubstring( "fake description"s ) for: "Registered listeners:
  fakeListener:  fake description

" ( contains: "fakeListener" and contains: "fake description" )
Misc.tests.cpp:<line number>: passed: with 1 message: 'oops!'
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'For some reason someone is throwing a string literal!'
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1b.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1b.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: s2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s2.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1b.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: s2b.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: s2b.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase2.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s2.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1b.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: s2b.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: ctx.completedCycle() for: true
PartTracker.tests.cpp:<line number>: passed: s2b.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: s2b.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase2.isSuccessfullyCompleted() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase3.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1c.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: s2c.isOpen() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase3.isSuccessfullyCompleted() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s2.isOpen() for: true
PartTracker.tests.cpp:<line number>: passed: s2.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: s1.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: s1.isComplete() for: true
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() == false for: false == false
PartTracker.tests.cpp:<line number>: passed: testCase.isComplete() for: true
StringManip.tests.cpp:<line number>: passed: trim(std::string(no_whitespace)) == no_whitespace for: "There is no extra whitespace here"
==
"There is no extra whitespace here"
StringManip.tests.cpp:<line number>: passed: trim(std::string(leading_whitespace)) == no_whitespace for: "There is no extra whitespace here"
==
"There is no extra whitespace here"
StringManip.tests.cpp:<line number>: passed: trim(std::string(trailing_whitespace)) == no_whitespace for: "There is no extra whitespace here"
==
"There is no extra whitespace here"
StringManip.tests.cpp:<line number>: passed: trim(std::string(whitespace_at_both_ends)) == no_whitespace for: "There is no extra whitespace here"
==
"There is no extra whitespace here"
StringManip.tests.cpp:<line number>: passed: trim(StringRef(no_whitespace)) == StringRef(no_whitespace) for: There is no extra whitespace here
==
There is no extra whitespace here
StringManip.tests.cpp:<line number>: passed: trim(StringRef(leading_whitespace)) == StringRef(no_whitespace) for: There is no extra whitespace here
==
There is no extra whitespace here
StringManip.tests.cpp:<line number>: passed: trim(StringRef(trailing_whitespace)) == StringRef(no_whitespace) for: There is no extra whitespace here
==
There is no extra whitespace here
StringManip.tests.cpp:<line number>: passed: trim(StringRef(whitespace_at_both_ends)) == StringRef(no_whitespace) for: There is no extra whitespace here
==
There is no extra whitespace here
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, RangeEquals( c_array ) for: { 1, 2, 3 } elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, UnorderedRangeEquals( c_array ) for: { 1, 2, 3 } unordered elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_3, !RangeEquals( array_int_4 ) for: { 1, 2, 3 } not elements are { 1, 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_3, !UnorderedRangeEquals( array_int_4 ) for: { 1, 2, 3 } not unordered elements are { 1, 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, RangeEquals( vector_char_a ) for: { 1, 2, 3 } elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, UnorderedRangeEquals( vector_char_a ) for: { 1, 2, 3 } unordered elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: with 1 message: 'ContainerIsRandomAccess( array_int_a ) != ContainerIsRandomAccess( list_char_a )'
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, RangeEquals( list_char_a ) for: { 1, 2, 3 } elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_int_a, UnorderedRangeEquals( list_char_a ) for: { 1, 2, 3 } unordered elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: vector_int_a, RangeEquals( vector_char_a ) for: { 1, 2, 3 } elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: vector_int_a, UnorderedRangeEquals( vector_char_a ) for: { 1, 2, 3 } unordered elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: vector_int_a, !RangeEquals( vector_char_b ) for: { 1, 2, 3 } not elements are { 1, 2, 2 }
MatchersRanges.tests.cpp:<line number>: passed: vector_int_a, !UnorderedRangeEquals( vector_char_b ) for: { 1, 2, 3 } not unordered elements are { 1, 2, 2 }
MatchersRanges.tests.cpp:<line number>: passed: a, !RangeEquals( b ) for: { 1, 2, 3 } not elements are { 3, 2, 1 }
MatchersRanges.tests.cpp:<line number>: passed: a, UnorderedRangeEquals( b ) for: { 1, 2, 3 } unordered elements are { 3, 2, 1 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, RangeEquals( array_a_plus_1, close_enough ) for: { 1, 2, 3 } elements are { 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, UnorderedRangeEquals( array_a_plus_1, close_enough ) for: { 1, 2, 3 } unordered elements are { 2, 3, 4 }
Exception.tests.cpp:<line number>: failed: unexpected exception with message: '3.14'
UniquePtr.tests.cpp:<line number>: passed: bptr->i == 3 for: 3 == 3
UniquePtr.tests.cpp:<line number>: passed: bptr->i == 3 for: 3 == 3
MatchersRanges.tests.cpp:<line number>: passed: data, AllMatch(SizeIs(5)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } all match has size == 5
MatchersRanges.tests.cpp:<line number>: passed: data, !AllMatch(Contains(0) && Contains(1)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } not all match ( contains element 0 and contains element 1 )
MatchersRanges.tests.cpp:<line number>: passed: needs_adl, AllMatch( Predicate<int>( []( int elem ) { return elem < 6; } ) ) for: { 1, 2, 3, 4, 5 } all match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked, allMatch for: { 1, 2, 3, 4, 5 } all match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, !allMatch for: { 1, 2, 3, 4, 5 } not all match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: data, AllTrue() for: { true, true, true, true, true } contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, AllTrue() for: {  } contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, !AllTrue() for: { true, true, false, true, true } not contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, !AllTrue() for: { false, false, false, false, false } not contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, AllTrue() for: { true, true, true, true, true } contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, !AllTrue() for: { true, true, false, true, true } not contains only true
MatchersRanges.tests.cpp:<line number>: passed: data, !AllTrue() for: { false, false, false, false, false } not contains only true
MatchersRanges.tests.cpp:<line number>: passed: mocked, AllTrue() for: { true, true, true, true, true } contains only true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, !AllTrue() for: { true, true, false, true, true } not contains only true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: data, AnyMatch(SizeIs(5)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } any match has size == 5
MatchersRanges.tests.cpp:<line number>: passed: data, !AnyMatch(Contains(0) && Contains(10)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } not any match ( contains element 0 and contains element 10 )
MatchersRanges.tests.cpp:<line number>: passed: needs_adl, AnyMatch( Predicate<int>( []( int elem ) { return elem < 3; } ) ) for: { 1, 2, 3, 4, 5 } any match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked, !anyMatch for: { 1, 2, 3, 4, 5 } not any match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, anyMatch for: { 1, 2, 3, 4, 5 } any match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[1]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[2]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: data, AnyTrue() for: { true, true, true, true, true } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, !AnyTrue() for: {  } not contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, AnyTrue() for: { false, false, true, false, false } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, !AnyTrue() for: { false, false, false, false, false } not contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, AnyTrue() for: { true, true, true, true, true } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, AnyTrue() for: { false, false, true, false, false } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: data, !AnyTrue() for: { false, false, false, false, false } not contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: mocked, AnyTrue() for: { false, false, false, false, true } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, AnyTrue() for: { false, false, true, true, true } contains at least one true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: data, NoneMatch(SizeIs(6)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } none match has size == 6
MatchersRanges.tests.cpp:<line number>: passed: data, !NoneMatch(Contains(0) && Contains(1)) for: { { 0, 1, 2, 3, 5 }, { 4, -3, -2, 5, 0 }, { 0, 0, 0, 5, 0 }, { 0, -5, 0, 5, 0 }, { 1, 0, 0, -1, 5 } } not none match ( contains element 0 and contains element 1 )
MatchersRanges.tests.cpp:<line number>: passed: needs_adl, NoneMatch( Predicate<int>( []( int elem ) { return elem > 6; } ) ) for: { 1, 2, 3, 4, 5 } none match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked, noneMatch for: { 1, 2, 3, 4, 5 } none match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, !noneMatch for: { 1, 2, 3, 4, 5 } not none match matches undescribed predicate
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[1]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[2]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: data, !NoneTrue() for: { true, true, true, true, true } not contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, NoneTrue() for: {  } contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, !NoneTrue() for: { false, false, true, false, false } not contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, NoneTrue() for: { false, false, false, false, false } contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, !NoneTrue() for: { true, true, true, true, true } not contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, !NoneTrue() for: { false, false, true, false, false } not contains no true
MatchersRanges.tests.cpp:<line number>: passed: data, NoneTrue() for: { false, false, false, false, false } contains no true
MatchersRanges.tests.cpp:<line number>: passed: mocked, NoneTrue() for: { false, false, false, false, false } contains no true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[4] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked, !NoneTrue() for: { false, false, true, true, true } not contains no true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: !(mocked.m_derefed[4]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: empty_vector, RangeEquals( empty_vector ) for: {  } elements are {  }
MatchersRanges.tests.cpp:<line number>: passed: empty_vector, !RangeEquals( non_empty_vector ) for: {  } not elements are { 1 }
MatchersRanges.tests.cpp:<line number>: passed: non_empty_vector, !RangeEquals( empty_vector ) for: { 1 } not elements are {  }
MatchersRanges.tests.cpp:<line number>: passed: non_empty_array, RangeEquals( non_empty_array ) for: { 1 } elements are { 1 }
MatchersRanges.tests.cpp:<line number>: passed: array_a, RangeEquals( array_a ) for: { 1, 2, 3 } elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_a, !RangeEquals( array_b ) for: { 1, 2, 3 } not elements are { 2, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_a, !RangeEquals( array_c ) for: { 1, 2, 3 } not elements are { 1, 2, 2 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, !RangeEquals( vector_b ) for: { 1, 2, 3 } not elements are { 1, 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, RangeEquals( vector_a_plus_1, close_enough ) for: { 1, 2, 3 } elements are { 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, !RangeEquals( vector_b, close_enough ) for: { 1, 2, 3 } not elements are { 3, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: needs_adl1, RangeEquals( needs_adl2 ) for: { 1, 2, 3, 4, 5 } elements are { 1, 2, 3, 4, 5 }
MatchersRanges.tests.cpp:<line number>: passed: needs_adl1, RangeEquals( needs_adl3, []( int l, int r ) { return l + 1 == r; } ) for: { 1, 2, 3, 4, 5 } elements are { 2, 3, 4, 5, 6 }
MatchersRanges.tests.cpp:<line number>: passed: mocked1, !RangeEquals( arr ) for: { 1, 2, 3, 4 } not elements are { 1, 2, 4, 4 }
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: !(mocked1.m_derefed[3]) for: !false
MatchersRanges.tests.cpp:<line number>: passed: mocked1, RangeEquals( arr ) for: { 1, 2, 3, 4 } elements are { 1, 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[0] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[1] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[2] for: true
MatchersRanges.tests.cpp:<line number>: passed: mocked1.m_derefed[3] for: true
MatchersRanges.tests.cpp:<line number>: passed: empty_vector, UnorderedRangeEquals( empty_vector ) for: {  } unordered elements are {  }
MatchersRanges.tests.cpp:<line number>: passed: empty_vector, !UnorderedRangeEquals( non_empty_vector ) for: {  } not unordered elements are { 1 }
MatchersRanges.tests.cpp:<line number>: passed: non_empty_vector, !UnorderedRangeEquals( empty_vector ) for: { 1 } not unordered elements are {  }
MatchersRanges.tests.cpp:<line number>: passed: non_empty_array, UnorderedRangeEquals( non_empty_array ) for: { 1 } unordered elements are { 1 }
MatchersRanges.tests.cpp:<line number>: passed: array_a, UnorderedRangeEquals( array_a ) for: { 1, 2, 3 } unordered elements are { 1, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: array_a, !UnorderedRangeEquals( array_b ) for: { 1, 2, 3 } not unordered elements are { 2, 2, 3 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, !UnorderedRangeEquals( vector_b ) for: { 1, 2, 3 } not unordered elements are { 1, 2, 3, 4 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, UnorderedRangeEquals( vector_a_plus_1, close_enough ) for: { 1, 10, 20 } unordered elements are { 11, 21, 2 }
MatchersRanges.tests.cpp:<line number>: passed: vector_a, !UnorderedRangeEquals( vector_b, close_enough ) for: { 1, 10, 21 } not unordered elements are { 11, 21, 3 }
MatchersRanges.tests.cpp:<line number>: passed: needs_adl1, UnorderedRangeEquals( needs_adl2 ) for: { 1, 2, 3, 4, 5 } unordered elements are { 1, 2, 3, 4, 5 }
MatchersRanges.tests.cpp:<line number>: passed: empty_vec, SizeIs(0) for: {  } has size == 0
MatchersRanges.tests.cpp:<line number>: passed: empty_vec, !SizeIs(2) for: {  } not has size == 2
MatchersRanges.tests.cpp:<line number>: passed: empty_vec, SizeIs(Lt(2)) for: {  } size matches is less than 2
MatchersRanges.tests.cpp:<line number>: passed: arr, SizeIs(2) for: { 0, 0 } has size == 2
MatchersRanges.tests.cpp:<line number>: passed: arr, SizeIs( Lt(3)) for: { 0, 0 } size matches is less than 3
MatchersRanges.tests.cpp:<line number>: passed: arr, !SizeIs(!Lt(3)) for: { 0, 0 } not size matches not is less than 3
MatchersRanges.tests.cpp:<line number>: passed: map, SizeIs(3) for: { {?}, {?}, {?} } has size == 3
MatchersRanges.tests.cpp:<line number>: passed: unrelated::ADL_size{}, SizeIs(12) for: {?} has size == 12
MatchersRanges.tests.cpp:<line number>: passed: has_size{}, SizeIs(13) for: {?} has size == 13
Approx.tests.cpp:<line number>: passed: d == approx( 1.23 ) for: 1.23 == Approx( 1.23 )
Approx.tests.cpp:<line number>: passed: d == approx( 1.22 ) for: 1.23 == Approx( 1.22 )
Approx.tests.cpp:<line number>: passed: d == approx( 1.24 ) for: 1.23 == Approx( 1.24 )
Approx.tests.cpp:<line number>: passed: d != approx( 1.25 ) for: 1.23 != Approx( 1.25 )
Approx.tests.cpp:<line number>: passed: approx( d ) == 1.23 for: Approx( 1.23 ) == 1.23
Approx.tests.cpp:<line number>: passed: approx( d ) == 1.22 for: Approx( 1.23 ) == 1.22
Approx.tests.cpp:<line number>: passed: approx( d ) == 1.24 for: Approx( 1.23 ) == 1.24
Approx.tests.cpp:<line number>: passed: approx( d ) != 1.25 for: Approx( 1.23 ) != 1.25
VariadicMacros.tests.cpp:<line number>: passed: with 1 message: 'no assertions'
Matchers.tests.cpp:<line number>: passed: empty, Approx( empty ) for: {  } is approx: {  }
Matchers.tests.cpp:<line number>: passed: v1, Approx( v1 ) for: { 1.0, 2.0, 3.0 } is approx: { 1.0, 2.0, 3.0 }
Matchers.tests.cpp:<line number>: passed: v1, Approx<double>( { 1., 2., 3. } ) for: { 1.0, 2.0, 3.0 } is approx: { 1.0, 2.0, 3.0 }
Matchers.tests.cpp:<line number>: passed: v1, !Approx( temp ) for: { 1.0, 2.0, 3.0 } not is approx: { 1.0, 2.0, 3.0, 4.0 }
Matchers.tests.cpp:<line number>: passed: v1, !Approx( v2 ) for: { 1.0, 2.0, 3.0 } not is approx: { 1.5, 2.5, 3.5 }
Matchers.tests.cpp:<line number>: passed: v1, Approx( v2 ).margin( 0.5 ) for: { 1.0, 2.0, 3.0 } is approx: { 1.5, 2.5, 3.5 }
Matchers.tests.cpp:<line number>: passed: v1, Approx( v2 ).epsilon( 0.5 ) for: { 1.0, 2.0, 3.0 } is approx: { 1.5, 2.5, 3.5 }
Matchers.tests.cpp:<line number>: passed: v1, Approx( v2 ).epsilon( 0.1 ).scale( 500 ) for: { 1.0, 2.0, 3.0 } is approx: { 1.5, 2.5, 3.5 }
Matchers.tests.cpp:<line number>: failed: empty, Approx( t1 ) for: {  } is approx: { 1.0, 2.0 }
Matchers.tests.cpp:<line number>: failed: v1, Approx( v2 ) for: { 2.0, 4.0, 6.0 } is approx: { 1.0, 3.0, 5.0 }
Matchers.tests.cpp:<line number>: passed: v, VectorContains( 1 ) for: { 1, 2, 3 } Contains: 1
Matchers.tests.cpp:<line number>: passed: v, VectorContains( 2 ) for: { 1, 2, 3 } Contains: 2
Matchers.tests.cpp:<line number>: passed: v5, ( VectorContains<int, CustomAllocator<int>>( 2 ) ) for: { 1, 2, 3 } Contains: 2
Matchers.tests.cpp:<line number>: passed: v, Contains( v2 ) for: { 1, 2, 3 } Contains: { 1, 2 }
Matchers.tests.cpp:<line number>: passed: v, Contains<int>( { 1, 2 } ) for: { 1, 2, 3 } Contains: { 1, 2 }
Matchers.tests.cpp:<line number>: passed: v5, ( Contains<int, std::allocator<int>, CustomAllocator<int>>( v2 ) ) for: { 1, 2, 3 } Contains: { 1, 2 }
Matchers.tests.cpp:<line number>: passed: v, Contains( v2 ) for: { 1, 2, 3 } Contains: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v, Contains( empty ) for: { 1, 2, 3 } Contains: {  }
Matchers.tests.cpp:<line number>: passed: empty, Contains( empty ) for: {  } Contains: {  }
Matchers.tests.cpp:<line number>: passed: v5, ( Contains<int, std::allocator<int>, CustomAllocator<int>>( v2 ) ) for: { 1, 2, 3 } Contains: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v5, Contains( v6 ) for: { 1, 2, 3 } Contains: { 1, 2 }
Matchers.tests.cpp:<line number>: passed: v, VectorContains( 1 ) && VectorContains( 2 ) for: { 1, 2, 3 } ( Contains: 1 and Contains: 2 )
Matchers.tests.cpp:<line number>: passed: v, Equals( v ) for: { 1, 2, 3 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: empty, Equals( empty ) for: {  } Equals: {  }
Matchers.tests.cpp:<line number>: passed: v, Equals<int>( { 1, 2, 3 } ) for: { 1, 2, 3 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v, Equals( v2 ) for: { 1, 2, 3 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v5, ( Equals<int, std::allocator<int>, CustomAllocator<int>>( v2 ) ) for: { 1, 2, 3 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v5, Equals( v6 ) for: { 1, 2, 3 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v, UnorderedEquals( v ) for: { 1, 2, 3 } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v, UnorderedEquals<int>( { 3, 2, 1 } ) for: { 1, 2, 3 } UnorderedEquals: { 3, 2, 1 }
Matchers.tests.cpp:<line number>: passed: empty, UnorderedEquals( empty ) for: {  } UnorderedEquals: {  }
Matchers.tests.cpp:<line number>: passed: permuted, UnorderedEquals( v ) for: { 1, 3, 2 } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: permuted, UnorderedEquals( v ) for: { 2, 3, 1 } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: passed: v5, ( UnorderedEquals<int, std::allocator<int>, CustomAllocator<int>>( permuted ) ) for: { 1, 2, 3 } UnorderedEquals: { 2, 3, 1 }
Matchers.tests.cpp:<line number>: passed: v5_permuted, UnorderedEquals( v5 ) for: { 1, 3, 2 } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: v, VectorContains( -1 ) for: { 1, 2, 3 } Contains: -1
Matchers.tests.cpp:<line number>: failed: empty, VectorContains( 1 ) for: {  } Contains: 1
Matchers.tests.cpp:<line number>: failed: empty, Contains( v ) for: {  } Contains: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: v, Contains( v2 ) for: { 1, 2, 3 } Contains: { 1, 2, 4 }
Matchers.tests.cpp:<line number>: failed: v, Equals( v2 ) for: { 1, 2, 3 } Equals: { 1, 2 }
Matchers.tests.cpp:<line number>: failed: v2, Equals( v ) for: { 1, 2 } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: empty, Equals( v ) for: {  } Equals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: v, Equals( empty ) for: { 1, 2, 3 } Equals: {  }
Matchers.tests.cpp:<line number>: failed: v, UnorderedEquals( empty ) for: { 1, 2, 3 } UnorderedEquals: {  }
Matchers.tests.cpp:<line number>: failed: empty, UnorderedEquals( v ) for: {  } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: permuted, UnorderedEquals( v ) for: { 1, 3 } UnorderedEquals: { 1, 2, 3 }
Matchers.tests.cpp:<line number>: failed: permuted, UnorderedEquals( v ) for: { 3, 1 } UnorderedEquals: { 1, 2, 3 }
Exception.tests.cpp:<line number>: passed: thisThrows(), std::domain_error
Exception.tests.cpp:<line number>: passed: thisDoesntThrow()
Exception.tests.cpp:<line number>: passed: thisThrows()
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'unexpected exception'
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'expected exception'; expression was: thisThrows() == 0
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'expected exception'; expression was: thisThrows() == 0
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'expected exception'; expression was: thisThrows() == 0
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'unexpected exception'
Tricky.tests.cpp:<line number>: passed:
Tricky.tests.cpp:<line number>: passed:
Tricky.tests.cpp:<line number>: passed:
Tricky.tests.cpp:<line number>: passed:
Xml.tests.cpp:<line number>: passed: encode( "normal string" ) == "normal string" for: "normal string" == "normal string"
Xml.tests.cpp:<line number>: passed: encode( "" ) == "" for: "" == ""
Xml.tests.cpp:<line number>: passed: encode( "smith & jones" ) == "smith &amp; jones" for: "smith &amp; jones" == "smith &amp; jones"
Xml.tests.cpp:<line number>: passed: encode( "smith < jones" ) == "smith &lt; jones" for: "smith &lt; jones" == "smith &lt; jones"
Xml.tests.cpp:<line number>: passed: encode( "smith > jones" ) == "smith > jones" for: "smith > jones" == "smith > jones"
Xml.tests.cpp:<line number>: passed: encode( "smith ]]> jones" ) == "smith ]]&gt; jones" for: "smith ]]&gt; jones"
==
"smith ]]&gt; jones"
Xml.tests.cpp:<line number>: passed: encode( stringWithQuotes ) == stringWithQuotes for: "don't "quote" me on that"
==
"don't "quote" me on that"
Xml.tests.cpp:<line number>: passed: encode( stringWithQuotes, Catch::XmlEncode::ForAttributes ) == "don't &quot;quote&quot; me on that" for: "don't &quot;quote&quot; me on that"
==
"don't &quot;quote&quot; me on that"
Xml.tests.cpp:<line number>: passed: encode( "[\x01]" ) == "[\\x01]" for: "[\x01]" == "[\x01]"
Xml.tests.cpp:<line number>: passed: encode( "[\x7F]" ) == "[\\x7F]" for: "[\x7F]" == "[\x7F]"
Xml.tests.cpp:<line number>: passed: stream.str(), ContainsSubstring(R"(attr1="true")") && ContainsSubstring(R"(attr2="false")") for: "<?xml version="1.0" encoding="UTF-8"?>
<Element1 attr1="true" attr2="false"/>
" ( contains: "attr1="true"" and contains: "attr2="false"" )
Skip.tests.cpp:<line number>: passed:
Skip.tests.cpp:<line number>: skipped:
InternalBenchmark.tests.cpp:<line number>: passed: analysis.mean.point.count() == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: analysis.mean.lower_bound.count() == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: analysis.mean.upper_bound.count() == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: analysis.standard_deviation.point.count() == 0 for: 0.0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.standard_deviation.lower_bound.count() == 0 for: 0.0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.standard_deviation.upper_bound.count() == 0 for: 0.0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.total() == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.low_mild == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.low_severe == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.high_mild == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.high_severe == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outliers.samples_seen == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: analysis.outlier_variance == 0 for: 0.0 == 0
ToStringVector.tests.cpp:<line number>: passed: Catch::Detail::stringify( empty ) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: Catch::Detail::stringify( oneValue ) == "{ 42 }" for: "{ 42 }" == "{ 42 }"
ToStringVector.tests.cpp:<line number>: passed: Catch::Detail::stringify( twoValues ) == "{ 42, 250 }" for: "{ 42, 250 }" == "{ 42, 250 }"
InternalBenchmark.tests.cpp:<line number>: passed: model.started == 1 for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: model.finished == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: model.started == 1 for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: model.finished == 1 for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: called == 1 for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: model.started == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: model.finished == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: model.started == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: model.finished == 0 for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: called == 1 for: 1 == 1
Tricky.tests.cpp:<line number>: passed: obj.prop != 0 for: 0x<hex digits> != 0
Misc.tests.cpp:<line number>: passed: flag for: true
Misc.tests.cpp:<line number>: passed: testCheckedElse( true ) for: true
Misc.tests.cpp:<line number>: failed - but was ok: flag for: false
Misc.tests.cpp:<line number>: failed: testCheckedElse( false ) for: false
Misc.tests.cpp:<line number>: passed: flag for: true
Misc.tests.cpp:<line number>: passed: testCheckedIf( true ) for: true
Misc.tests.cpp:<line number>: failed - but was ok: flag for: false
Misc.tests.cpp:<line number>: failed: testCheckedIf( false ) for: false
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.samples_seen == static_cast<int>(x.size()) for: 6 == 6
InternalBenchmark.tests.cpp:<line number>: passed: o.low_severe == los for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.low_mild == lom for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.high_mild == him for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: o.high_severe == his for: 0 == 0
InternalBenchmark.tests.cpp:<line number>: passed: o.total() == los + lom + him + his for: 2 == 2
Condition.tests.cpp:<line number>: passed: unsigned_char_var == 1 for: 1 == 1
Condition.tests.cpp:<line number>: passed: unsigned_short_var == 1 for: 1 == 1
Condition.tests.cpp:<line number>: passed: unsigned_int_var == 1 for: 1 == 1
Condition.tests.cpp:<line number>: passed: unsigned_long_var == 1 for: 1 == 1
Condition.tests.cpp:<line number>: passed: long_var == unsigned_char_var for: 1 == 1
Condition.tests.cpp:<line number>: passed: long_var == unsigned_short_var for: 1 == 1
Condition.tests.cpp:<line number>: passed: long_var == unsigned_int_var for: 1 == 1
Condition.tests.cpp:<line number>: passed: long_var == unsigned_long_var for: 1 == 1
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( 0.f ) == 0 for: 0 == 0
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( -0.f ) == ( 1ULL << 31 ) for: 2147483648 (0x<hex digits>)
==
2147483648 (0x<hex digits>)
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( 0. ) == 0 for: 0 == 0
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( -0. ) == ( 1ULL << 63 ) for: 9223372036854775808 (0x<hex digits>)
==
9223372036854775808 (0x<hex digits>)
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( std::numeric_limits<float>::denorm_min() ) == 1 for: 1 == 1
FloatingPoint.tests.cpp:<line number>: passed: convertToBits( std::numeric_limits<double>::denorm_min() ) == 1 for: 1 == 1
Skip.tests.cpp:<line number>: skipped: 'skipping because answer = 41'
Skip.tests.cpp:<line number>: passed:
Skip.tests.cpp:<line number>: skipped: 'skipping because answer = 43'
Tag.tests.cpp:<line number>: passed: Catch::TestCaseInfo("", { "test with an empty tag", "[]" }, dummySourceLineInfo)
InternalBenchmark.tests.cpp:<line number>: passed: erfc_inv(1.103560) == Approx(-0.09203687623843015) for: -0.0920368762 == Approx( -0.0920368762 )
InternalBenchmark.tests.cpp:<line number>: passed: erfc_inv(1.067400) == Approx(-0.05980291115763361) for: -0.0598029112 == Approx( -0.0598029112 )
InternalBenchmark.tests.cpp:<line number>: passed: erfc_inv(0.050000) == Approx(1.38590382434967796) for: 1.3859038243 == Approx( 1.3859038243 )
InternalBenchmark.tests.cpp:<line number>: passed: res.mean.count() == rate for: 2000.0 == 2000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res.outliers.total() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed:
Skip.tests.cpp:<line number>: failed: 3 == 4
Skip.tests.cpp:<line number>: skipped:
Skip.tests.cpp:<line number>: failed: explicitly
Skip.tests.cpp:<line number>: skipped:
Skip.tests.cpp:<line number>: failed: explicitly
Skip.tests.cpp:<line number>: skipped:
Skip.tests.cpp:<line number>: skipped:
Skip.tests.cpp:<line number>: failed: explicitly
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary1)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary2)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary3)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary4)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary5)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: 'Catch::Clara::Detail::is_unary_function<decltype(unary6)>::value'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<decltype(binary1)>::value)'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<decltype(binary2)>::value)'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<decltype(nullary1)>::value)'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<decltype(nullary2)>::value)'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<int>::value)'
Clara.tests.cpp:<line number>: passed: with 1 message: '!(Catch::Clara::Detail::is_unary_function<std::string const&>::value)'
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'Previous info should not be seen'
Message.tests.cpp:<line number>: failed: explicitly with 1 message: 'previous unscoped info SHOULD not be seen'
Misc.tests.cpp:<line number>: passed: l == std::numeric_limits<long long>::max() for: 9223372036854775807 (0x<hex digits>)
==
9223372036854775807 (0x<hex digits>)
Misc.tests.cpp:<line number>: failed: b > a for: 0 > 1
Misc.tests.cpp:<line number>: failed: b > a for: 1 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 2 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 3 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 4 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 5 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 6 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 7 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 8 > 1
Misc.tests.cpp:<line number>: passed: b > a for: 9 > 1
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[0] (1) is even'
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[1] (1) is even'
Misc.tests.cpp:<line number>: passed: ( fib[i] % 2 ) == 0 for: 0 == 0 with 1 message: 'Testing if fib[2] (2) is even'
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[3] (3) is even'
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[4] (5) is even'
Misc.tests.cpp:<line number>: passed: ( fib[i] % 2 ) == 0 for: 0 == 0 with 1 message: 'Testing if fib[5] (8) is even'
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[6] (13) is even'
Misc.tests.cpp:<line number>: failed: ( fib[i] % 2 ) == 0 for: 1 == 0 with 1 message: 'Testing if fib[7] (21) is even'
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "%debug" )
UniquePtr.tests.cpp:<line number>: passed: !(lval.has_moved) for: !false
UniquePtr.tests.cpp:<line number>: passed: rval.has_moved for: true
UniquePtr.tests.cpp:<line number>: passed: *ptr == std::tuple<int, double, int>{1, 2., 3} for: {?} == {?}
InternalBenchmark.tests.cpp:<line number>: passed: m == 19. for: 19.0 == 19.0
InternalBenchmark.tests.cpp:<line number>: passed: x == 17 for: 17 == 17
InternalBenchmark.tests.cpp:<line number>: passed: x == 23 for: 23 == 23
InternalBenchmark.tests.cpp:<line number>: passed: r.elapsed.count() == 42 for: 42 == 42
InternalBenchmark.tests.cpp:<line number>: passed: r.result == 23 for: 23 == 23
InternalBenchmark.tests.cpp:<line number>: passed: r.iterations == 1 for: 1 == 1
InternalBenchmark.tests.cpp:<line number>: passed: s.elapsed.count() == 69 for: 69 == 69
InternalBenchmark.tests.cpp:<line number>: passed: s.result == 17 for: 17 == 17
InternalBenchmark.tests.cpp:<line number>: passed: s.iterations == 1 for: 1 == 1
Message.tests.cpp:<line number>: warning: 'info' with 2 messages: 'unscoped info' and 'and warn may mix'
Message.tests.cpp:<line number>: warning: 'info' with 2 messages: 'unscoped info' and 'they are not cleared after warnings'
Misc.tests.cpp:<line number>: failed: a == b for: 1 == 2
Misc.tests.cpp:<line number>: passed: a != b for: 1 != 2
Misc.tests.cpp:<line number>: passed: a < b for: 1 < 2
Misc.tests.cpp:<line number>: passed: a != b for: 1 != 2
Misc.tests.cpp:<line number>: passed: b != a for: 2 != 1
Misc.tests.cpp:<line number>: passed: a != b for: 1 != 2
Skip.tests.cpp:<line number>: skipped:
Tricky.tests.cpp:<line number>: passed: s == "7" for: "7" == "7"
Tricky.tests.cpp:<line number>: passed: ti == typeid(int) for: {?} == {?}
InternalBenchmark.tests.cpp:<line number>: passed: normal_cdf(0.000000) == Approx(0.50000000000000000) for: 0.5 == Approx( 0.5 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_cdf(1.000000) == Approx(0.84134474606854293) for: 0.8413447461 == Approx( 0.8413447461 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_cdf(-1.000000) == Approx(0.15865525393145705) for: 0.1586552539 == Approx( 0.1586552539 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_cdf(2.809729) == Approx(0.99752083845315409) for: 0.9975208385 == Approx( 0.9975208385 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_cdf(-1.352570) == Approx(0.08809652095066035) for: 0.088096521 == Approx( 0.088096521 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_quantile(0.551780) == Approx(0.13015979861484198) for: 0.1301597986 == Approx( 0.1301597986 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_quantile(0.533700) == Approx(0.08457408802851875) for: 0.084574088 == Approx( 0.084574088 )
InternalBenchmark.tests.cpp:<line number>: passed: normal_quantile(0.025000) == Approx(-1.95996398454005449) for: -1.9599639845 == Approx( -1.9599639845 )
Misc.tests.cpp:<line number>: passed:
Message.tests.cpp:<line number>: passed: true with 1 message: 'this MAY be seen only for the FIRST assertion IF info is printed for passing assertions'
Message.tests.cpp:<line number>: passed: true with 1 message: 'this MAY be seen only for the SECOND assertion IF info is printed for passing assertions'
Message.tests.cpp:<line number>: failed: false with 1 message: 'this SHOULD be seen'
Misc.tests.cpp:<line number>: passed: makeString( false ) != static_cast<char*>(0) for: "valid string" != {null string}
Misc.tests.cpp:<line number>: passed: makeString( true ) == static_cast<char*>(0) for: {null string} == {null string}
Tricky.tests.cpp:<line number>: passed: ptr.get() == 0 for: 0 == 0
ToStringPair.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( pair ) == "{ { 42, \"Arthur\" }, { \"Ford\", 24 } }" for: "{ { 42, "Arthur" }, { "Ford", 24 } }"
==
"{ { 42, "Arthur" }, { "Ford", 24 } }"
ToString.tests.cpp:<line number>: passed: parseEnums( "" ), Equals( std::vector<Catch::StringRef>{} ) for: {  } Equals: {  }
ToString.tests.cpp:<line number>: passed: parseEnums( "ClassName::EnumName::Value1" ), Equals(std::vector<Catch::StringRef>{"Value1"} ) for: { Value1 } Equals: { Value1 }
ToString.tests.cpp:<line number>: passed: parseEnums( "Value1" ), Equals( std::vector<Catch::StringRef>{"Value1"} ) for: { Value1 } Equals: { Value1 }
ToString.tests.cpp:<line number>: passed: parseEnums( "EnumName::Value1" ), Equals(std::vector<Catch::StringRef>{"Value1"} ) for: { Value1 } Equals: { Value1 }
ToString.tests.cpp:<line number>: passed: parseEnums( "ClassName::EnumName::Value1, ClassName::EnumName::Value2" ), Equals( std::vector<Catch::StringRef>{"Value1", "Value2"} ) for: { Value1, Value2 } Equals: { Value1, Value2 }
ToString.tests.cpp:<line number>: passed: parseEnums( "ClassName::EnumName::Value1, ClassName::EnumName::Value2, ClassName::EnumName::Value3" ), Equals( std::vector<Catch::StringRef>{"Value1", "Value2", "Value3"} ) for: { Value1, Value2, Value3 } Equals: { Value1, Value2, Value3 }
ToString.tests.cpp:<line number>: passed: parseEnums( "ClassName::EnumName::Value1,ClassName::EnumName::Value2 , ClassName::EnumName::Value3" ), Equals( std::vector<Catch::StringRef>{"Value1", "Value2", "Value3"} ) for: { Value1, Value2, Value3 } Equals: { Value1, Value2, Value3 }
Tricky.tests.cpp:<line number>: passed: p == 0 for: 0 == 0
Message.tests.cpp:<line number>: passed: true with 1 message: 'this MAY be seen IF info is printed for passing assertions'
Message.tests.cpp:<line number>: failed: false with 2 messages: 'this SHOULD be seen' and 'this SHOULD also be seen'
Message.tests.cpp:<line number>: failed: false with 1 message: 'this SHOULD be seen only ONCE'
Message.tests.cpp:<line number>: passed: true
Message.tests.cpp:<line number>: passed: true with 1 message: 'this MAY also be seen only ONCE IF info is printed for passing assertions'
Message.tests.cpp:<line number>: passed: true
Misc.tests.cpp:<line number>: passed: a != b for: 1 != 2
Misc.tests.cpp:<line number>: passed: b != a for: 2 != 1
Misc.tests.cpp:<line number>: passed: a != b for: 1 != 2
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(letters, "b", "z") for: true
StringManip.tests.cpp:<line number>: passed: letters == "azcdefcg" for: "azcdefcg" == "azcdefcg"
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(letters, "c", "z") for: true
StringManip.tests.cpp:<line number>: passed: letters == "abzdefzg" for: "abzdefzg" == "abzdefzg"
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(letters, "a", "z") for: true
StringManip.tests.cpp:<line number>: passed: letters == "zbcdefcg" for: "zbcdefcg" == "zbcdefcg"
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(letters, "g", "z") for: true
StringManip.tests.cpp:<line number>: passed: letters == "abcdefcz" for: "abcdefcz" == "abcdefcz"
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(letters, letters, "replaced") for: true
StringManip.tests.cpp:<line number>: passed: letters == "replaced" for: "replaced" == "replaced"
StringManip.tests.cpp:<line number>: passed: !(Catch::replaceInPlace(letters, "x", "z")) for: !false
StringManip.tests.cpp:<line number>: passed: letters == letters for: "abcdefcg" == "abcdefcg"
StringManip.tests.cpp:<line number>: passed: Catch::replaceInPlace(s, "'", "|'") for: true
StringManip.tests.cpp:<line number>: passed: s == "didn|'t" for: "didn|'t" == "didn|'t"
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "%somestream" )
InternalBenchmark.tests.cpp:<line number>: passed: res.size() == count for: 10 == 10
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: res[i] == rate for: 1000.0 == 1000 (0x<hex digits>)
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 1 >= 1
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 2 >= 1
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 4 >= 2
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 8 >= 4
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 16 >= 8
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 32 >= 16
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 64 >= 32
InternalBenchmark.tests.cpp:<line number>: passed: meter.runs() >= old_runs for: 128 >= 64
InternalBenchmark.tests.cpp:<line number>: passed: Timing.elapsed >= time for: 128 ns >= 100 ns
InternalBenchmark.tests.cpp:<line number>: passed: Timing.result == Timing.iterations + 17 for: 145 == 145
InternalBenchmark.tests.cpp:<line number>: passed: Timing.iterations >= time.count() for: 128 >= 100
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 1 >= 1
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 2 >= 1
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 4 >= 2
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 8 >= 4
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 16 >= 8
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 32 >= 16
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 64 >= 32
InternalBenchmark.tests.cpp:<line number>: passed: x >= old_x for: 128 >= 64
InternalBenchmark.tests.cpp:<line number>: passed: Timing.elapsed >= time for: 128 ns >= 100 ns
InternalBenchmark.tests.cpp:<line number>: passed: Timing.result == Timing.iterations + 17 for: 145 == 145
InternalBenchmark.tests.cpp:<line number>: passed: Timing.iterations >= time.count() for: 128 >= 100
Skip.tests.cpp:<line number>: passed:
Skip.tests.cpp:<line number>: skipped:
Skip.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: failed: false with 1 message: '3'
Message.tests.cpp:<line number>: failed: false with 2 messages: 'hi' and 'i := 7'
Tag.tests.cpp:<line number>: passed: testcase.tags, VectorContains( Tag( "magic-tag" ) ) && VectorContains( Tag( "."_catch_sr ) ) for: { {?}, {?} } ( Contains: {?} and Contains: {?} )
Skip.tests.cpp:<line number>: skipped: 'skipping because answer = 43'
StringManip.tests.cpp:<line number>: passed: splitStringRef("", ','), Equals(std::vector<StringRef>()) for: {  } Equals: {  }
StringManip.tests.cpp:<line number>: passed: splitStringRef("abc", ','), Equals(std::vector<StringRef>{"abc"}) for: { abc } Equals: { abc }
StringManip.tests.cpp:<line number>: passed: splitStringRef("abc,def", ','), Equals(std::vector<StringRef>{"abc", "def"}) for: { abc, def } Equals: { abc, def }
Message.tests.cpp:<line number>: failed: false with 4 messages: 'Count 1 to 3...' and '1' and '2' and '3'
Message.tests.cpp:<line number>: failed: false with 4 messages: 'Count 4 to 6...' and '4' and '5' and '6'
StringManip.tests.cpp:<line number>: passed: !(startsWith("", 'c')) for: !false
StringManip.tests.cpp:<line number>: passed: startsWith(std::string("abc"), 'a') for: true
StringManip.tests.cpp:<line number>: passed: startsWith("def"_catch_sr, 'd') for: true
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( emptyMap ) == "{  }" for: "{  }" == "{  }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( map ) == "{ { \"one\", 1 } }" for: "{ { "one", 1 } }" == "{ { "one", 1 } }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( map ) == "{ { \"abc\", 1 }, { \"def\", 2 }, { \"ghi\", 3 } }" for: "{ { "abc", 1 }, { "def", 2 }, { "ghi", 3 } }"
==
"{ { "abc", 1 }, { "def", 2 }, { "ghi", 3 } }"
ToStringPair.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(value) == "{ 34, \"xyzzy\" }" for: "{ 34, "xyzzy" }" == "{ 34, "xyzzy" }"
ToStringPair.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( value ) == "{ 34, \"xyzzy\" }" for: "{ 34, "xyzzy" }" == "{ 34, "xyzzy" }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( emptySet ) == "{  }" for: "{  }" == "{  }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( set ) == "{ \"one\" }" for: "{ "one" }" == "{ "one" }"
ToStringGeneral.tests.cpp:<line number>: passed: Catch::Detail::stringify( set ) == "{ \"abc\", \"def\", \"ghi\" }" for: "{ "abc", "def", "ghi" }"
==
"{ "abc", "def", "ghi" }"
ToStringPair.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( pr ) == "{ { \"green\", 55 } }" for: "{ { "green", 55 } }"
==
"{ { "green", 55 } }"
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "%stderr" )->isConsole() for: true
Stream.tests.cpp:<line number>: passed: Catch::makeStream( "%stdout" )->isConsole() for: true
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(streamable_range{}) == "op<<(streamable_range)" for: "op<<(streamable_range)"
==
"op<<(streamable_range)"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(stringmaker_range{}) == "stringmaker(streamable_range)" for: "stringmaker(streamable_range)"
==
"stringmaker(streamable_range)"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(just_range{}) == "{ 1, 2, 3, 4 }" for: "{ 1, 2, 3, 4 }" == "{ 1, 2, 3, 4 }"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(disabled_range{}) == "{?}" for: "{?}" == "{?}"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( item ) == "StringMaker<has_maker>" for: "StringMaker<has_maker>"
==
"StringMaker<has_maker>"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( item ) == "StringMaker<has_maker_and_operator>" for: "StringMaker<has_maker_and_operator>"
==
"StringMaker<has_maker_and_operator>"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(item) == "{?}" for: "{?}" == "{?}"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( item ) == "operator<<( has_operator )" for: "operator<<( has_operator )"
==
"operator<<( has_operator )"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( item ) == "operator<<( has_template_operator )" for: "operator<<( has_template_operator )"
==
"operator<<( has_template_operator )"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( v ) == "{ StringMaker<has_maker> }" for: "{ StringMaker<has_maker> }"
==
"{ StringMaker<has_maker> }"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( v ) == "{ StringMaker<has_maker_and_operator> }" for: "{ StringMaker<has_maker_and_operator> }"
==
"{ StringMaker<has_maker_and_operator> }"
ToStringWhich.tests.cpp:<line number>: passed: ::Catch::Detail::stringify( v ) == "{ operator<<( has_operator ) }" for: "{ operator<<( has_operator ) }"
==
"{ operator<<( has_operator ) }"
Generators.tests.cpp:<line number>: passed: data.str.size() == data.len for: 3 == 3
Generators.tests.cpp:<line number>: passed: data.str.size() == data.len for: 3 == 3
Generators.tests.cpp:<line number>: passed: data.str.size() == data.len for: 5 == 5
Generators.tests.cpp:<line number>: passed: data.str.size() == data.len for: 4 == 4
Generators.tests.cpp:<line number>: passed: strlen(std::get<0>(data)) == static_cast<size_t>(std::get<1>(data)) for: 5 == 5
Generators.tests.cpp:<line number>: passed: strlen(std::get<0>(data)) == static_cast<size_t>(std::get<1>(data)) for: 6 == 6
Generators.tests.cpp:<line number>: passed: strlen(std::get<0>(data)) == static_cast<size_t>(std::get<1>(data)) for: 5 == 5
Generators.tests.cpp:<line number>: passed: strlen(std::get<0>(data)) == static_cast<size_t>(std::get<1>(data)) for: 6 == 6
Tag.tests.cpp:<line number>: passed: testcase.tags.size() == 1 for: 1 == 1
Tag.tests.cpp:<line number>: passed: testcase.tags[0].original == "magic.tag"_catch_sr for: magic.tag == magic.tag
Skip.tests.cpp:<line number>: skipped:
Exception.tests.cpp:<line number>: failed: unexpected exception with message: 'Why would you throw a std::string?'
Misc.tests.cpp:<line number>: passed: result == "\"wide load\"" for: ""wide load"" == ""wide load""
Misc.tests.cpp:<line number>: passed: result == "\"wide load\"" for: ""wide load"" == ""wide load""
Misc.tests.cpp:<line number>: passed: result == "\"wide load\"" for: ""wide load"" == ""wide load""
Misc.tests.cpp:<line number>: passed: result == "\"wide load\"" for: ""wide load"" == ""wide load""
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e0) == "E2/V0" for: "E2/V0" == "E2/V0"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e1) == "E2/V1" for: "E2/V1" == "E2/V1"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e3) == "Unknown enum value 10" for: "Unknown enum value 10"
==
"Unknown enum value 10"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e0) == "0" for: "0" == "0"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e1) == "1" for: "1" == "1"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e0) == "E2{0}" for: "E2{0}" == "E2{0}"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e1) == "E2{1}" for: "E2{1}" == "E2{1}"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e0) == "0" for: "0" == "0"
EnumToString.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(e1) == "1" for: "1" == "1"
ToStringTuple.tests.cpp:<line number>: passed: "{ }" == ::Catch::Detail::stringify(type{}) for: "{ }" == "{ }"
ToStringTuple.tests.cpp:<line number>: passed: "{ }" == ::Catch::Detail::stringify(value) for: "{ }" == "{ }"
ToStringTuple.tests.cpp:<line number>: passed: "1.2f" == ::Catch::Detail::stringify(float(1.2)) for: "1.2f" == "1.2f"
ToStringTuple.tests.cpp:<line number>: passed: "{ 1.2f, 0 }" == ::Catch::Detail::stringify(type{1.2f,0}) for: "{ 1.2f, 0 }" == "{ 1.2f, 0 }"
ToStringTuple.tests.cpp:<line number>: passed: "{ 0 }" == ::Catch::Detail::stringify(type{0}) for: "{ 0 }" == "{ 0 }"
ToStringTuple.tests.cpp:<line number>: passed: "{ \"hello\", \"world\" }" == ::Catch::Detail::stringify(type{"hello","world"}) for: "{ "hello", "world" }"
==
"{ "hello", "world" }"
ToStringTuple.tests.cpp:<line number>: passed: "{ { 42 }, { }, 1.2f }" == ::Catch::Detail::stringify(value) for: "{ { 42 }, { }, 1.2f }"
==
"{ { 42 }, { }, 1.2f }"
InternalBenchmark.tests.cpp:<line number>: passed: e.point == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: e.upper_bound == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: e.lower_bound == 23 for: 23.0 == 23
InternalBenchmark.tests.cpp:<line number>: passed: e.confidence_interval == 0.95 for: 0.95 == 0.95
UniquePtr.tests.cpp:<line number>: passed: !(ptr) for: !{?}
UniquePtr.tests.cpp:<line number>: passed: ptr.get() == 0 for: 0 == 0
UniquePtr.tests.cpp:<line number>: passed: ptr for: {?}
UniquePtr.tests.cpp:<line number>: passed: *ptr == 0 for: 0 == 0
UniquePtr.tests.cpp:<line number>: passed: ptr.get() == naked_ptr for: 0x<hex digits> == 0x<hex digits>
UniquePtr.tests.cpp:<line number>: passed: !(ptr) for: !{?}
UniquePtr.tests.cpp:<line number>: passed: ptr.get() == 0 for: 0 == 0
UniquePtr.tests.cpp:<line number>: passed: ptr for: {?}
UniquePtr.tests.cpp:<line number>: passed: *ptr == 0 for: 0 == 0
UniquePtr.tests.cpp:<line number>: passed: ptr.get() == naked_ptr for: 0x<hex digits> == 0x<hex digits>
UniquePtr.tests.cpp:<line number>: passed: ptr for: {?}
UniquePtr.tests.cpp:<line number>: passed: ptr.get() != 0 for: 0x<hex digits> != 0
UniquePtr.tests.cpp:<line number>: passed: *ptr == 2 for: 2 == 2
UniquePtr.tests.cpp:<line number>: passed: !(ptr) for: !{?}
UniquePtr.tests.cpp:<line number>: passed: ptr.get() == 0 for: 0 == 0
UniquePtr.tests.cpp:<line number>: passed: !(ptr1) for: !{?}
UniquePtr.tests.cpp:<line number>: passed: ptr2 for: {?}
UniquePtr.tests.cpp:<line number>: passed: *ptr2 == 1 for: 1 == 1
UniquePtr.tests.cpp:<line number>: passed: !(ptr2) for: !{?}
UniquePtr.tests.cpp:<line number>: passed: ptr1 for: {?}
UniquePtr.tests.cpp:<line number>: passed: *ptr1 == 2 for: 2 == 2
UniquePtr.tests.cpp:<line number>: passed: *ptr1 == 2 for: 2 == 2
UniquePtr.tests.cpp:<line number>: passed: *ptr2 == 1 for: 1 == 1
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(v) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(v) == "{ { \"hello\" }, { \"world\" } }" for: "{ { "hello" }, { "world" } }"
==
"{ { "hello" }, { "world" } }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(bools) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(bools) == "{ true }" for: "{ true }" == "{ true }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(bools) == "{ true, false }" for: "{ true, false }" == "{ true, false }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ 42 }" for: "{ 42 }" == "{ 42 }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ 42, 250 }" for: "{ 42, 250 }" == "{ 42, 250 }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ 42 }" for: "{ 42 }" == "{ 42 }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ 42, 250 }" for: "{ 42, 250 }" == "{ 42, 250 }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{  }" for: "{  }" == "{  }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ \"hello\" }" for: "{ "hello" }" == "{ "hello" }"
ToStringVector.tests.cpp:<line number>: passed: ::Catch::Detail::stringify(vv) == "{ \"hello\", \"world\" }" for: "{ "hello", "world" }"
==
"{ "hello", "world" }"
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 10 for: 10 == 10
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.capacity() == 0 for: 0 == 0
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 10 for: 10 >= 10
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
Misc.tests.cpp:<line number>: passed: v.size() == 5 for: 5 == 5
Misc.tests.cpp:<line number>: passed: v.capacity() >= 5 for: 5 >= 5
InternalBenchmark.tests.cpp:<line number>: passed: (iterations * rate) > Catch::Benchmark::Detail::warmup_time.count() for: 160000000 (0x<hex digits>) > 100
InternalBenchmark.tests.cpp:<line number>: passed: (end - start) > Catch::Benchmark::Detail::warmup_time for: 310016000 ns > 100 ms
InternalBenchmark.tests.cpp:<line number>: passed: q1 == 14.5 for: 14.5 == 14.5
InternalBenchmark.tests.cpp:<line number>: passed: med == 18. for: 18.0 == 18.0
InternalBenchmark.tests.cpp:<line number>: passed: q3 == 23. for: 23.0 == 23.0
Misc.tests.cpp:<line number>: passed:
Misc.tests.cpp:<line number>: passed:
test cases:  409 |  308 passed |  84 failed | 6 skipped | 11 failed as expected
assertions: 2225 | 2048 passed | 145 failed | 32 failed as expected


