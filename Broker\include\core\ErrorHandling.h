#pragma once

#include "core/Types.h"
#include <exception>
#include <string>
#include <optional>
#include <functional>
#include <unordered_map>
#include <chrono>
// Note: source_location and stacktrace require C++20/23, using alternatives for compatibility
// #include <source_location>
// #include <stacktrace>

namespace RoboQuant::Broker {

// Enhanced error information
struct ErrorInfo {
    ErrorCode code{ErrorCode::Unknown};
    std::string message;
    std::string category;
    // Using string for location info instead of source_location for compatibility
    std::string location_info;
    std::optional<std::string> stack_trace_info;
    std::unordered_map<std::string, std::string> context;
    TimePoint timestamp{std::chrono::system_clock::now()};

    ErrorInfo() = default;
    ErrorInfo(ErrorCode c, std::string msg, std::string cat = "")
        : code(c), message(std::move(msg)), category(std::move(cat)) {}
    
    // Add context information
    ErrorInfo& with_context(const std::string& key, const std::string& value) {
        context[key] = value;
        return *this;
    }
    
    // Add stack trace
    ErrorInfo& with_stack_trace() {
        stack_trace = std::stacktrace::current();
        return *this;
    }
    
    // Severity assessment
    enum class Severity { Info, Warning, Error, Critical };
    Severity get_severity() const;
    
    // Serialization
    nlohmann::json to_json() const;
    static ErrorInfo from_json(const nlohmann::json& j);
    
    std::string to_string() const;
};

// Enhanced Result type with detailed error information
template<typename T>
class Result {
public:
    // Constructors
    Result(T&& value) : value_(std::move(value)), has_value_(true) {}
    Result(const T& value) : value_(value), has_value_(true) {}
    Result(ErrorCode code) : error_(code, to_string(code)), has_value_(false) {}
    Result(ErrorInfo error) : error_(std::move(error)), has_value_(false) {}
    
    // Factory methods
    static Result success(T&& value) { return Result(std::move(value)); }
    static Result success(const T& value) { return Result(value); }
    static Result failure(ErrorCode code, const std::string& message = "") {
        ErrorInfo error(code, message.empty() ? to_string(code) : message);
        return Result(std::move(error));
    }
    static Result failure(ErrorInfo error) { return Result(std::move(error)); }
    
    // Status checking
    bool has_value() const noexcept { return has_value_; }
    bool has_error() const noexcept { return !has_value_; }
    explicit operator bool() const noexcept { return has_value_; }
    
    // Value access
    T& value() & {
        if (!has_value_) throw std::runtime_error("Accessing value of failed Result");
        return value_;
    }
    
    const T& value() const & {
        if (!has_value_) throw std::runtime_error("Accessing value of failed Result");
        return value_;
    }
    
    T&& value() && {
        if (!has_value_) throw std::runtime_error("Accessing value of failed Result");
        return std::move(value_);
    }
    
    T value_or(const T& default_value) const & {
        return has_value_ ? value_ : default_value;
    }
    
    T value_or(T&& default_value) const & {
        return has_value_ ? value_ : std::move(default_value);
    }
    
    // Error access
    const ErrorInfo& error() const {
        if (has_value_) throw std::runtime_error("Accessing error of successful Result");
        return error_;
    }
    
    ErrorCode error_code() const {
        return has_value_ ? ErrorCode::Success : error_.code;
    }
    
    // Monadic operations
    template<typename F>
    auto and_then(F&& f) -> Result<std::invoke_result_t<F, T>> {
        using U = std::invoke_result_t<F, T>;
        if (has_value_) {
            return std::invoke(std::forward<F>(f), value_);
        } else {
            return Result<U>::failure(error_);
        }
    }
    
    template<typename F>
    auto or_else(F&& f) -> Result<T> {
        if (has_value_) {
            return *this;
        } else {
            return std::invoke(std::forward<F>(f), error_);
        }
    }
    
    template<typename F>
    auto transform(F&& f) -> Result<std::invoke_result_t<F, T>> {
        using U = std::invoke_result_t<F, T>;
        if (has_value_) {
            return Result<U>::success(std::invoke(std::forward<F>(f), value_));
        } else {
            return Result<U>::failure(error_);
        }
    }

private:
    T value_{};
    ErrorInfo error_;
    bool has_value_{false};
};

// Specialization for void
template<>
class Result<void> {
public:
    Result() : has_value_(true) {}
    Result(ErrorCode code) : error_(code, to_string(code)), has_value_(false) {}
    Result(ErrorInfo error) : error_(std::move(error)), has_value_(false) {}
    
    static Result success() { return Result(); }
    static Result failure(ErrorCode code, const std::string& message = "") {
        ErrorInfo error(code, message.empty() ? to_string(code) : message);
        return Result(std::move(error));
    }
    static Result failure(ErrorInfo error) { return Result(std::move(error)); }
    
    bool has_value() const noexcept { return has_value_; }
    bool has_error() const noexcept { return !has_value_; }
    explicit operator bool() const noexcept { return has_value_; }
    
    const ErrorInfo& error() const {
        if (has_value_) throw std::runtime_error("Accessing error of successful Result");
        return error_;
    }
    
    ErrorCode error_code() const {
        return has_value_ ? ErrorCode::Success : error_.code;
    }

private:
    ErrorInfo error_;
    bool has_value_{false};
};

// Exception classes
class BrokerException : public std::exception {
public:
    explicit BrokerException(ErrorInfo error) : error_(std::move(error)) {}
    BrokerException(ErrorCode code, const std::string& message)
        : error_(code, message) {}
    
    const char* what() const noexcept override {
        return error_.message.c_str();
    }
    
    const ErrorInfo& error_info() const noexcept { return error_; }
    ErrorCode error_code() const noexcept { return error_.code; }

private:
    ErrorInfo error_;
};

class OrderException : public BrokerException {
public:
    OrderException(ErrorCode code, const std::string& message, const OrderId& order_id = "")
        : BrokerException(code, message), order_id_(order_id) {
        if (!order_id_.empty()) {
            error_.with_context("order_id", order_id_);
        }
    }
    
    const OrderId& order_id() const noexcept { return order_id_; }

private:
    OrderId order_id_;
    ErrorInfo error_;
};

class AccountException : public BrokerException {
public:
    AccountException(ErrorCode code, const std::string& message, const AccountId& account_id = "")
        : BrokerException(code, message), account_id_(account_id) {
        if (!account_id_.empty()) {
            error_.with_context("account_id", account_id_);
        }
    }
    
    const AccountId& account_id() const noexcept { return account_id_; }

private:
    AccountId account_id_;
    ErrorInfo error_;
};

class ConnectionException : public BrokerException {
public:
    ConnectionException(ErrorCode code, const std::string& message, BrokerType broker_type = BrokerType::Unknown)
        : BrokerException(code, message), broker_type_(broker_type) {
        error_.with_context("broker_type", to_string(broker_type_));
    }
    
    BrokerType broker_type() const noexcept { return broker_type_; }

private:
    BrokerType broker_type_;
    ErrorInfo error_;
};

// Error handling utilities
class ErrorHandler {
public:
    using ErrorCallback = std::function<void(const ErrorInfo&)>;
    
    static ErrorHandler& instance();
    
    void set_error_callback(ErrorCallback callback);
    void handle_error(const ErrorInfo& error);
    void handle_error(ErrorCode code, const std::string& message, const std::string& category = "");
    
    // Error statistics
    size_t total_errors() const { return total_errors_; }
    size_t errors_by_code(ErrorCode code) const;
    std::vector<ErrorInfo> recent_errors(size_t count = 10) const;
    
    // Error recovery
    void register_recovery_handler(ErrorCode code, std::function<bool(const ErrorInfo&)> handler);
    bool try_recover(const ErrorInfo& error);

private:
    ErrorHandler() = default;
    
    ErrorCallback error_callback_;
    std::atomic<size_t> total_errors_{0};
    mutable std::shared_mutex errors_mutex_;
    std::unordered_map<ErrorCode, size_t> error_counts_;
    std::vector<ErrorInfo> error_history_;
    std::unordered_map<ErrorCode, std::function<bool(const ErrorInfo&)>> recovery_handlers_;
    
    static constexpr size_t MAX_ERROR_HISTORY = 1000;
};

// Macros for convenient error handling
#define BROKER_RESULT_TRY(expr) \
    do { \
        auto result = (expr); \
        if (!result) { \
            return Result<decltype(result.value())>::failure(result.error()); \
        } \
    } while(0)

#define BROKER_RESULT_ASSIGN(var, expr) \
    auto result_##var = (expr); \
    if (!result_##var) { \
        return Result<decltype(result_##var.value())>::failure(result_##var.error()); \
    } \
    auto var = std::move(result_##var.value())

#define BROKER_THROW_ON_ERROR(result) \
    do { \
        if (!(result)) { \
            throw BrokerException((result).error()); \
        } \
    } while(0)

// Utility functions
template<typename T>
bool is_success(const Result<T>& result) {
    return result.has_value();
}

template<typename T>
bool is_error(const Result<T>& result) {
    return result.has_error();
}

template<typename T>
T& get_value(Result<T>& result) {
    return result.value();
}

template<typename T>
const T& get_value(const Result<T>& result) {
    return result.value();
}

template<typename T>
ErrorCode get_error_code(const Result<T>& result) {
    return result.error_code();
}

template<typename T>
const ErrorInfo& get_error_info(const Result<T>& result) {
    return result.error();
}

// Error logging integration
void log_error(const ErrorInfo& error);
void log_error(ErrorCode code, const std::string& message, const std::string& category = "");

// Error metrics
struct ErrorMetrics {
    size_t total_errors{0};
    std::unordered_map<ErrorCode, size_t> error_counts;
    std::unordered_map<std::string, size_t> category_counts;
    TimePoint last_error_time;
    double error_rate{0.0}; // errors per second
    
    nlohmann::json to_json() const;
    static ErrorMetrics from_json(const nlohmann::json& j);
};

ErrorMetrics get_error_metrics();
void reset_error_metrics();

} // namespace RoboQuant::Broker
