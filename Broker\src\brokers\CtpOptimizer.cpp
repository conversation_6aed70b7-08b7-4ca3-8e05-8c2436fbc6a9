#include "brokers/CtpOptimizer.h"
#include <spdlog/spdlog.h>
#include <algorithm>
#include <numeric>

namespace RoboQuant::Broker {

// CtpPerformanceStats 实现
double CtpPerformanceStats::get_success_rate() const {
    uint64_t total = total_requests.load();
    if (total == 0) return 1.0;
    return static_cast<double>(successful_requests.load()) / total;
}

double CtpPerformanceStats::get_requests_per_second() const {
    auto uptime = get_uptime_seconds();
    if (uptime <= 0) return 0.0;
    return static_cast<double>(total_requests.load()) / uptime;
}

double CtpPerformanceStats::get_uptime_seconds() const {
    auto now = std::chrono::system_clock::now();
    return std::chrono::duration<double>(now - start_time).count();
}

nlohmann::json CtpPerformanceStats::to_json() const {
    nlohmann::json j;
    j["connect_count"] = connect_count.load();
    j["disconnect_count"] = disconnect_count.load();
    j["reconnect_count"] = reconnect_count.load();
    j["auth_success_count"] = auth_success_count.load();
    j["auth_failure_count"] = auth_failure_count.load();
    j["total_requests"] = total_requests.load();
    j["successful_requests"] = successful_requests.load();
    j["failed_requests"] = failed_requests.load();
    j["timeout_requests"] = timeout_requests.load();
    j["orders_submitted"] = orders_submitted.load();
    j["orders_filled"] = orders_filled.load();
    j["orders_cancelled"] = orders_cancelled.load();
    j["orders_rejected"] = orders_rejected.load();
    j["avg_request_latency_ms"] = avg_request_latency_ms.load();
    j["avg_order_latency_ms"] = avg_order_latency_ms.load();
    j["max_request_latency_ms"] = max_request_latency_ms.load();
    j["max_order_latency_ms"] = max_order_latency_ms.load();
    j["network_errors"] = network_errors.load();
    j["api_errors"] = api_errors.load();
    j["timeout_errors"] = timeout_errors.load();
    j["auth_errors"] = auth_errors.load();
    j["success_rate"] = get_success_rate();
    j["requests_per_second"] = get_requests_per_second();
    j["uptime_seconds"] = get_uptime_seconds();
    return j;
}

void CtpPerformanceStats::reset() {
    connect_count.store(0);
    disconnect_count.store(0);
    reconnect_count.store(0);
    auth_success_count.store(0);
    auth_failure_count.store(0);
    total_requests.store(0);
    successful_requests.store(0);
    failed_requests.store(0);
    timeout_requests.store(0);
    orders_submitted.store(0);
    orders_filled.store(0);
    orders_cancelled.store(0);
    orders_rejected.store(0);
    avg_request_latency_ms.store(0.0);
    avg_order_latency_ms.store(0.0);
    max_request_latency_ms.store(0.0);
    max_order_latency_ms.store(0.0);
    network_errors.store(0);
    api_errors.store(0);
    timeout_errors.store(0);
    auth_errors.store(0);
    start_time = std::chrono::system_clock::now();
    last_update_time.store(start_time);
}

// CtpRequestOptimizer 实现
CtpRequestOptimizer::CtpRequestOptimizer(int base_interval_ms) 
    : base_interval_ms_(base_interval_ms), current_interval_ms_(base_interval_ms) {
}

void CtpRequestOptimizer::update_request_result(bool success, double latency_ms) {
    total_requests_.fetch_add(1);
    if (success) {
        successful_requests_.fetch_add(1);
    }
    
    // 更新平均延迟
    auto current_total_latency = total_latency_ms_.load();
    auto new_total_latency = current_total_latency + latency_ms;
    total_latency_ms_.store(new_total_latency);
    
    // 自适应调整间隔
    adaptive_interval_adjustment();
}

int CtpRequestOptimizer::get_optimal_interval_ms() const {
    return current_interval_ms_.load();
}

void CtpRequestOptimizer::add_request(std::function<int()> request, int priority) {
    RequestItem item;
    item.request = std::move(request);
    item.priority = priority;
    item.retry_count = 0;
    item.max_retries = 0;
    item.submit_time = std::chrono::system_clock::now();
    
    std::unique_lock<std::mutex> lock(queue_mutex_);
    request_queue_.push(item);
}

void CtpRequestOptimizer::add_retry_request(std::function<int()> request, int max_retries) {
    RequestItem item;
    item.request = std::move(request);
    item.priority = 100; // 重试请求高优先级
    item.retry_count = 0;
    item.max_retries = max_retries;
    item.submit_time = std::chrono::system_clock::now();
    
    std::unique_lock<std::mutex> lock(queue_mutex_);
    request_queue_.push(item);
}

void CtpRequestOptimizer::process_batch() {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    
    if (request_queue_.empty()) {
        return;
    }
    
    // 处理最高优先级的请求
    auto item = request_queue_.top();
    request_queue_.pop();
    lock.unlock();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        int result = item.request();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto latency = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        bool success = (result == 0);
        update_request_result(success, latency);
        
        if (!success && item.retry_count < item.max_retries) {
            // 重试
            item.retry_count++;
            std::unique_lock<std::mutex> retry_lock(queue_mutex_);
            request_queue_.push(item);
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in request processing: {}", e.what());
        update_request_result(false, 0.0);
    }
}

double CtpRequestOptimizer::get_success_rate() const {
    uint64_t total = total_requests_.load();
    if (total == 0) return 1.0;
    return static_cast<double>(successful_requests_.load()) / total;
}

double CtpRequestOptimizer::get_average_latency() const {
    uint64_t total = total_requests_.load();
    if (total == 0) return 0.0;
    return total_latency_ms_.load() / total;
}

size_t CtpRequestOptimizer::get_pending_requests() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return request_queue_.size();
}

void CtpRequestOptimizer::adaptive_interval_adjustment() {
    double success_rate = get_success_rate();
    double avg_latency = get_average_latency();
    
    int current_interval = current_interval_ms_.load();
    int new_interval = current_interval;
    
    // 根据成功率和延迟调整间隔
    if (success_rate < SUCCESS_RATE_THRESHOLD || avg_latency > LATENCY_THRESHOLD_MS) {
        // 性能不佳，增加间隔
        new_interval = std::min(current_interval * 2, MAX_INTERVAL_MS);
    } else if (success_rate > 0.98 && avg_latency < LATENCY_THRESHOLD_MS / 2) {
        // 性能良好，减少间隔
        new_interval = std::max(current_interval / 2, MIN_INTERVAL_MS);
    }
    
    if (new_interval != current_interval) {
        current_interval_ms_.store(new_interval);
        spdlog::debug("CTP request interval adjusted to: {}ms", new_interval);
    }
}

// CtpErrorRecovery 实现
CtpErrorRecovery::CtpErrorRecovery() {
    // 设置默认恢复策略
    RecoveryStrategy network_strategy;
    network_strategy.max_retry_count = 3;
    network_strategy.retry_interval = std::chrono::seconds(5);
    network_strategy.auto_reconnect = true;
    network_strategy.reset_session = false;
    strategies_[ErrorType::NetworkError] = network_strategy;
    
    RecoveryStrategy auth_strategy;
    auth_strategy.max_retry_count = 2;
    auth_strategy.retry_interval = std::chrono::seconds(10);
    auth_strategy.auto_reconnect = true;
    auth_strategy.reset_session = true;
    strategies_[ErrorType::AuthError] = auth_strategy;
    
    RecoveryStrategy api_strategy;
    api_strategy.max_retry_count = 1;
    api_strategy.retry_interval = std::chrono::seconds(1);
    api_strategy.auto_reconnect = false;
    api_strategy.reset_session = false;
    strategies_[ErrorType::ApiError] = api_strategy;
    
    RecoveryStrategy timeout_strategy;
    timeout_strategy.max_retry_count = 2;
    timeout_strategy.retry_interval = std::chrono::seconds(3);
    timeout_strategy.auto_reconnect = false;
    timeout_strategy.reset_session = false;
    strategies_[ErrorType::TimeoutError] = timeout_strategy;
    
    // 初始化计数器
    for (auto error_type : {ErrorType::NetworkError, ErrorType::AuthError, 
                           ErrorType::ApiError, ErrorType::TimeoutError, ErrorType::UnknownError}) {
        error_counts_[error_type].store(0);
        recovery_success_counts_[error_type].store(0);
    }
}

bool CtpErrorRecovery::handle_error(ErrorType error_type, const std::string& error_msg, 
                                   std::shared_ptr<CtpBroker> broker) {
    error_counts_[error_type].fetch_add(1);
    
    spdlog::warn("CTP error occurred: {} - {}", to_string(error_type), error_msg);
    
    auto strategy = get_recovery_strategy(error_type);
    bool recovery_success = false;
    
    for (int retry = 0; retry < strategy.max_retry_count; ++retry) {
        if (retry > 0) {
            std::this_thread::sleep_for(strategy.retry_interval);
        }
        
        try {
            switch (error_type) {
                case ErrorType::NetworkError:
                    recovery_success = try_network_recovery(broker);
                    break;
                case ErrorType::AuthError:
                    recovery_success = try_auth_recovery(broker);
                    break;
                case ErrorType::ApiError:
                    recovery_success = try_api_recovery(broker);
                    break;
                case ErrorType::TimeoutError:
                    recovery_success = try_timeout_recovery(broker);
                    break;
                default:
                    recovery_success = false;
                    break;
            }
            
            if (recovery_success) {
                recovery_success_counts_[error_type].fetch_add(1);
                spdlog::info("CTP error recovery successful for: {}", to_string(error_type));
                break;
            }
            
        } catch (const std::exception& e) {
            spdlog::error("Exception during error recovery: {}", e.what());
        }
    }
    
    if (!recovery_success) {
        spdlog::error("CTP error recovery failed for: {}", to_string(error_type));
    }
    
    return recovery_success;
}

void CtpErrorRecovery::set_recovery_strategy(ErrorType error_type, const RecoveryStrategy& strategy) {
    std::unique_lock<std::shared_mutex> lock(recovery_mutex_);
    strategies_[error_type] = strategy;
}

CtpErrorRecovery::RecoveryStrategy CtpErrorRecovery::get_recovery_strategy(ErrorType error_type) const {
    std::shared_lock<std::shared_mutex> lock(recovery_mutex_);
    auto it = strategies_.find(error_type);
    return it != strategies_.end() ? it->second : RecoveryStrategy{};
}

uint64_t CtpErrorRecovery::get_error_count(ErrorType error_type) const {
    auto it = error_counts_.find(error_type);
    return it != error_counts_.end() ? it->second.load() : 0;
}

uint64_t CtpErrorRecovery::get_recovery_success_count(ErrorType error_type) const {
    auto it = recovery_success_counts_.find(error_type);
    return it != recovery_success_counts_.end() ? it->second.load() : 0;
}

double CtpErrorRecovery::get_recovery_success_rate(ErrorType error_type) const {
    uint64_t total_errors = get_error_count(error_type);
    if (total_errors == 0) return 1.0;
    uint64_t successful_recoveries = get_recovery_success_count(error_type);
    return static_cast<double>(successful_recoveries) / total_errors;
}

bool CtpErrorRecovery::try_network_recovery(std::shared_ptr<CtpBroker> broker) {
    if (!broker) return false;
    
    // 尝试重新连接
    auto disconnect_result = broker->disconnect();
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    ConnectionInfo conn_info(BrokerType::CTP, "127.0.0.1", 10130);
    auto connect_result = broker->connect(conn_info);
    
    return connect_result.has_value();
}

bool CtpErrorRecovery::try_auth_recovery(std::shared_ptr<CtpBroker> broker) {
    if (!broker) return false;
    
    // 重新认证
    auto config = broker->get_ctp_config();
    auto auth_result = broker->authenticate(config.user_id, config.password);
    
    return auth_result.has_value();
}

bool CtpErrorRecovery::try_api_recovery(std::shared_ptr<CtpBroker> broker) {
    if (!broker) return false;
    
    // API 错误通常需要等待一段时间
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    return true;
}

bool CtpErrorRecovery::try_timeout_recovery(std::shared_ptr<CtpBroker> broker) {
    if (!broker) return false;
    
    // 超时错误，检查连接状态
    auto health_result = broker->health_check();
    return health_result.has_value();
}

// CtpMonitor 实现
CtpMonitor::CtpMonitor() {
    last_alert_time_ = std::chrono::system_clock::now() - alert_cooldown_;
}

CtpMonitor::~CtpMonitor() {
    stop_monitoring();
}

void CtpMonitor::start_monitoring(std::shared_ptr<CtpBroker> broker) {
    if (monitoring_active_.load()) {
        stop_monitoring();
    }
    
    monitored_broker_ = broker;
    monitoring_active_.store(true);
    monitoring_thread_ = std::thread(&CtpMonitor::monitoring_worker, this);
    
    spdlog::info("CTP monitoring started");
}

void CtpMonitor::stop_monitoring() {
    if (!monitoring_active_.load()) return;
    
    monitoring_active_.store(false);
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    monitored_broker_.reset();
    spdlog::info("CTP monitoring stopped");
}

bool CtpMonitor::is_monitoring() const {
    return monitoring_active_.load();
}

void CtpMonitor::record_request_latency(double latency_ms) {
    stats_.total_requests.fetch_add(1);
    
    // 更新平均延迟
    auto current_avg = stats_.avg_request_latency_ms.load();
    auto total_requests = stats_.total_requests.load();
    auto new_avg = (current_avg * (total_requests - 1) + latency_ms) / total_requests;
    stats_.avg_request_latency_ms.store(new_avg);
    
    // 更新最大延迟
    auto current_max = stats_.max_request_latency_ms.load();
    if (latency_ms > current_max) {
        stats_.max_request_latency_ms.store(latency_ms);
    }
    
    stats_.last_update_time.store(std::chrono::system_clock::now());
}

void CtpMonitor::record_order_latency(double latency_ms) {
    // 类似于 record_request_latency 的实现
    auto current_avg = stats_.avg_order_latency_ms.load();
    auto orders_submitted = stats_.orders_submitted.load();
    if (orders_submitted > 0) {
        auto new_avg = (current_avg * (orders_submitted - 1) + latency_ms) / orders_submitted;
        stats_.avg_order_latency_ms.store(new_avg);
    }
    
    auto current_max = stats_.max_order_latency_ms.load();
    if (latency_ms > current_max) {
        stats_.max_order_latency_ms.store(latency_ms);
    }
}

void CtpMonitor::record_error(const std::string& error_type) {
    if (error_type == "network") {
        stats_.network_errors.fetch_add(1);
    } else if (error_type == "api") {
        stats_.api_errors.fetch_add(1);
    } else if (error_type == "timeout") {
        stats_.timeout_errors.fetch_add(1);
    } else if (error_type == "auth") {
        stats_.auth_errors.fetch_add(1);
    }
    
    consecutive_errors_.fetch_add(1);
    stats_.last_update_time.store(std::chrono::system_clock::now());
}

bool CtpMonitor::is_broker_healthy() const {
    if (!monitored_broker_) return false;
    
    // 检查连接状态
    if (!monitored_broker_->is_connected()) return false;
    
    // 检查认证状态
    if (!monitored_broker_->is_authenticated()) return false;
    
    // 检查性能指标
    double avg_latency = stats_.avg_request_latency_ms.load();
    if (avg_latency > max_latency_threshold_ms_) return false;
    
    double success_rate = stats_.get_success_rate();
    if (success_rate < min_success_rate_threshold_) return false;
    
    int consecutive_errors = consecutive_errors_.load();
    if (consecutive_errors > max_consecutive_errors_) return false;
    
    return true;
}

std::string CtpMonitor::get_health_status() const {
    if (!monitored_broker_) return "No broker monitored";
    
    if (is_broker_healthy()) {
        return "Healthy";
    } else {
        std::ostringstream oss;
        oss << "Unhealthy - ";
        
        if (!monitored_broker_->is_connected()) {
            oss << "Not connected; ";
        }
        if (!monitored_broker_->is_authenticated()) {
            oss << "Not authenticated; ";
        }
        
        double avg_latency = stats_.avg_request_latency_ms.load();
        if (avg_latency > max_latency_threshold_ms_) {
            oss << "High latency (" << avg_latency << "ms); ";
        }
        
        double success_rate = stats_.get_success_rate();
        if (success_rate < min_success_rate_threshold_) {
            oss << "Low success rate (" << (success_rate * 100) << "%); ";
        }
        
        int consecutive_errors = consecutive_errors_.load();
        if (consecutive_errors > max_consecutive_errors_) {
            oss << "Too many consecutive errors (" << consecutive_errors << "); ";
        }
        
        return oss.str();
    }
}

void CtpMonitor::set_alert_callback(AlertCallback callback) {
    alert_callback_ = std::move(callback);
}

CtpPerformanceStats CtpMonitor::get_performance_stats() const {
    return stats_;
}

nlohmann::json CtpMonitor::generate_monitoring_report() const {
    nlohmann::json report;
    report["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    report["health_status"] = get_health_status();
    report["is_healthy"] = is_broker_healthy();
    report["performance_stats"] = stats_.to_json();
    
    if (monitored_broker_) {
        report["broker_status"] = {
            {"connected", monitored_broker_->is_connected()},
            {"authenticated", monitored_broker_->is_authenticated()},
            {"trader_connected", monitored_broker_->is_trader_connected()},
            {"md_connected", monitored_broker_->is_md_connected()}
        };
    }
    
    return report;
}

void CtpMonitor::monitoring_worker() {
    while (monitoring_active_.load()) {
        try {
            check_performance_thresholds();
            
            // 重置连续错误计数器（如果没有新错误）
            auto last_update = stats_.last_update_time.load();
            auto now = std::chrono::system_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_update).count() > 60) {
                consecutive_errors_.store(0);
            }
            
        } catch (const std::exception& e) {
            spdlog::error("Exception in monitoring worker: {}", e.what());
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
}

void CtpMonitor::check_performance_thresholds() {
    if (!is_broker_healthy()) {
        send_alert("health", get_health_status());
    }
    
    double avg_latency = stats_.avg_request_latency_ms.load();
    if (avg_latency > max_latency_threshold_ms_) {
        send_alert("latency", "High average latency: " + std::to_string(avg_latency) + "ms");
    }
    
    double success_rate = stats_.get_success_rate();
    if (success_rate < min_success_rate_threshold_) {
        send_alert("success_rate", "Low success rate: " + std::to_string(success_rate * 100) + "%");
    }
}

void CtpMonitor::send_alert(const std::string& alert_type, const std::string& message) {
    auto now = std::chrono::system_clock::now();
    if (now - last_alert_time_ < alert_cooldown_) {
        return; // 冷却期内，不发送告警
    }
    
    if (alert_callback_) {
        try {
            alert_callback_(alert_type, message);
            last_alert_time_ = now;
        } catch (const std::exception& e) {
            spdlog::error("Exception in alert callback: {}", e.what());
        }
    }
}

// 工具函数
std::string to_string(CtpErrorRecovery::ErrorType error_type) {
    switch (error_type) {
        case CtpErrorRecovery::ErrorType::NetworkError: return "NetworkError";
        case CtpErrorRecovery::ErrorType::AuthError: return "AuthError";
        case CtpErrorRecovery::ErrorType::ApiError: return "ApiError";
        case CtpErrorRecovery::ErrorType::TimeoutError: return "TimeoutError";
        case CtpErrorRecovery::ErrorType::UnknownError: return "UnknownError";
        default: return "Unknown";
    }
}

CtpErrorRecovery::ErrorType parse_error_type(const std::string& str) {
    if (str == "NetworkError") return CtpErrorRecovery::ErrorType::NetworkError;
    if (str == "AuthError") return CtpErrorRecovery::ErrorType::AuthError;
    if (str == "ApiError") return CtpErrorRecovery::ErrorType::ApiError;
    if (str == "TimeoutError") return CtpErrorRecovery::ErrorType::TimeoutError;
    return CtpErrorRecovery::ErrorType::UnknownError;
}

} // namespace RoboQuant::Broker
