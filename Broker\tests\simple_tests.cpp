#include <iostream>
#include <string>
#include <vector>
#include <functional>
#include <chrono>
#include <exception>

// Simple test framework
class SimpleTest {
public:
    struct TestResult {
        std::string name;
        bool passed;
        std::string error_message;
        std::chrono::milliseconds duration;
    };
    
    static void add_test(const std::string& name, std::function<void()> test_func) {
        tests_.emplace_back(name, std::move(test_func));
    }
    
    static int run_all_tests() {
        std::cout << "Running " << tests_.size() << " tests...\n\n";
        
        std::vector<TestResult> results;
        int passed = 0;
        int failed = 0;
        
        for (const auto& [name, test_func] : tests_) {
            std::cout << "Running: " << name << " ... ";
            
            auto start = std::chrono::high_resolution_clock::now();
            TestResult result;
            result.name = name;
            
            try {
                test_func();
                result.passed = true;
                std::cout << "PASSED";
                ++passed;
            } catch (const std::exception& e) {
                result.passed = false;
                result.error_message = e.what();
                std::cout << "FAILED: " << e.what();
                ++failed;
            } catch (...) {
                result.passed = false;
                result.error_message = "Unknown exception";
                std::cout << "FAILED: Unknown exception";
                ++failed;
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
            std::cout << " (" << result.duration.count() << "ms)\n";
            
            results.push_back(result);
        }
        
        std::cout << "\n=== Test Summary ===\n";
        std::cout << "Total: " << tests_.size() << "\n";
        std::cout << "Passed: " << passed << "\n";
        std::cout << "Failed: " << failed << "\n";
        
        if (failed > 0) {
            std::cout << "\nFailed tests:\n";
            for (const auto& result : results) {
                if (!result.passed) {
                    std::cout << "  " << result.name << ": " << result.error_message << "\n";
                }
            }
        }
        
        return failed == 0 ? 0 : 1;
    }

private:
    static std::vector<std::pair<std::string, std::function<void()>>> tests_;
};

std::vector<std::pair<std::string, std::function<void()>>> SimpleTest::tests_;

// Test macros
#define TEST(test_name) \
    void test_##test_name(); \
    static bool test_##test_name##_registered = []() { \
        SimpleTest::add_test(#test_name, test_##test_name); \
        return true; \
    }(); \
    void test_##test_name()

#define ASSERT_TRUE(condition) \
    do { \
        if (!(condition)) { \
            throw std::runtime_error("Assertion failed: " #condition); \
        } \
    } while(0)

#define ASSERT_FALSE(condition) \
    do { \
        if (condition) { \
            throw std::runtime_error("Assertion failed: !(" #condition ")"); \
        } \
    } while(0)

#define ASSERT_EQ(expected, actual) \
    do { \
        if ((expected) != (actual)) { \
            throw std::runtime_error("Assertion failed: " #expected " == " #actual); \
        } \
    } while(0)

#define ASSERT_NE(expected, actual) \
    do { \
        if ((expected) == (actual)) { \
            throw std::runtime_error("Assertion failed: " #expected " != " #actual); \
        } \
    } while(0)

#define ASSERT_THROW(statement, exception_type) \
    do { \
        bool caught = false; \
        try { \
            statement; \
        } catch (const exception_type&) { \
            caught = true; \
        } \
        if (!caught) { \
            throw std::runtime_error("Expected exception " #exception_type " not thrown"); \
        } \
    } while(0)

#define ASSERT_NO_THROW(statement) \
    do { \
        try { \
            statement; \
        } catch (...) { \
            throw std::runtime_error("Unexpected exception thrown"); \
        } \
    } while(0)

// Main function
int main() {
    try {
        return SimpleTest::run_all_tests();
    } catch (const std::exception& e) {
        std::cerr << "Test framework error: " << e.what() << std::endl;
        return 1;
    }
}
