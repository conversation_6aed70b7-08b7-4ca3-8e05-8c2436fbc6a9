#include "utils/ThreadSafety.h"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace RoboQuant::Broker::Utils {

// ThreadPool implementation
ThreadPool::ThreadPool(size_t num_threads) {
    if (num_threads == 0) {
        num_threads = std::thread::hardware_concurrency();
        if (num_threads == 0) {
            num_threads = 4; // Fallback
        }
    }
    
    workers_.reserve(num_threads);
    
    for (size_t i = 0; i < num_threads; ++i) {
        workers_.emplace_back(&ThreadPool::worker_thread, this);
    }
    
    spdlog::info("ThreadPool created with {} threads", num_threads);
}

ThreadPool::~ThreadPool() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        stop_ = true;
    }
    
    condition_.notify_all();
    
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    spdlog::info("ThreadPool destroyed");
}

size_t ThreadPool::pending_tasks() const {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    return tasks_.size();
}

void ThreadPool::wait_for_all() {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    
    // Wait until all tasks are completed and no threads are active
    while (!tasks_.empty() || active_threads_.load() > 0) {
        lock.unlock();
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        lock.lock();
    }
}

void ThreadPool::worker_thread() {
    while (true) {
        std::function<void()> task;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            condition_.wait(lock, [this] {
                return stop_ || !tasks_.empty();
            });
            
            if (stop_ && tasks_.empty()) {
                break;
            }
            
            task = std::move(tasks_.front());
            tasks_.pop();
        }
        
        // Execute task
        active_threads_.fetch_add(1);
        try {
            task();
        } catch (const std::exception& e) {
            spdlog::error("Exception in thread pool task: {}", e.what());
        } catch (...) {
            spdlog::error("Unknown exception in thread pool task");
        }
        active_threads_.fetch_sub(1);
    }
}

} // namespace RoboQuant::Broker::Utils
