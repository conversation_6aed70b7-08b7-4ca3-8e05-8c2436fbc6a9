#include "FactorEngine_Enhanced.h" // 引入增强版头文件
#include <iostream>
#include <chrono>
#include <random>

int main() {
    // 1. 初始化增强版因子引擎，使用硬件并发线程数
    QuantEngine::FactorEngineEnhanced engine(std::thread::hardware_concurrency());

    // 2. 添加证券及其因子配置
    engine.addSecurity("AAPL", {{"price", 20}, {"volume", 30}});
    engine.addSecurity("GOOG", {{"price", 20}, {"volume", 50}});

    std::cout << "Engine initialized. Simulating market data stream..." << std::endl;

    // 3. 模拟实时市场数据流
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<> price_dist_aapl(150.0, 2.0);
    std::normal_distribution<> vol_dist_aapl(20000000.0, 500000.0);
    std::normal_distribution<> price_dist_goog(2800.0, 20.0);
    std::normal_distribution<> vol_dist_goog(1500000.0, 200000.0);
  
    for (int i = 0; i < 100; ++i) {
        // 模拟AAPL数据
        double aapl_price = price_dist_aapl(gen);
        double aapl_vol = vol_dist_aapl(gen);
        engine.processTick({"AAPL", "price", aapl_price});
        engine.processTick({"AAPL", "volume", aapl_vol});

        // 模拟GOOG数据
        double goog_price = price_dist_goog(gen);
        double goog_vol = vol_dist_goog(gen);
        engine.processTick({"GOOG", "price", goog_price});
        engine.processTick({"GOOG", "volume", goog_vol});

        // 每隔10个tick，我们查询一次当前状态
        if ((i + 1) % 10 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 等待任务执行
          
            std::cout << "\n--- Status after " << i + 1 << " ticks ---" << std::endl;
          
            double aapl_price_std = engine.queryStandardizedValue("AAPL", "price", aapl_price);
            double aapl_vol_std = engine.queryStandardizedValue("AAPL", "volume", aapl_vol);
            std::cout << "AAPL Price: " << aapl_price << " -> Z-Score: " << aapl_price_std << std::endl;
            std::cout << "AAPL Volume: " << aapl_vol << " -> Z-Score: " << aapl_vol_std << std::endl;

            double goog_price_std = engine.queryStandardizedValue("GOOG", "price", goog_price);
            double goog_vol_std = engine.queryStandardizedValue("GOOG", "volume", goog_vol);
            std::cout << "GOOG Price: " << goog_price << " -> Z-Score: " << goog_price_std << std::endl;
            std::cout << "GOOG Volume: " << goog_vol << " -> Z-Score: " << goog_vol_std << std::endl;
        }
    }
  
    std::cout << "\nSimulation finished." << std::endl;

    return 0;
}