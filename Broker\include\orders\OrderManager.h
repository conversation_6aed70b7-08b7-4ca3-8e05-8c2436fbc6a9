#pragma once

#include "orders/Order.h"
#include "core/EventSystem.h"
#include <unordered_map>
#include <unordered_set>
#include <shared_mutex>
#include <functional>

namespace RoboQuant::Broker {

// Forward declarations
class BrokerInterface;

// Order filter and query types
struct OrderFilter {
    std::optional<AccountId> account_id;
    std::optional<AssetId> asset_id;
    std::optional<OrderSide> side;
    std::optional<OrderType> type;
    std::optional<OrderStatus> status;
    std::optional<std::string> strategy_id;
    std::optional<TimePoint> from_time;
    std::optional<TimePoint> to_time;
    
    bool matches(const Order& order) const;
};

using OrderCallback = std::function<void(const Order&)>;
using OrderValidator = std::function<Result<void>(const OrderRequest&)>;

// Order manager class
class OrderManager {
public:
    OrderManager();
    ~OrderManager();
    
    // Non-copyable, non-movable
    OrderManager(const OrderManager&) = delete;
    OrderManager& operator=(const OrderManager&) = delete;
    OrderManager(OrderManager&&) = delete;
    OrderManager& operator=(OrderManager&&) = delete;
    
    // Order lifecycle management
    Result<OrderId> submit_order(const OrderRequest& request);
    Result<void> cancel_order(const OrderId& order_id);
    Result<void> modify_order(const OrderId& order_id, const OrderRequest& new_request);
    
    // Order queries
    std::optional<Order> get_order(const OrderId& order_id) const;
    std::vector<Order> get_orders(const OrderFilter& filter = {}) const;
    std::vector<Order> get_active_orders() const;
    std::vector<Order> get_orders_by_account(const AccountId& account_id) const;
    std::vector<Order> get_orders_by_asset(const AssetId& asset_id) const;
    std::vector<Order> get_orders_by_strategy(const std::string& strategy_id) const;
    
    // Order status management
    void update_order_status(const OrderId& order_id, OrderStatus status);
    void update_order_fill(const OrderId& order_id, Quantity quantity, Price price);
    void update_order_error(const OrderId& order_id, const std::string& error);
    void set_broker_order_id(const OrderId& order_id, const std::string& broker_order_id);
    
    // Broker integration
    void set_broker_interface(std::shared_ptr<BrokerInterface> broker);
    std::shared_ptr<BrokerInterface> get_broker_interface() const;
    
    // Validation
    void add_validator(OrderValidator validator);
    void clear_validators();
    
    // Callbacks
    void set_order_callback(OrderCallback callback);
    void clear_order_callback();
    
    // Statistics
    size_t total_orders() const;
    size_t active_orders_count() const;
    size_t filled_orders_count() const;
    size_t cancelled_orders_count() const;
    
    // Persistence
    Result<void> save_orders_to_file(const std::string& filename) const;
    Result<void> load_orders_from_file(const std::string& filename);
    
    // Event handling
    void start_event_handling();
    void stop_event_handling();

private:
    void handle_order_event(const EventPtr& event);
    void handle_trade_event(const EventPtr& event);
    void notify_order_update(const Order& order);
    Result<void> validate_order_request(const OrderRequest& request) const;
    void add_order_internal(const Order& order);
    void update_order_internal(const OrderId& order_id, std::function<void(Order&)> updater);
    
    // Thread-safe storage
    mutable std::shared_mutex orders_mutex_;
    std::unordered_map<OrderId, Order, OrderIdHash, OrderIdEqual> orders_;
    
    // Indices for fast lookup
    mutable std::shared_mutex indices_mutex_;
    std::unordered_map<AccountId, std::unordered_set<OrderId>> orders_by_account_;
    std::unordered_map<AssetId, std::unordered_set<OrderId>> orders_by_asset_;
    std::unordered_map<std::string, std::unordered_set<OrderId>> orders_by_strategy_;
    std::unordered_map<OrderStatus, std::unordered_set<OrderId>> orders_by_status_;
    
    // Broker interface
    std::shared_ptr<BrokerInterface> broker_interface_;
    
    // Validation and callbacks
    std::vector<OrderValidator> validators_;
    OrderCallback order_callback_;
    
    // Event handling
    std::atomic<bool> event_handling_active_{false};
    std::vector<EventHandlerPtr> event_handlers_;
};

// Order statistics
struct OrderStatistics {
    size_t total_orders{0};
    size_t active_orders{0};
    size_t filled_orders{0};
    size_t cancelled_orders{0};
    size_t rejected_orders{0};
    Amount total_traded_value{0.0};
    Quantity total_traded_quantity{0};
    
    nlohmann::json to_json() const;
    static OrderStatistics from_json(const nlohmann::json& j);
};

// Order book snapshot
struct OrderBookSnapshot {
    TimePoint timestamp;
    std::vector<Order> orders;
    OrderStatistics statistics;
    
    nlohmann::json to_json() const;
    static OrderBookSnapshot from_json(const nlohmann::json& j);
};

// Utility functions
OrderStatistics calculate_order_statistics(const std::vector<Order>& orders);
OrderBookSnapshot create_order_book_snapshot(const OrderManager& manager);

// Order management policies
class OrderPolicy {
public:
    virtual ~OrderPolicy() = default;
    virtual Result<void> validate_order(const OrderRequest& request) const = 0;
    virtual bool can_submit_order(const OrderRequest& request) const = 0;
    virtual bool can_cancel_order(const Order& order) const = 0;
    virtual bool can_modify_order(const Order& order) const = 0;
};

// Risk management policies
class RiskPolicy : public OrderPolicy {
public:
    RiskPolicy(Amount max_order_value = 1000000.0, Quantity max_order_quantity = 100000)
        : max_order_value_(max_order_value), max_order_quantity_(max_order_quantity) {}
    
    Result<void> validate_order(const OrderRequest& request) const override;
    bool can_submit_order(const OrderRequest& request) const override;
    bool can_cancel_order(const Order& order) const override;
    bool can_modify_order(const Order& order) const override;
    
    void set_max_order_value(Amount value) { max_order_value_ = value; }
    void set_max_order_quantity(Quantity quantity) { max_order_quantity_ = quantity; }

private:
    Amount max_order_value_;
    Quantity max_order_quantity_;
};

// Time-based policies
class TradingHoursPolicy : public OrderPolicy {
public:
    TradingHoursPolicy() = default;
    
    Result<void> validate_order(const OrderRequest& request) const override;
    bool can_submit_order(const OrderRequest& request) const override;
    bool can_cancel_order(const Order& order) const override;
    bool can_modify_order(const Order& order) const override;
    
    void add_trading_session(int start_hour, int start_minute, int end_hour, int end_minute);
    void clear_trading_sessions();

private:
    struct TradingSession {
        int start_hour, start_minute;
        int end_hour, end_minute;
    };
    std::vector<TradingSession> trading_sessions_;
    
    bool is_trading_time() const;
};

} // namespace RoboQuant::Broker
