#pragma once

#include "core/Types.h"
#include <functional>
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <thread>
#include <queue>
#include <future>
#include <any>

// Note: Coroutine support can be added later when needed
// #include <coroutine>

namespace RoboQuant::Broker {

// Forward declarations
class Event;
class EventHandler;
class EventDispatcher;

// Event base class
class Event {
public:
    Event(EventType type, TimePoint timestamp = std::chrono::system_clock::now())
        : type_(type), timestamp_(timestamp), id_(generate_id()) {}
    
    virtual ~Event() = default;
    
    EventType type() const noexcept { return type_; }
    TimePoint timestamp() const noexcept { return timestamp_; }
    uint64_t id() const noexcept { return id_; }
    
    template<typename T>
    T* as() {
        return dynamic_cast<T*>(this);
    }
    
    template<typename T>
    const T* as() const {
        return dynamic_cast<const T*>(this);
    }

private:
    EventType type_;
    TimePoint timestamp_;
    uint64_t id_;
    
    static uint64_t generate_id() {
        static std::atomic<uint64_t> counter{0};
        return ++counter;
    }
};

using EventPtr = std::shared_ptr<Event>;

// Specific event types
class OrderEvent : public Event {
public:
    OrderEvent(OrderId order_id, OrderStatus status, std::string message = "")
        : Event(EventType::OrderUpdate), order_id_(std::move(order_id)), 
          status_(status), message_(std::move(message)) {}
    
    const OrderId& order_id() const noexcept { return order_id_; }
    OrderStatus status() const noexcept { return status_; }
    const std::string& message() const noexcept { return message_; }

private:
    OrderId order_id_;
    OrderStatus status_;
    std::string message_;
};

class TradeEvent : public Event {
public:
    TradeEvent(TradeId trade_id, OrderId order_id, AssetId asset_id, 
               OrderSide side, Quantity quantity, Price price)
        : Event(EventType::TradeUpdate), trade_id_(std::move(trade_id)),
          order_id_(std::move(order_id)), asset_id_(std::move(asset_id)),
          side_(side), quantity_(quantity), price_(price) {}
    
    const TradeId& trade_id() const noexcept { return trade_id_; }
    const OrderId& order_id() const noexcept { return order_id_; }
    const AssetId& asset_id() const noexcept { return asset_id_; }
    OrderSide side() const noexcept { return side_; }
    Quantity quantity() const noexcept { return quantity_; }
    Price price() const noexcept { return price_; }

private:
    TradeId trade_id_;
    OrderId order_id_;
    AssetId asset_id_;
    OrderSide side_;
    Quantity quantity_;
    Price price_;
};

class AccountEvent : public Event {
public:
    AccountEvent(AccountId account_id, Amount balance, Amount available)
        : Event(EventType::AccountUpdate), account_id_(std::move(account_id)),
          balance_(balance), available_(available) {}
    
    const AccountId& account_id() const noexcept { return account_id_; }
    Amount balance() const noexcept { return balance_; }
    Amount available() const noexcept { return available_; }

private:
    AccountId account_id_;
    Amount balance_;
    Amount available_;
};

class ConnectionEvent : public Event {
public:
    ConnectionEvent(BrokerType broker_type, ConnectionStatus status, std::string message = "")
        : Event(EventType::ConnectionUpdate), broker_type_(broker_type),
          status_(status), message_(std::move(message)) {}
    
    BrokerType broker_type() const noexcept { return broker_type_; }
    ConnectionStatus status() const noexcept { return status_; }
    const std::string& message() const noexcept { return message_; }

private:
    BrokerType broker_type_;
    ConnectionStatus status_;
    std::string message_;
};

class ErrorEvent : public Event {
public:
    ErrorEvent(ErrorCode error_code, std::string message, std::any context = {})
        : Event(EventType::ErrorEvent), error_code_(error_code),
          message_(std::move(message)), context_(std::move(context)) {}
    
    ErrorCode error_code() const noexcept { return error_code_; }
    const std::string& message() const noexcept { return message_; }
    const std::any& context() const noexcept { return context_; }

private:
    ErrorCode error_code_;
    std::string message_;
    std::any context_;
};

// Event handler interface
class EventHandler {
public:
    virtual ~EventHandler() = default;
    virtual void handle_event(const EventPtr& event) = 0;
    virtual bool can_handle(EventType type) const = 0;
};

using EventHandlerPtr = std::shared_ptr<EventHandler>;

// Functional event handler
template<typename Func>
class FunctionalEventHandler : public EventHandler {
public:
    FunctionalEventHandler(EventType type, Func&& func)
        : type_(type), func_(std::forward<Func>(func)) {}
    
    void handle_event(const EventPtr& event) override {
        if (event->type() == type_) {
            func_(event);
        }
    }
    
    bool can_handle(EventType type) const override {
        return type == type_;
    }

private:
    EventType type_;
    Func func_;
};

// Future coroutine support (placeholder for now)
// struct EventAwaiter {
//     EventType event_type;
//     EventDispatcher* dispatcher;
//     EventPtr result;
//
//     EventAwaiter(EventType type, EventDispatcher* disp)
//         : event_type(type), dispatcher(disp) {}
//
//     bool await_ready() const noexcept { return false; }
//     void await_suspend(std::coroutine_handle<> h);
//     EventPtr await_resume() const noexcept { return result; }
// };

// Event dispatcher
class EventDispatcher {
public:
    EventDispatcher();
    ~EventDispatcher();
    
    // Non-copyable, non-movable
    EventDispatcher(const EventDispatcher&) = delete;
    EventDispatcher& operator=(const EventDispatcher&) = delete;
    EventDispatcher(EventDispatcher&&) = delete;
    EventDispatcher& operator=(EventDispatcher&&) = delete;
    
    // Start/stop the dispatcher
    void start();
    void stop();
    bool is_running() const noexcept { return running_.load(); }
    
    // Event publishing
    void publish(EventPtr event);
    void publish_sync(EventPtr event);
    
    // Handler registration
    void subscribe(EventType type, EventHandlerPtr handler);
    void unsubscribe(EventType type, EventHandlerPtr handler);
    
    template<typename Func>
    void subscribe(EventType type, Func&& func) {
        auto handler = std::make_shared<FunctionalEventHandler<Func>>(
            type, std::forward<Func>(func));
        subscribe(type, handler);
    }
    
    // Future coroutine support
    // EventAwaiter wait_for_event(EventType type) {
    //     return EventAwaiter{type, this};
    // }

    // Alternative: Promise-based async event waiting
    std::future<EventPtr> wait_for_event_async(EventType type);
    
    // Statistics
    size_t pending_events() const;
    size_t total_handlers() const;

private:
    void worker_thread();
    void dispatch_event(const EventPtr& event);
    void notify_promise_waiters(const EventPtr& event);

    std::atomic<bool> running_{false};
    std::thread worker_;

    mutable std::mutex queue_mutex_;
    std::queue<EventPtr> event_queue_;
    std::condition_variable queue_cv_;

    mutable std::mutex handlers_mutex_;
    std::unordered_map<EventType, std::vector<EventHandlerPtr>> handlers_;

    mutable std::mutex promises_mutex_;
    std::unordered_map<EventType, std::vector<std::promise<EventPtr>>> event_promises_;
};

// Global event dispatcher instance
EventDispatcher& get_event_dispatcher();

// Convenience functions
void publish_event(EventPtr event);
void subscribe_to_events(EventType type, EventHandlerPtr handler);

template<typename Func>
void subscribe_to_events(EventType type, Func&& func) {
    get_event_dispatcher().subscribe(type, std::forward<Func>(func));
}

// Event creation helpers
EventPtr make_order_event(OrderId order_id, OrderStatus status, std::string message = "");
EventPtr make_trade_event(TradeId trade_id, OrderId order_id, AssetId asset_id,
                         OrderSide side, Quantity quantity, Price price);
EventPtr make_account_event(AccountId account_id, Amount balance, Amount available);
EventPtr make_connection_event(BrokerType broker_type, ConnectionStatus status, std::string message = "");
EventPtr make_error_event(ErrorCode error_code, std::string message, std::any context = {});

} // namespace RoboQuant::Broker
