{"system": {"name": "RoboQuant", "version": "1.0.0", "log_level": "info", "log_file": "logs/roboquant.log"}, "database": {"type": "sqlite", "path": "data/roboquant.db", "connection_pool_size": 10}, "market_data": {"providers": ["tushare", "akshare"], "cache_enabled": true, "cache_size": 1000, "update_interval": 1000}, "trading": {"default_broker": "ctp", "risk_management": {"max_position_size": 1000000, "max_daily_loss": 50000, "enabled": true}}, "ai": {"torch_device": "cpu", "model_path": "models/", "enabled": false}, "technical_analysis": {"talib_enabled": true, "indicators": ["SMA", "EMA", "RSI", "MACD"]}, "network": {"api_port": 8080, "websocket_port": 8081, "timeout": 30000}}