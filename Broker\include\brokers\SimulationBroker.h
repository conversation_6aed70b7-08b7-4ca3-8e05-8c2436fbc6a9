#pragma once

#include "brokers/BrokerInterface.h"
#include "accounts/Account.h"
#include <random>
#include <thread>
#include <atomic>

namespace RoboQuant::Broker {

// Simulation parameters
struct SimulationConfig {
    Duration order_fill_delay{std::chrono::milliseconds(100)};
    double fill_probability{0.95};  // Probability of order being filled
    double slippage_factor{0.001};  // Price slippage as fraction
    bool simulate_partial_fills{true};
    double partial_fill_probability{0.3};
    Amount initial_balance{1000000.0};  // Initial account balance
    
    nlohmann::json to_json() const;
    static SimulationConfig from_json(const nlohmann::json& j);
};

// Simulation broker implementation
class SimulationBroker : public BaseBroker {
public:
    SimulationBroker();
    explicit SimulationBroker(const SimulationConfig& sim_config);
    ~SimulationBroker() override;
    
    // Broker information
    BrokerCapabilities get_capabilities() const override;
    
    // Connection management
    Result<void> connect(const ConnectionInfo& info) override;
    Result<void> disconnect() override;
    Result<void> authenticate(const std::string& username, const std::string& password) override;
    
    // Order management
    Result<void> submit_order(const Order& order) override;
    Result<void> cancel_order(const Order& order) override;
    Result<void> modify_order(const Order& order, const OrderRequest& new_request) override;
    Result<std::vector<Order>> query_orders(const AccountId& account_id = "") override;
    Result<Order> query_order(const OrderId& order_id) override;
    
    // Account management
    Result<std::vector<Account>> query_accounts() override;
    Result<Account> query_account(const AccountId& account_id) override;
    Result<std::vector<Position>> query_positions(const AccountId& account_id = "") override;
    Result<std::vector<Trade>> query_trades(const AccountId& account_id = "", 
                                           const TimePoint& from = {},
                                           const TimePoint& to = {}) override;
    
    // Health check
    Result<void> health_check() override;
    
    // Simulation-specific methods
    void set_simulation_config(const SimulationConfig& config);
    SimulationConfig get_simulation_config() const;
    void set_market_price(const AssetId& asset_id, Price price);
    Price get_market_price(const AssetId& asset_id) const;
    void simulate_market_movement();
    void start_simulation();
    void stop_simulation();
    bool is_simulation_running() const;

private:
    void simulation_worker();
    void process_pending_orders();
    void simulate_order_fill(const Order& order);
    Price calculate_fill_price(const Order& order) const;
    bool should_fill_order(const Order& order) const;
    bool should_partial_fill(const Order& order) const;
    Quantity calculate_fill_quantity(const Order& order) const;
    void create_default_account();
    void update_account_balance(const AccountId& account_id, Amount change);
    
    SimulationConfig sim_config_;
    
    // Market data simulation
    mutable std::shared_mutex market_data_mutex_;
    std::unordered_map<AssetId, Price> market_prices_;
    
    // Order tracking
    mutable std::shared_mutex orders_mutex_;
    std::unordered_map<OrderId, Order> orders_;
    std::queue<OrderId> pending_orders_;
    
    // Account simulation
    mutable std::shared_mutex accounts_mutex_;
    std::unordered_map<AccountId, Account> accounts_;
    
    // Position tracking
    mutable std::shared_mutex positions_mutex_;
    std::unordered_map<std::string, Position> positions_;  // key: account_id + asset_id
    
    // Trade history
    mutable std::shared_mutex trades_mutex_;
    std::vector<Trade> trades_;
    
    // Simulation thread
    std::atomic<bool> simulation_running_{false};
    std::thread simulation_thread_;
    
    // Random number generation
    mutable std::mt19937 rng_;
    mutable std::uniform_real_distribution<double> uniform_dist_{0.0, 1.0};
    mutable std::normal_distribution<double> normal_dist_{0.0, 1.0};
};

// Simulation broker factory
class SimulationBrokerFactory : public BrokerFactory {
public:
    BrokerType get_broker_type() const override { return BrokerType::Simulation; }
    std::string get_broker_name() const override { return "Simulation Broker"; }
    BrokerInterfacePtr create_broker() override;
    BrokerInterfacePtr create_broker(const BrokerConfig& config) override;
    bool supports_config(const BrokerConfig& config) const override;
};

// Market data simulator
class MarketDataSimulator {
public:
    MarketDataSimulator();
    ~MarketDataSimulator();
    
    void add_asset(const AssetId& asset_id, Price initial_price);
    void remove_asset(const AssetId& asset_id);
    void set_price(const AssetId& asset_id, Price price);
    Price get_price(const AssetId& asset_id) const;
    
    void set_volatility(const AssetId& asset_id, double volatility);
    void set_drift(const AssetId& asset_id, double drift);
    
    void start();
    void stop();
    bool is_running() const;
    
    void set_update_interval(Duration interval);
    void set_price_update_callback(std::function<void(const AssetId&, Price)> callback);

private:
    void simulation_worker();
    void update_prices();
    Price simulate_price_movement(const AssetId& asset_id, Price current_price) const;
    
    struct AssetData {
        Price price{100.0};
        double volatility{0.02};  // Daily volatility
        double drift{0.0};        // Daily drift
    };
    
    mutable std::shared_mutex assets_mutex_;
    std::unordered_map<AssetId, AssetData> assets_;
    
    std::atomic<bool> running_{false};
    std::thread worker_thread_;
    Duration update_interval_{std::chrono::milliseconds(1000)};
    
    std::function<void(const AssetId&, Price)> price_callback_;
    
    mutable std::mt19937 rng_;
    mutable std::normal_distribution<double> normal_dist_{0.0, 1.0};
};

// Utility functions for simulation
void register_simulation_broker();
SimulationConfig create_default_simulation_config();
std::vector<AssetInfo> create_sample_assets();
void setup_sample_market_data(MarketDataSimulator& simulator);

} // namespace RoboQuant::Broker
