/**
 * @file Events.cpp
 * @brief Implementation of event system classes
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Events.h"
#include <algorithm>
#include <iostream>
#include <thread>
#include <chrono>

namespace RoboQuant::Trading {

// EventBus Implementation
EventBus::EventBus() = default;

EventBus::~EventBus() {
    stop();
}

void EventBus::subscribe(const std::string& event_type, UniquePtr<EventHandler> handler) {
    subscribe_impl(event_type, std::move(handler));
}

void EventBus::unsubscribe(const std::string& event_type) {
    std::unique_lock lock(handlers_mutex_);
    handlers_.erase(event_type);
}

void EventBus::publish(UniquePtr<Event> event) {
    if (!running_.load()) {
        return;
    }

    // ConcurrentQueue doesn't need external locking
    event_queue_.enqueue(std::move(event));
    queue_condition_.notify_one();
}

void EventBus::start() {
    if (running_.load()) {
        return;
    }
    
    running_.store(true);
    worker_thread_ = std::thread(&EventBus::process_events, this);
}

void EventBus::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    queue_condition_.notify_all();
    
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
}

bool EventBus::is_running() const noexcept {
    return running_.load();
}

void EventBus::subscribe_impl(const std::string& event_type, UniquePtr<EventHandler> handler) {
    std::unique_lock lock(handlers_mutex_);
    handlers_[event_type].push_back(std::move(handler));
}

void EventBus::process_events() {
    while (running_.load()) {
        UniquePtr<Event> event;

        // Try to dequeue an event
        if (event_queue_.try_dequeue(event)) {
            // Process event immediately if available
            
            try {
                std::shared_lock handlers_lock(handlers_mutex_);
                auto it = handlers_.find(event->type());

                if (it != handlers_.end()) {
                    for (const auto& handler : it->second) {
                        try {
                            handler->handle_event(*event);
                        } catch (const std::exception& e) {
                            std::cerr << "Error in event handler for " << event->type()
                                     << ": " << e.what() << std::endl;
                        }
                    }
                }
            } catch (const std::exception& e) {
                std::cerr << "Error processing event " << event->type()
                         << ": " << e.what() << std::endl;
            }
        } else {
            // No event available, sleep briefly
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}

// EventDispatcher Implementation
void EventDispatcher::subscribe(const std::string& event_type, UniquePtr<EventHandler> handler) {
    subscribe_impl(event_type, std::move(handler));
}

void EventDispatcher::unsubscribe(const std::string& event_type) {
    std::unique_lock lock(handlers_mutex_);
    handlers_.erase(event_type);
}

void EventDispatcher::dispatch(const Event& event) {
    std::shared_lock lock(handlers_mutex_);
    auto it = handlers_.find(event.type());
    
    if (it != handlers_.end()) {
        for (const auto& handler : it->second) {
            try {
                handler->handle_event(event);
            } catch (const std::exception& e) {
                std::cerr << "Error in event handler for " << event.type() 
                         << ": " << e.what() << std::endl;
            }
        }
    }
}

void EventDispatcher::subscribe_impl(const std::string& event_type, UniquePtr<EventHandler> handler) {
    std::unique_lock lock(handlers_mutex_);
    handlers_[event_type].push_back(std::move(handler));
}

// EventAggregator Implementation
EventAggregator::EventAggregator(Duration batch_interval) 
    : batch_interval_(batch_interval) {}

EventAggregator::~EventAggregator() {
    stop();
}

void EventAggregator::add_event(UniquePtr<Event> event) {
    if (!running_.load()) {
        return;
    }
    
    std::lock_guard lock(batch_mutex_);
    current_batch_.push_back(std::move(event));
    batch_condition_.notify_one();
}

void EventAggregator::set_batch_processor(BatchProcessor processor) {
    batch_processor_ = std::move(processor);
}

void EventAggregator::start() {
    if (running_.load()) {
        return;
    }
    
    running_.store(true);
    worker_thread_ = std::thread(&EventAggregator::process_batches, this);
}

void EventAggregator::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    batch_condition_.notify_all();
    
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
    
    // Process remaining events
    flush();
}

void EventAggregator::flush() {
    std::unique_lock lock(batch_mutex_);
    
    if (!current_batch_.empty() && batch_processor_) {
        try {
            batch_processor_(std::move(current_batch_));
        } catch (const std::exception& e) {
            std::cerr << "Error processing event batch: " << e.what() << std::endl;
        }
        current_batch_.clear();
    }
}

void EventAggregator::process_batches() {
    while (running_.load()) {
        std::unique_lock lock(batch_mutex_);
        
        // Wait for batch interval or events
        auto wait_result = batch_condition_.wait_for(lock, batch_interval_, [this] {
            return !current_batch_.empty() || !running_.load();
        });
        
        // Process batch if we have events or timeout occurred
        if (!current_batch_.empty() && batch_processor_) {
            try {
                auto batch = std::move(current_batch_);
                current_batch_.clear();
                lock.unlock();
                
                batch_processor_(std::move(batch));
            } catch (const std::exception& e) {
                std::cerr << "Error processing event batch: " << e.what() << std::endl;
            }
        }
    }
}

} // namespace RoboQuant::Trading
