{"strategies": [{"id": "futures_momentum_001", "name": "Futures Momentum Strategy", "description": "Momentum-based futures trading strategy", "enabled": true, "type": "FuturesStrategy", "parameters": {"entry_threshold": 0.02, "exit_threshold": 0.01, "stop_loss_pct": 0.05, "take_profit_pct": 0.1, "max_position_size": 1000000.0, "risk_per_trade": 0.02, "min_hold_time_minutes": 5, "max_hold_time_hours": 24, "enable_market_timing": true, "market_timing_model": "market_regime_001", "enable_position_sizing": true, "enable_stop_loss": true, "enable_take_profit": true, "prediction_model": "futures_pred_001", "min_prediction_confidence": 0.6, "use_market_orders": false, "limit_order_offset": 0.001, "order_timeout_minutes": 5, "allowed_assets": ["IF2412.CFFEX", "IC2412.CFFEX", "IH2412.CFFEX", "IM2412.CFFEX"]}}, {"id": "stock_alpha_001", "name": "Stock Alpha Strategy", "description": "Multi-factor alpha stock strategy", "enabled": true, "type": "StockStrategy", "parameters": {"long_entry_threshold": 0.02, "long_exit_threshold": 0.01, "stop_loss_pct": 0.08, "take_profit_pct": 0.15, "max_position_value": 100000.0, "max_portfolio_weight": 0.05, "risk_per_trade": 0.01, "min_hold_time_hours": 1, "max_hold_time_days": 30, "avoid_earnings_season": true, "avoid_ex_dividend_date": true, "avoided_industries": ["Mining", "Utilities"], "min_volume": 1000000.0, "min_price": 5.0, "max_price": 1000.0, "min_market_cap": 100000000.0, "use_limit_orders": true, "limit_order_offset": 0.002, "order_timeout_minutes": 10, "alpha_model": "stock_alpha_001", "risk_model": "stock_risk_001", "min_alpha_score": 0.6, "enable_sector_limits": true, "max_sector_weight": 0.2, "enable_correlation_limits": true, "max_correlation": 0.7}}]}