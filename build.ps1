# RoboQuant跨平台构建脚本 - Windows PowerShell版本
# 支持完整的构建、测试、安装工作流

param(
    [string]$BuildType = "Release",
    [string]$Generator = "Visual Studio 17 2022",
    [string]$Platform = "x64",
    [switch]$Clean,
    [switch]$Test,
    [switch]$Install,
    [switch]$Package,
    [switch]$Verbose,
    [switch]$EnableLibTorch,
    [switch]$EnableTALib,
    [switch]$EnableTests,
    [switch]$EnableExamples,
    [switch]$StaticLinking,
    [string]$InstallPrefix = "",
    [string]$LibTorchPath = "",
    [string]$TALibPath = "",
    [int]$Jobs = 0
)

# ============================================================================
# 颜色输出函数
# ============================================================================

$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue
$Cyan = [System.ConsoleColor]::Cyan

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "INFO: $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "SUCCESS: $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "WARNING: $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "ERROR: $message"
}

function Write-Header($message) {
    Write-ColorOutput $Cyan "============================================================================"
    Write-ColorOutput $Cyan $message
    Write-ColorOutput $Cyan "============================================================================"
}

# ============================================================================
# 环境检查函数
# ============================================================================

function Test-Prerequisites {
    Write-Header "检查构建环境"
    
    # 检查CMake
    if (-not (Get-Command cmake -ErrorAction SilentlyContinue)) {
        Write-Error "CMake未安装或不在PATH中"
        Write-Info "请从 https://cmake.org/download/ 下载并安装CMake"
        exit 1
    }
    
    $cmakeVersion = (cmake --version | Select-String "cmake version" | ForEach-Object { $_.ToString().Split()[2] })
    Write-Success "找到CMake版本: $cmakeVersion"
    
    # 检查Visual Studio（如果使用VS生成器）
    if ($Generator -like "*Visual Studio*") {
        $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
        if (Test-Path $vsWhere) {
            $vsInfo = & $vsWhere -latest -property displayName,installationVersion
            if ($vsInfo) {
                Write-Success "找到Visual Studio: $($vsInfo[0]) (版本 $($vsInfo[1]))"
            }
        } else {
            Write-Warning "无法检测Visual Studio安装"
        }
    }
    
    # 检查Git
    if (Get-Command git -ErrorAction SilentlyContinue) {
        $gitVersion = (git --version | ForEach-Object { $_.ToString().Split()[2] })
        Write-Success "找到Git版本: $gitVersion"
    } else {
        Write-Warning "Git未安装，某些依赖可能无法获取"
    }
    
    Write-Success "环境检查完成"
}

function Test-Dependencies {
    Write-Header "检查可选依赖"
    
    # 检查LibTorch
    if ($EnableLibTorch -or $LibTorchPath) {
        $torchPaths = @(
            $LibTorchPath,
            "E:\BaseLibrary\libtorch",
            "C:\libtorch",
            "C:\Program Files\libtorch"
        ) | Where-Object { $_ -and (Test-Path $_) }
        
        if ($torchPaths) {
            Write-Success "找到LibTorch: $($torchPaths[0])"
            $script:LibTorchPath = $torchPaths[0]
        } else {
            Write-Warning "LibTorch未找到，AI/ML功能将被禁用"
        }
    }
    
    # 检查TA-Lib
    if ($EnableTALib -or $TALibPath) {
        $talibPaths = @(
            $TALibPath,
            "E:\BaseLibrary\ta-lib-precompiled",
            "E:\BaseLibrary\ta-lib",
            "C:\Program Files\TA-Lib",
            "C:\Program Files (x86)\TA-Lib"
        ) | Where-Object { $_ -and (Test-Path $_) }
        
        if ($talibPaths) {
            Write-Success "找到TA-Lib: $($talibPaths[0])"
            $script:TALibPath = $talibPaths[0]
        } else {
            Write-Warning "TA-Lib未找到，技术分析功能将受限"
        }
    }
}

# ============================================================================
# 构建配置函数
# ============================================================================

function Get-BuildConfiguration {
    Write-Header "构建配置"
    
    # 项目根目录
    $script:ProjectRoot = $PSScriptRoot
    $script:BuildDir = Join-Path $ProjectRoot "build"
    
    # 并行作业数
    if ($Jobs -eq 0) {
        $script:Jobs = [Environment]::ProcessorCount
    } else {
        $script:Jobs = $Jobs
    }
    
    # 安装前缀
    if (-not $InstallPrefix) {
        $script:InstallPrefix = Join-Path $BuildDir "install"
    } else {
        $script:InstallPrefix = $InstallPrefix
    }
    
    Write-Info "项目根目录: $ProjectRoot"
    Write-Info "构建目录: $BuildDir"
    Write-Info "构建类型: $BuildType"
    Write-Info "生成器: $Generator"
    Write-Info "平台: $Platform"
    Write-Info "并行作业: $Jobs"
    Write-Info "安装前缀: $InstallPrefix"
    
    if ($EnableLibTorch) { Write-Info "LibTorch: 启用" }
    if ($EnableTALib) { Write-Info "TA-Lib: 启用" }
    if ($EnableTests) { Write-Info "测试: 启用" }
    if ($EnableExamples) { Write-Info "示例: 启用" }
    if ($StaticLinking) { Write-Info "静态链接: 启用" }
}

# ============================================================================
# 构建函数
# ============================================================================

function Invoke-Clean {
    if ($Clean -and (Test-Path $BuildDir)) {
        Write-Header "清理构建目录"
        Remove-Item -Recurse -Force $BuildDir
        Write-Success "构建目录已清理"
    }
}

function Invoke-Configure {
    Write-Header "配置项目"
    
    # 创建构建目录
    if (-not (Test-Path $BuildDir)) {
        New-Item -ItemType Directory -Path $BuildDir | Out-Null
    }
    
    # 切换到构建目录
    Push-Location $BuildDir
    
    try {
        # 构建CMake参数
        $configArgs = @(
            ".."
            "-G", $Generator
            "-A", $Platform
            "-DCMAKE_BUILD_TYPE=$BuildType"
            "-DCMAKE_INSTALL_PREFIX=$InstallPrefix"
        )
        
        # 添加可选参数
        if ($EnableLibTorch) {
            $configArgs += "-DROBOQUANT_ENABLE_LIBTORCH=ON"
            if ($script:LibTorchPath) {
                $configArgs += "-DCMAKE_PREFIX_PATH=$($script:LibTorchPath)"
            }
        }
        
        if ($EnableTALib) {
            $configArgs += "-DROBOQUANT_ENABLE_TALIB=ON"
            if ($script:TALibPath) {
                $configArgs += "-DTALIB_ROOT=$($script:TALibPath)"
            }
        }
        
        if ($EnableTests) {
            $configArgs += "-DROBOQUANT_BUILD_TESTS=ON"
        }
        
        if ($EnableExamples) {
            $configArgs += "-DROBOQUANT_BUILD_EXAMPLES=ON"
        }
        
        if ($StaticLinking) {
            $configArgs += "-DROBOQUANT_STATIC_LINKING=ON"
        }
        
        if ($Verbose) {
            $configArgs += "--debug-output"
        }
        
        # 执行配置
        Write-Info "执行: cmake $($configArgs -join ' ')"
        & cmake @configArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "CMake配置失败"
        }
        
        Write-Success "项目配置完成"
        
    } finally {
        Pop-Location
    }
}

function Invoke-Build {
    Write-Header "构建项目"
    
    Push-Location $BuildDir
    
    try {
        $buildArgs = @(
            "--build", "."
            "--config", $BuildType
            "--parallel", $Jobs
        )
        
        if ($Verbose) {
            $buildArgs += "--verbose"
        }
        
        Write-Info "执行: cmake $($buildArgs -join ' ')"
        & cmake @buildArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "项目构建失败"
        }
        
        Write-Success "项目构建完成"
        
    } finally {
        Pop-Location
    }
}

function Invoke-Test {
    if (-not $Test) {
        return
    }
    
    Write-Header "运行测试"
    
    Push-Location $BuildDir
    
    try {
        $testArgs = @(
            "--output-on-failure"
            "--config", $BuildType
        )
        
        if ($Verbose) {
            $testArgs += "--verbose"
        }
        
        if ($Jobs -gt 1) {
            $testArgs += "--parallel", $Jobs
        }
        
        Write-Info "执行: ctest $($testArgs -join ' ')"
        & ctest @testArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "所有测试通过"
        } else {
            Write-Warning "部分测试失败"
        }
        
    } finally {
        Pop-Location
    }
}

function Invoke-Install {
    if (-not $Install) {
        return
    }
    
    Write-Header "安装项目"
    
    Push-Location $BuildDir
    
    try {
        $installArgs = @(
            "--install", "."
            "--config", $BuildType
        )
        
        Write-Info "执行: cmake $($installArgs -join ' ')"
        & cmake @installArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "项目安装失败"
        }
        
        Write-Success "项目安装完成"
        Write-Info "安装位置: $InstallPrefix"
        
    } finally {
        Pop-Location
    }
}

function Invoke-Package {
    if (-not $Package) {
        return
    }
    
    Write-Header "打包项目"
    
    Push-Location $BuildDir
    
    try {
        Write-Info "执行: cpack"
        & cpack
        
        if ($LASTEXITCODE -ne 0) {
            throw "项目打包失败"
        }
        
        Write-Success "项目打包完成"
        
    } finally {
        Pop-Location
    }
}

# ============================================================================
# 主函数
# ============================================================================

function Main {
    try {
        Write-Header "RoboQuant构建系统 - Windows版本"
        
        # 检查环境
        Test-Prerequisites
        Test-Dependencies
        
        # 获取配置
        Get-BuildConfiguration
        
        # 执行构建流程
        Invoke-Clean
        Invoke-Configure
        Invoke-Build
        Invoke-Test
        Invoke-Install
        Invoke-Package
        
        Write-Header "构建完成"
        Write-Success "RoboQuant构建成功完成！"
        
        # 显示使用信息
        Write-Info ""
        Write-Info "使用示例:"
        Write-Info "  .\build.ps1                                    # 默认Release构建"
        Write-Info "  .\build.ps1 -BuildType Debug                   # Debug构建"
        Write-Info "  .\build.ps1 -Clean -Test                      # 清理并测试"
        Write-Info "  .\build.ps1 -EnableLibTorch -EnableTALib      # 启用AI和技术分析"
        Write-Info "  .\build.ps1 -Install -Package                 # 安装并打包"
        
    } catch {
        Write-Error "构建失败: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main