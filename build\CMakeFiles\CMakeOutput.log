The system is: Windows - 10.0.26100 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
鐢熸垚鍚姩鏃堕棿涓?2025/7/16 12:39:28銆?

鑺傜偣 1 涓婄殑椤圭洰鈥淓:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
PrepareForBuild:
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\鈥濄€?
  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\CompilerIdCXX.tlog\鈥濄€?
InitializeBuildStatus:
  姝ｅ湪鍒涘缓鈥淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
VcpkgTripletSelection:
  Using triplet "x64-windows" from "E:\BaseLibrary\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\BaseLibrary\vcpkg\installed\x64-windows\lib" /LIBPATH:"E:\BaseLibrary\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\BaseLibrary\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.exe
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\BaseLibrary\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.exe" "E:\BaseLibrary\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"
  'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
  鎴栨壒澶勭悊鏂囦欢銆?
  鍛戒护鈥減wsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\BaseLibrary\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.exe" "E:\BaseLibrary\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"鈥濆凡閫€鍑猴紝浠ｇ爜涓?9009銆?
  "C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "E:\BaseLibrary\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.exe" "E:\BaseLibrary\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
宸插畬鎴愮敓鎴愰」鐩€淓:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdCXX\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?

宸叉垚鍔熺敓鎴愩€?
    0 涓鍛?
    0 涓敊璇?

宸茬敤鏃堕棿 00:00:03.61


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "E:/lab/RoboQuant/Experiments/build/CMakeFiles/3.25.0-rc2/CompilerIdCXX/CompilerIdCXX.exe"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: E:/lab/RoboQuant/Experiments/build/CMakeFiles/CMakeScratch/TryCompile-2y7tr0

Run Build Command(s):d:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_b5867.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
  鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
  cl /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b5867.dir\Debug\\" /Fd"cmTC_b5867.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXCompilerABI.cpp"

  CMakeCXXCompilerABI.cpp

  cmTC_b5867.vcxproj -> E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-2y7tr0\Debug\cmTC_b5867.exe

  'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
  鎴栨壒澶勭悊鏂囦欢銆?



Determining if the function pthread_create exists in the pthreads passed with the following output:
Change Dir: E:/lab/RoboQuant/Experiments/build/CMakeFiles/CMakeScratch/TryCompile-zdyrzk

Run Build Command(s):d:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_5ae05.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
  鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
  cl /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_5ae05.dir\Debug\\" /Fd"cmTC_5ae05.dir\Debug\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-zdyrzk\CheckFunctionExists.cxx"

  CheckFunctionExists.cxx

  cmTC_5ae05.vcxproj -> E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-zdyrzk\Debug\cmTC_5ae05.exe

  'pwsh.exe' 涓嶆槸鍐呴儴鎴栧閮ㄥ懡浠わ紝涔熶笉鏄彲杩愯鐨勭▼搴?
  鎴栨壒澶勭悊鏂囦欢銆?



Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
鐢熸垚鍚姩鏃堕棿涓?2025/7/16 14:22:13銆?

鑺傜偣 1 涓婄殑椤圭洰鈥淓:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdC\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
PrepareForBuild:
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\鈥濄€?
  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\CompilerIdC.tlog\鈥濄€?
InitializeBuildStatus:
  姝ｅ湪鍒涘缓鈥淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
VcpkgTripletSelection:
  Using triplet "x64-windows" from "E:\BaseLibrary\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
  CMakeCCompilerId.c
Link:
  d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\BaseLibrary\vcpkg\installed\x64-windows\lib" /LIBPATH:"E:\BaseLibrary\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\BaseLibrary\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdC.lib" /MACHINE:X64 Debug\CMakeCCompilerId.obj
  CompilerIdC.vcxproj -> E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdC\CompilerIdC.exe
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\BaseLibrary\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "E:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdC\CompilerIdC.exe" "E:\BaseLibrary\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdC.tlog\CompilerIdC.write.1u.tlog" "Debug\vcpkg.applocal.log"
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_C_COMPILER=d:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdC.tlog\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
宸插畬鎴愮敓鎴愰」鐩€淓:\lab\RoboQuant\Experiments\build\CMakeFiles\3.25.0-rc2\CompilerIdC\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?

宸叉垚鍔熺敓鎴愩€?
    0 涓鍛?
    0 涓敊璇?

宸茬敤鏃堕棿 00:00:03.82


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"

The C compiler identification is MSVC, found in "E:/lab/RoboQuant/Experiments/build/CMakeFiles/3.25.0-rc2/CompilerIdC/CompilerIdC.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: E:/lab/RoboQuant/Experiments/build/CMakeFiles/CMakeScratch/TryCompile-89kdtx

Run Build Command(s):d:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_b6bcd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
  鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
  cl /c /I"E:\BaseLibrary\vcpkg\installed\x64-windows\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b6bcd.dir\Debug\\" /Fd"cmTC_b6bcd.dir\Debug\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCCompilerABI.c"

  CMakeCCompilerABI.c

  cmTC_b6bcd.vcxproj -> E:\lab\RoboQuant\Experiments\build\CMakeFiles\CMakeScratch\TryCompile-89kdtx\Debug\cmTC_b6bcd.exe




