#pragma once

#include "brokers/BrokerInterface.h"
#include "accounts/Account.h"
#include "utils/ThreadSafety.h"
#include <unordered_map>
#include <unordered_set>
#include <queue>
#include <atomic>
#include <thread>
#include <condition_variable>

// CTP API 头文件 (需要根据实际 CTP SDK 路径调整)
// #include "ThostFtdcMdApi.h"
// #include "ThostFtdcTraderApi.h"

namespace RoboQuant::Broker {

// CTP 配置结构
struct CtpConfig {
    std::string front_address;          // 交易前置地址
    std::string md_front_address;       // 行情前置地址
    std::string broker_id;              // 经纪商代码
    std::string user_id;                // 用户代码
    std::string password;               // 密码
    std::string app_id;                 // 应用标识
    std::string auth_code;              // 授权编码
    std::string user_product_info;      // 用户端产品信息
    std::string flow_path;              // 流文件存放目录
    
    // 连接参数
    int connect_timeout_ms{10000};      // 连接超时
    int heartbeat_interval_ms{30000};   // 心跳间隔
    bool auto_reconnect{true};          // 自动重连
    int max_reconnect_attempts{5};      // 最大重连次数
    
    // 交易参数
    bool enable_market_data{true};      // 启用行情
    bool enable_trading{true};          // 启用交易
    int request_interval_ms{1000};      // 请求间隔
    
    nlohmann::json to_json() const;
    static CtpConfig from_json(const nlohmann::json& j);
};

// CTP 订单状态映射
struct CtpOrderStatusMap {
    static OrderStatus from_ctp_status(char ctp_status);
    static char to_ctp_status(OrderStatus status);
    static OrderSide from_ctp_direction(char direction);
    static char to_ctp_direction(OrderSide side);
    static OrderType from_ctp_order_type(char order_type);
    static char to_ctp_order_type(OrderType type);
};

// CTP 请求管理器
class CtpRequestManager {
public:
    CtpRequestManager(int interval_ms = 1000);
    ~CtpRequestManager();
    
    // 添加请求到队列
    void add_request(std::function<int()> request);
    
    // 启动/停止请求处理
    void start();
    void stop();
    
    // 获取统计信息
    size_t pending_requests() const;
    size_t total_requests() const;

private:
    void worker_thread();
    
    std::atomic<bool> running_{false};
    std::thread worker_;
    std::chrono::milliseconds interval_;
    
    mutable std::mutex queue_mutex_;
    std::queue<std::function<int()>> request_queue_;
    std::condition_variable queue_cv_;
    
    std::atomic<size_t> total_requests_{0};
};

// CTP 交易接口实现
class CtpBroker : public BaseBroker {
public:
    explicit CtpBroker(const CtpConfig& config = {});
    ~CtpBroker() override;
    
    // 禁止拷贝和移动
    CtpBroker(const CtpBroker&) = delete;
    CtpBroker& operator=(const CtpBroker&) = delete;
    CtpBroker(CtpBroker&&) = delete;
    CtpBroker& operator=(CtpBroker&&) = delete;
    
    // Broker 信息
    BrokerCapabilities get_capabilities() const override;
    
    // 连接管理
    Result<void> connect(const ConnectionInfo& info) override;
    Result<void> disconnect() override;
    Result<void> authenticate(const std::string& username, const std::string& password) override;
    
    // 订单管理
    Result<void> submit_order(const Order& order) override;
    Result<void> cancel_order(const Order& order) override;
    Result<void> modify_order(const Order& order, const OrderRequest& new_request) override;
    Result<std::vector<Order>> query_orders(const AccountId& account_id = "") override;
    Result<Order> query_order(const OrderId& order_id) override;
    
    // 账户管理
    Result<std::vector<Account>> query_accounts() override;
    Result<Account> query_account(const AccountId& account_id) override;
    Result<std::vector<Position>> query_positions(const AccountId& account_id = "") override;
    Result<std::vector<Trade>> query_trades(const AccountId& account_id = "", 
                                           const TimePoint& from = {},
                                           const TimePoint& to = {}) override;
    
    // 行情订阅
    Result<void> subscribe_market_data(const AssetId& asset_id) override;
    Result<void> unsubscribe_market_data(const AssetId& asset_id) override;
    
    // 健康检查
    Result<void> health_check() override;
    
    // CTP 特有方法
    void set_ctp_config(const CtpConfig& config);
    CtpConfig get_ctp_config() const;
    
    // 获取 CTP 连接状态
    bool is_trader_connected() const { return trader_connected_.load(); }
    bool is_md_connected() const { return md_connected_.load(); }
    bool is_authenticated() const override;
    
    // 获取统计信息
    size_t pending_requests() const;
    std::string get_trading_day() const;

private:
    // CTP API 回调处理 (这些方法会在实际的 CTP 回调中调用)
    void on_front_connected();
    void on_front_disconnected(int reason);
    void on_rsp_authenticate(bool success, const std::string& error_msg);
    void on_rsp_user_login(bool success, const std::string& error_msg);
    void on_rsp_user_logout(bool success, const std::string& error_msg);
    void on_rsp_order_insert(const std::string& order_id, bool success, const std::string& error_msg);
    void on_rsp_order_action(const std::string& order_id, bool success, const std::string& error_msg);
    void on_rtn_order(const std::string& order_id, OrderStatus status, Quantity filled_qty);
    void on_rtn_trade(const std::string& trade_id, const std::string& order_id, 
                     const AssetId& asset_id, OrderSide side, Quantity quantity, Price price);
    void on_rsp_qry_trading_account(const AccountBalance& balance);
    void on_rsp_qry_investor_position(const Position& position);
    
    // 内部方法
    void initialize_apis();
    void cleanup_apis();
    void start_heartbeat();
    void stop_heartbeat();
    void heartbeat_worker();
    
    // 订单管理
    void add_order_internal(const Order& order);
    void update_order_internal(const OrderId& order_id, std::function<void(Order&)> updater);
    Order* find_order_internal(const OrderId& order_id);
    
    // 请求方法
    int req_authenticate();
    int req_user_login();
    int req_user_logout();
    int req_order_insert(const Order& order);
    int req_order_action(const Order& order);
    int req_qry_trading_account();
    int req_qry_investor_position();
    int req_qry_order();
    int req_qry_trade();
    
    // 配置和状态
    CtpConfig config_;
    std::atomic<bool> trader_connected_{false};
    std::atomic<bool> md_connected_{false};
    std::atomic<bool> authenticated_{false};
    std::atomic<bool> logged_in_{false};
    
    // 请求管理
    std::unique_ptr<CtpRequestManager> request_manager_;
    std::atomic<int> request_id_{0};
    
    // 数据存储
    mutable std::shared_mutex orders_mutex_;
    std::unordered_map<OrderId, Order> orders_;
    std::unordered_map<std::string, OrderId> ctp_order_map_; // CTP订单号 -> 内部订单号
    
    mutable std::shared_mutex accounts_mutex_;
    std::unordered_map<AccountId, Account> accounts_;
    
    mutable std::shared_mutex positions_mutex_;
    std::unordered_map<std::string, Position> positions_; // key: account_id + asset_id
    
    mutable std::shared_mutex trades_mutex_;
    std::vector<Trade> trades_;
    
    // 行情订阅
    mutable std::shared_mutex subscriptions_mutex_;
    std::unordered_set<AssetId> subscribed_assets_;
    
    // 心跳线程
    std::atomic<bool> heartbeat_running_{false};
    std::thread heartbeat_thread_;
    
    // CTP API 指针 (实际使用时需要包含 CTP 头文件)
    // CThostFtdcTraderApi* trader_api_{nullptr};
    // CThostFtdcMdApi* md_api_{nullptr};
    
    // 模拟 CTP API 指针 (用于编译)
    void* trader_api_{nullptr};
    void* md_api_{nullptr};
    
    // 交易日
    std::string trading_day_;
    
    // 会话参数
    int front_id_{0};
    int session_id_{0};
    std::string order_ref_prefix_;
};

// CTP Broker 工厂
class CtpBrokerFactory : public BrokerFactory {
public:
    BrokerType get_broker_type() const override { return BrokerType::CTP; }
    std::string get_broker_name() const override { return "CTP Broker"; }
    BrokerInterfacePtr create_broker() override;
    BrokerInterfacePtr create_broker(const BrokerConfig& config) override;
    bool supports_config(const BrokerConfig& config) const override;
};

// 工具函数
void register_ctp_broker();
CtpConfig create_default_ctp_config();
std::string generate_ctp_order_ref();
std::string ctp_error_msg_to_string(int error_id, const std::string& error_msg);

} // namespace RoboQuant::Broker
