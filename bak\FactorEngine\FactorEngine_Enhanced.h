#pragma once

#include "ThreadPool_Enhanced.h"
#include "RollingWindow_SIMD.h" // 使用SIMD版本
#include <string>
#include <unordered_map>
#include <memory>
#include <shared_mutex> // 读写锁
#include <boost/pool/object_pool.hpp> // 内存池
#include "DataTypes.h"

namespace QuantEngine {

// 使用别名
using RollingWindowType = RollingWindowSIMD;

class SecurityEnhanced {
public:
    SecurityEnhanced(const std::string& ticker) : m_ticker(ticker) {}

    void addFactor(const std::string& factor_name, size_t window_size, boost::object_pool<RollingWindowType>& pool) {
        std::unique_lock<std::shared_mutex> lock(m_mutex); // 写锁
        RollingWindowType* window = pool.construct(window_size); // 从内存池分配
        m_factors.emplace(factor_name, std::unique_ptr<RollingWindowType, std::function<void(RollingWindowType*)>>(
            window,
            [&pool](RollingWindowType* p) { pool.destroy(p); } // 自定义删除器，归还内存池
        ));
    }

    void updateFactor(const std::string& factor_name, double value) {
        std::shared_lock<std::shared_mutex> lock(m_mutex); // 读锁
        auto it = m_factors.find(factor_name);
        if (it != m_factors.end()) {
            it->second->push(value); // RollingWindow内部是线程安全的(增量更新)无需外部同步
        }
    }

    double getStandardizedValue(const std::string& factor_name, double current_value) {
        std::shared_lock<std::shared_mutex> lock(m_mutex); // 读锁
        auto it = m_factors.find(factor_name);
        if (it != m_factors.end()) {
            return it->second->standardize(current_value);
        }
        return 0.0;
    }

private:
    std::string m_ticker;
    // 使用读写锁，允许多线程同时读取
    std::shared_mutex m_mutex; 
    // 使用 unique_ptr 和自定义删除器来管理内存池的对象
    std::unordered_map<std::string, std::unique_ptr<RollingWindowType, std::function<void(RollingWindowType*)>>> m_factors;
};


class FactorEngineEnhanced {
public:
    FactorEngineEnhanced(size_t num_threads = std::thread::hardware_concurrency()) 
        : m_pool(num_threads), m_window_pool(32) {} // 32是内存池的初始大小

    void addSecurity(const std::string& ticker, const std::vector<std::pair<std::string, size_t>>& factor_configs) {
        std::unique_lock<std::shared_mutex> lock(m_securities_mutex);
        auto security = std::make_shared<SecurityEnhanced>(ticker);
        for (const auto& config : factor_configs) {
            security->addFactor(config.first, config.second, m_window_pool);
        }
        m_securities[ticker] = security;
    }

    void processTick(const TickData& tick) {
        m_pool.enqueue([this, tick] {
            std::shared_ptr<SecurityEnhanced> security;
            {
                std::shared_lock<std::shared_mutex> lock(m_securities_mutex);
                auto it = m_securities.find(tick.ticker);
                if (it == m_securities.end()) return;
                security = it->second;
            }
            security->updateFactor(tick.factor_name, tick.value);
        });
    }
  
    // 查询标准化值的方法与之前相同
    double queryStandardizedValue(const std::string& ticker, const std::string& factor_name, double current_value) {
        std::shared_lock<std::shared_mutex> lock(m_securities_mutex);
        auto it = m_securities.find(ticker);
        if (it != m_securities.end()) {
            return it->second->getStandardizedValue(factor_name, current_value);
        }
        return 0.0; // Not found
    }

private:
    ThreadPoolEnhanced m_pool;
    std::unordered_map<std::string, std::shared_ptr<SecurityEnhanced>> m_securities;
    std::shared_mutex m_securities_mutex;
    boost::object_pool<RollingWindowType> m_window_pool; // 为RollingWindow对象提供内存池
};

} // namespace QuantEngine