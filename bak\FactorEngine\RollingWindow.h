
#pragma once

#include <vector>
#include <cmath>
#include <stdexcept>

// ʵO(1)µĹ
class RollingWindow {
public:
    RollingWindow(size_t window_size = 0) : 
        m_window_size(window_size), m_count(0), m_mean(0.0), m_s(0.0), m_head(0) {
        if (m_window_size > 0) {
            m_buffer.resize(m_window_size, 0.0);
        }
    }

    void push(double value) {
        if (m_window_size == 0) return; // Window size 0 means no-op

        double old_value = 0.0;
        if (m_count == m_window_size) {
            old_value = m_buffer[m_head];
        }

        m_buffer[m_head] = value;
        m_head = (m_head + 1) % m_window_size;

        double old_mean = m_mean;
        if (m_count < m_window_size) {
            m_count++;
            m_mean = old_mean + (value - old_mean) / m_count;
            m_s += (value - old_mean) * (value - m_mean);
        } else { // Window is full, handle removal and addition
            m_mean = old_mean + (value - old_value) / m_window_size;
            m_s += (value - old_value) * (value + old_value - m_mean - old_mean);
        }
    }

    double getMean() const { return m_mean; }
  
    double getVariance() const {
        return (m_count > 1) ? m_s / (m_count - 1) : 0.0;
    }
  
    double getStdDev() const {
        return std::sqrt(getVariance());
    }

    double standardize(double value) const {
        double std_dev = getStdDev();
        if (std::abs(std_dev) < 1e-9) { // Avoid division by zero
            return 0.0;
        }
        return (value - m_mean) / std_dev;
    }
  
    size_t getCount() const { return m_count; }

private:
    size_t m_window_size;
    std::vector<double> m_buffer; // Ring buffer
    size_t m_count; // Number of elements currently in buffer
  
    // For Welford's algorithm
    double m_mean;
    double m_s; // Sum of squares of differences from the current mean

    size_t m_head; // Pointer to the next insertion point
};
