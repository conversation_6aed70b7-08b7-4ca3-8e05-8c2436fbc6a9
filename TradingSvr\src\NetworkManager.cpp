/**
 * @file NetworkManager.cpp
 * @brief Implementation of network communication classes
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/NetworkManager.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <random>
#include <regex>

namespace RoboQuant::Trading {

// Helper function to generate unique message IDs
std::string generate_message_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 8; ++i) {
        ss << dis(gen);
    }
    return ss.str();
}

// Helper function to generate unique connection IDs
std::string generate_connection_id() {
    return "conn_" + generate_message_id();
}

// NetworkMessage Implementation
std::string NetworkMessage::to_json() const {
    std::ostringstream oss;
    oss << "{"
        << "\"id\":\"" << id << "\","
        << "\"type\":\"" << type << "\","
        << "\"payload\":\"" << payload << "\","
        << "\"timestamp\":" << std::chrono::duration_cast<std::chrono::milliseconds>(timestamp.time_since_epoch()).count();
    
    if (!headers.empty()) {
        oss << ",\"headers\":{";
        bool first = true;
        for (const auto& [key, value] : headers) {
            if (!first) oss << ",";
            oss << "\"" << key << "\":\"" << value << "\"";
            first = false;
        }
        oss << "}";
    }
    
    oss << "}";
    return oss.str();
}

std::optional<NetworkMessage> NetworkMessage::from_json(const std::string& json) {
    // Simplified JSON parsing - in production would use proper JSON library
    NetworkMessage msg;
    
    // Extract basic fields using regex (simplified approach)
    std::regex id_regex("\"id\"\\s*:\\s*\"([^\"]*)\"");
    std::regex type_regex("\"type\"\\s*:\\s*\"([^\"]*)\"");
    std::regex payload_regex("\"payload\"\\s*:\\s*\"([^\"]*)\"");

    std::smatch match;
    
    if (std::regex_search(json, match, id_regex)) {
        msg.id = match[1].str();
    }
    if (std::regex_search(json, match, type_regex)) {
        msg.type = match[1].str();
    }
    if (std::regex_search(json, match, payload_regex)) {
        msg.payload = match[1].str();
    }
    
    msg.timestamp = std::chrono::system_clock::now();
    
    return msg.id.empty() ? std::nullopt : std::make_optional(msg);
}

NetworkMessage NetworkMessage::create_request(const std::string& type, const std::string& payload) {
    NetworkMessage msg;
    msg.id = generate_message_id();
    msg.type = type;
    msg.payload = payload;
    msg.timestamp = std::chrono::system_clock::now();
    msg.headers["message_type"] = "request";
    return msg;
}

NetworkMessage NetworkMessage::create_response(const std::string& request_id, const std::string& payload) {
    NetworkMessage msg;
    msg.id = generate_message_id();
    msg.type = "response";
    msg.payload = payload;
    msg.timestamp = std::chrono::system_clock::now();
    msg.headers["message_type"] = "response";
    msg.headers["request_id"] = request_id;
    return msg;
}

NetworkMessage NetworkMessage::create_notification(const std::string& type, const std::string& payload) {
    NetworkMessage msg;
    msg.id = generate_message_id();
    msg.type = type;
    msg.payload = payload;
    msg.timestamp = std::chrono::system_clock::now();
    msg.headers["message_type"] = "notification";
    return msg;
}

std::string NetworkMessage::generate_message_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint64_t> dis;
    
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    auto random_part = dis(gen);
    
    std::ostringstream oss;
    oss << "MSG_" << std::hex << timestamp << "_" << random_part;
    return oss.str();
}

// WebSocketConnection Implementation
WebSocketConnection::WebSocketConnection(asio::io_context& io_context)
    : io_context_(io_context), ws_(io_context), ping_timer_(io_context), reconnect_timer_(io_context) {
    connection_id_ = generate_connection_id();
}

WebSocketConnection::~WebSocketConnection() {
    if (connected_.load()) {
        auto disconnect_future = disconnect();
        disconnect_future.wait();
    }
}

std::future<bool> WebSocketConnection::connect(const std::string& endpoint) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    endpoint_ = endpoint;
    
    try {
        // Parse endpoint URL
        std::regex url_regex("ws://([^:/]+)(?::(\\d+))?(/.*)?");
        std::smatch match;
        
        if (!std::regex_match(endpoint, match, url_regex)) {
            promise.set_value(false);
            return future;
        }
        
        std::string host = match[1].str();
        std::string port = match[2].str().empty() ? "80" : match[2].str();
        std::string path = match[3].str().empty() ? "/" : match[3].str();
        
        // Resolve and connect (simplified implementation)
        asio::ip::tcp::resolver resolver(io_context_);
        auto endpoints = resolver.resolve(host, port);
        
        // Connect to the server
        beast::get_lowest_layer(ws_).connect(*endpoints.begin());
        
        // Set WebSocket options
        ws_.set_option(websocket::stream_base::timeout::suggested(beast::role_type::client));
        ws_.set_option(websocket::stream_base::decorator(
            [](websocket::request_type& req) {
                req.set(http::field::user_agent, "RoboQuant/1.0");
            }));
        
        // Perform WebSocket handshake
        ws_.handshake(host, path);
        
        connected_.store(true);
        
        // Start reading messages
        start_read();
        
        // Start ping timer
        if (ping_interval_.count() > 0) {
            start_ping_timer();
        }
        
        // Notify connection callback
        {
            std::lock_guard lock(callbacks_mutex_);
            if (connection_callback_) {
                connection_callback_(true);
            }
        }
        
        promise.set_value(true);
        
    } catch (const std::exception& e) {
        connected_.store(false);
        
        {
            std::lock_guard lock(callbacks_mutex_);
            if (error_callback_) {
                error_callback_("Connection failed: " + std::string(e.what()));
            }
        }
        
        promise.set_value(false);
    }
    
    return future;
}

std::future<void> WebSocketConnection::disconnect() {
    std::promise<void> promise;
    auto future = promise.get_future();
    
    if (!connected_.load()) {
        promise.set_value();
        return future;
    }
    
    try {
        connected_.store(false);
        
        // Cancel timers
        ping_timer_.cancel();
        reconnect_timer_.cancel();
        
        // Close WebSocket connection
        ws_.close(websocket::close_code::normal);
        
        // Notify connection callback
        {
            std::lock_guard lock(callbacks_mutex_);
            if (connection_callback_) {
                connection_callback_(false);
            }
        }
        
        promise.set_value();
        
    } catch (const std::exception& e) {
        {
            std::lock_guard lock(callbacks_mutex_);
            if (error_callback_) {
                error_callback_("Disconnect failed: " + std::string(e.what()));
            }
        }
        promise.set_exception(std::current_exception());
    }
    
    return future;
}

bool WebSocketConnection::is_connected() const {
    return connected_.load();
}

std::future<bool> WebSocketConnection::send_message(const NetworkMessage& message) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    if (!connected_.load()) {
        promise.set_value(false);
        return future;
    }
    
    try {
        std::string json_data = message.to_json();
        
        // Add to send queue
        {
            std::lock_guard lock(send_queue_mutex_);
            send_queue_.push(message);
        }
        send_condition_.notify_one();
        
        // For simplicity, assume success
        promise.set_value(true);
        
    } catch (const std::exception& e) {
        {
            std::lock_guard lock(callbacks_mutex_);
            if (error_callback_) {
                error_callback_("Send failed: " + std::string(e.what()));
            }
        }
        promise.set_value(false);
    }
    
    return future;
}

std::future<std::optional<NetworkMessage>> WebSocketConnection::receive_message() {
    std::promise<std::optional<NetworkMessage>> promise;
    auto future = promise.get_future();
    
    // This would be implemented with proper async reading
    // For now, return empty optional
    promise.set_value(std::nullopt);
    
    return future;
}

void WebSocketConnection::set_message_callback(MessageCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    message_callback_ = std::move(callback);
}

void WebSocketConnection::set_connection_callback(ConnectionCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    connection_callback_ = std::move(callback);
}

void WebSocketConnection::set_error_callback(ErrorCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    error_callback_ = std::move(callback);
}

std::string WebSocketConnection::get_endpoint() const {
    return endpoint_;
}

std::string WebSocketConnection::get_connection_id() const {
    return connection_id_;
}

void WebSocketConnection::set_ping_interval(Duration interval) {
    ping_interval_ = interval;
}

void WebSocketConnection::set_reconnect_interval(Duration interval) {
    reconnect_interval_ = interval;
}

void WebSocketConnection::enable_auto_reconnect(bool enable) {
    auto_reconnect_.store(enable);
}

void WebSocketConnection::start_read() {
    // This would implement async reading from WebSocket
    // For now, this is a placeholder
}

void WebSocketConnection::start_ping_timer() {
    ping_timer_.expires_after(ping_interval_);
    ping_timer_.async_wait([this](std::error_code ec) {
        if (!ec && connected_.load()) {
            // Send ping frame
            try {
                ws_.ping({});
                start_ping_timer(); // Schedule next ping
            } catch (const std::exception& e) {
                std::lock_guard lock(callbacks_mutex_);
                if (error_callback_) {
                    error_callback_("Ping failed: " + std::string(e.what()));
                }
            }
        }
    });
}

void WebSocketConnection::handle_reconnect() {
    if (!auto_reconnect_.load() || connected_.load()) {
        return;
    }
    
    reconnect_timer_.expires_after(reconnect_interval_);
    reconnect_timer_.async_wait([this](std::error_code ec) {
        if (!ec && !connected_.load() && auto_reconnect_.load()) {
            auto connect_future = connect(endpoint_);
            // Handle reconnection result asynchronously
        }
    });
}

std::string WebSocketConnection::generate_connection_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint64_t> dis;
    
    std::ostringstream oss;
    oss << "CONN_" << std::hex << dis(gen);
    return oss.str();
}

// HttpClient Implementation
HttpClient::HttpClient(asio::io_context& io_context) : io_context_(io_context) {}

std::future<http::response<http::string_body>> HttpClient::get(
    const std::string& url,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return make_request(http::verb::get, url, "", headers);
}

std::future<http::response<http::string_body>> HttpClient::post(
    const std::string& url,
    const std::string& body,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return make_request(http::verb::post, url, body, headers);
}

std::future<http::response<http::string_body>> HttpClient::put(
    const std::string& url,
    const std::string& body,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return make_request(http::verb::put, url, body, headers);
}

std::future<http::response<http::string_body>> HttpClient::delete_request(
    const std::string& url,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return make_request(http::verb::delete_, url, "", headers);
}

void HttpClient::set_timeout(Duration timeout) {
    timeout_ = timeout;
}

void HttpClient::set_user_agent(const std::string& user_agent) {
    user_agent_ = user_agent;
}

void HttpClient::set_default_headers(const std::unordered_map<std::string, std::string>& headers) {
    default_headers_ = headers;
}

std::future<http::response<http::string_body>> HttpClient::make_request(
    http::verb method,
    const std::string& url,
    const std::string& body,
    const std::unordered_map<std::string, std::string>& headers) {
    
    std::promise<http::response<http::string_body>> promise;
    auto future = promise.get_future();
    
    try {
        // Parse URL
        std::regex url_regex("https?://([^:/]+)(?::(\\d+))?(/.*)?");
        std::smatch match;
        
        if (!std::regex_match(url, match, url_regex)) {
            throw std::invalid_argument("Invalid URL format");
        }
        
        std::string host = match[1].str();
        std::string port = match[2].str().empty() ? "80" : match[2].str();
        std::string target = match[3].str().empty() ? "/" : match[3].str();
        
        // Create request
        http::request<http::string_body> req{method, target, 11};
        req.set(http::field::host, host);
        req.set(http::field::user_agent, user_agent_);
        
        // Add default headers
        for (const auto& [key, value] : default_headers_) {
            req.set(key, value);
        }
        
        // Add custom headers
        for (const auto& [key, value] : headers) {
            req.set(key, value);
        }
        
        // Set body if provided
        if (!body.empty()) {
            req.body() = body;
            req.prepare_payload();
        }
        
        // For now, create a simple response (placeholder implementation)
        http::response<http::string_body> res{http::status::ok, req.version()};
        res.set(http::field::server, "RoboQuant/1.0");
        res.set(http::field::content_type, "application/json");
        res.body() = R"({"status":"success","message":"Placeholder response"})";
        res.prepare_payload();
        
        promise.set_value(std::move(res));
        
    } catch (const std::exception& e) {
        promise.set_exception(std::current_exception());
    }
    
    return future;
}

// NetworkServer Implementation
NetworkServer::NetworkServer(asio::io_context& io_context, uint16_t port)
    : io_context_(io_context), acceptor_(io_context, asio::ip::tcp::endpoint(asio::ip::tcp::v4(), port)), port_(port) {}

NetworkServer::~NetworkServer() {
    if (running_.load()) {
        auto stop_future = stop();
        stop_future.wait();
    }
}

std::future<bool> NetworkServer::start() {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        running_.store(true);
        accept_connections();
        promise.set_value(true);
    } catch (const std::exception& e) {
        running_.store(false);
        promise.set_value(false);
    }

    return future;
}

std::future<void> NetworkServer::stop() {
    std::promise<void> promise;
    auto future = promise.get_future();

    running_.store(false);
    acceptor_.cancel();

    // Disconnect all clients
    {
        std::unique_lock lock(clients_mutex_);
        for (auto& [client_id, connection] : clients_) {
            if (connection) {
                auto disconnect_future = connection->disconnect();
                disconnect_future.wait();
            }
        }
        clients_.clear();
        client_groups_.clear();
    }

    promise.set_value();
    return future;
}

bool NetworkServer::is_running() const {
    return running_.load();
}

void NetworkServer::set_client_connected_callback(ClientConnectedCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    client_connected_callback_ = std::move(callback);
}

void NetworkServer::set_client_disconnected_callback(ClientDisconnectedCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    client_disconnected_callback_ = std::move(callback);
}

void NetworkServer::set_client_message_callback(ClientMessageCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    client_message_callback_ = std::move(callback);
}

std::future<bool> NetworkServer::send_to_client(const std::string& client_id, const NetworkMessage& message) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    std::shared_lock lock(clients_mutex_);
    auto it = clients_.find(client_id);

    if (it != clients_.end() && it->second) {
        auto send_future = it->second->send_message(message);
        promise.set_value(send_future.get());
    } else {
        promise.set_value(false);
    }

    return future;
}

std::future<void> NetworkServer::broadcast_message(const NetworkMessage& message) {
    std::promise<void> promise;
    auto future = promise.get_future();

    std::shared_lock lock(clients_mutex_);
    std::vector<std::future<bool>> send_futures;

    for (const auto& [client_id, connection] : clients_) {
        if (connection) {
            send_futures.push_back(connection->send_message(message));
        }
    }

    // Wait for all sends to complete
    for (auto& send_future : send_futures) {
        send_future.wait();
    }

    promise.set_value();
    return future;
}

std::future<void> NetworkServer::broadcast_to_group(const std::string& group, const NetworkMessage& message) {
    std::promise<void> promise;
    auto future = promise.get_future();

    std::shared_lock lock(clients_mutex_);
    auto group_it = client_groups_.find(group);

    if (group_it != client_groups_.end()) {
        std::vector<std::future<bool>> send_futures;

        for (const std::string& client_id : group_it->second) {
            auto client_it = clients_.find(client_id);
            if (client_it != clients_.end() && client_it->second) {
                send_futures.push_back(client_it->second->send_message(message));
            }
        }

        // Wait for all sends to complete
        for (auto& send_future : send_futures) {
            send_future.wait();
        }
    }

    promise.set_value();
    return future;
}

void NetworkServer::add_client_to_group(const std::string& client_id, const std::string& group) {
    std::unique_lock lock(clients_mutex_);
    client_groups_[group].insert(client_id);
}

void NetworkServer::remove_client_from_group(const std::string& client_id, const std::string& group) {
    std::unique_lock lock(clients_mutex_);
    auto it = client_groups_.find(group);
    if (it != client_groups_.end()) {
        it->second.erase(client_id);
        if (it->second.empty()) {
            client_groups_.erase(it);
        }
    }
}

std::vector<std::string> NetworkServer::get_connected_clients() const {
    std::shared_lock lock(clients_mutex_);
    std::vector<std::string> result;
    result.reserve(clients_.size());

    for (const auto& [client_id, connection] : clients_) {
        if (connection && connection->is_connected()) {
            result.push_back(client_id);
        }
    }

    return result;
}

std::vector<std::string> NetworkServer::get_clients_in_group(const std::string& group) const {
    std::shared_lock lock(clients_mutex_);
    auto it = client_groups_.find(group);

    if (it != client_groups_.end()) {
        return std::vector<std::string>(it->second.begin(), it->second.end());
    }

    return {};
}

void NetworkServer::accept_connections() {
    // This would implement async accept for incoming connections
    // For now, this is a placeholder
}

void NetworkServer::handle_client_connection(std::shared_ptr<NetworkConnection> connection) {
    if (!connection) return;

    std::string client_id = connection->get_connection_id();

    {
        std::unique_lock lock(clients_mutex_);
        clients_[client_id] = connection;
    }

    // Notify client connected
    {
        std::lock_guard lock(callbacks_mutex_);
        if (client_connected_callback_) {
            client_connected_callback_(client_id);
        }
    }

    // Set up message callback
    connection->set_message_callback([this, client_id](const NetworkMessage& message) {
        std::lock_guard lock(callbacks_mutex_);
        if (client_message_callback_) {
            client_message_callback_(client_id, message);
        }
    });

    // Set up disconnection callback
    connection->set_connection_callback([this, client_id](bool connected) {
        if (!connected) {
            {
                std::unique_lock lock(clients_mutex_);
                clients_.erase(client_id);

                // Remove from all groups
                for (auto& [group, members] : client_groups_) {
                    members.erase(client_id);
                }
            }

            {
                std::lock_guard lock(callbacks_mutex_);
                if (client_disconnected_callback_) {
                    client_disconnected_callback_(client_id);
                }
            }
        }
    });
}

// CloudClient Implementation
CloudClient::CloudClient(asio::io_context& io_context)
    : io_context_(io_context), http_client_(io_context), health_timer_(io_context) {}

std::future<bool> CloudClient::connect_to_service(const std::string& service_name, const std::string& endpoint) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        auto connection = std::make_unique<WebSocketConnection>(io_context_);
        auto connect_future = connection->connect(endpoint);

        if (connect_future.get()) {
            std::unique_lock lock(connections_mutex_);
            connections_[service_name] = std::move(connection);
            service_endpoints_[service_name] = endpoint;
            service_health_[service_name] = true;

            promise.set_value(true);
        } else {
            promise.set_value(false);
        }
    } catch (const std::exception& e) {
        promise.set_value(false);
    }

    return future;
}

std::future<void> CloudClient::disconnect_from_service(const std::string& service_name) {
    std::promise<void> promise;
    auto future = promise.get_future();

    std::unique_lock lock(connections_mutex_);
    auto it = connections_.find(service_name);

    if (it != connections_.end()) {
        auto disconnect_future = it->second->disconnect();
        disconnect_future.wait();

        connections_.erase(it);
        service_endpoints_.erase(service_name);
        service_health_.erase(service_name);
    }

    promise.set_value();
    return future;
}

bool CloudClient::is_connected_to_service(const std::string& service_name) const {
    std::shared_lock lock(connections_mutex_);
    auto it = connections_.find(service_name);
    return it != connections_.end() && it->second->is_connected();
}

std::future<std::optional<NetworkMessage>> CloudClient::send_request(
    const std::string& service_name,
    const NetworkMessage& request,
    Duration timeout) {

    std::promise<std::optional<NetworkMessage>> promise;
    auto future = promise.get_future();

    std::shared_lock lock(connections_mutex_);
    auto it = connections_.find(service_name);

    if (it != connections_.end() && it->second->is_connected()) {
        auto send_future = it->second->send_message(request);
        if (send_future.get()) {
            // For now, return a placeholder response
            NetworkMessage response = NetworkMessage::create_response(request.id, R"({"status":"success"})");
            promise.set_value(response);
        } else {
            promise.set_value(std::nullopt);
        }
    } else {
        promise.set_value(std::nullopt);
    }

    return future;
}

std::future<bool> CloudClient::send_notification(const std::string& service_name, const NetworkMessage& notification) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    std::shared_lock lock(connections_mutex_);
    auto it = connections_.find(service_name);

    if (it != connections_.end() && it->second->is_connected()) {
        auto send_future = it->second->send_message(notification);
        promise.set_value(send_future.get());
    } else {
        promise.set_value(false);
    }

    return future;
}

std::future<std::vector<std::string>> CloudClient::discover_services() {
    std::promise<std::vector<std::string>> promise;
    auto future = promise.get_future();

    // Placeholder implementation - would query service registry
    std::shared_lock lock(connections_mutex_);
    std::vector<std::string> services;
    services.reserve(connections_.size());

    for (const auto& [service_name, connection] : connections_) {
        services.push_back(service_name);
    }

    promise.set_value(std::move(services));
    return future;
}

std::future<std::optional<std::string>> CloudClient::get_service_endpoint(const std::string& service_name) {
    std::promise<std::optional<std::string>> promise;
    auto future = promise.get_future();

    std::shared_lock lock(connections_mutex_);
    auto it = service_endpoints_.find(service_name);

    if (it != service_endpoints_.end()) {
        promise.set_value(it->second);
    } else {
        promise.set_value(std::nullopt);
    }

    return future;
}

void CloudClient::set_service_event_callback(ServiceEventCallback callback) {
    std::lock_guard lock(callback_mutex_);
    service_event_callback_ = std::move(callback);
}

std::unordered_map<std::string, bool> CloudClient::get_service_health() const {
    std::shared_lock lock(connections_mutex_);
    return service_health_;
}

void CloudClient::enable_health_monitoring(bool enable, Duration check_interval) {
    health_monitoring_enabled_.store(enable);
    health_check_interval_ = check_interval;

    if (enable) {
        monitor_service_health();
    } else {
        health_timer_.cancel();
    }
}

void CloudClient::monitor_service_health() {
    if (!health_monitoring_enabled_.load()) {
        return;
    }

    health_timer_.expires_after(health_check_interval_);
    health_timer_.async_wait([this](std::error_code ec) {
        if (!ec && health_monitoring_enabled_.load()) {
            // Check health of all services
            std::shared_lock lock(connections_mutex_);
            for (const auto& [service_name, connection] : connections_) {
                service_health_[service_name] = connection->is_connected();
            }

            // Schedule next health check
            monitor_service_health();
        }
    });
}

// NetworkManager Implementation
NetworkManager::NetworkManager() : work_guard_(std::make_unique<asio::io_context::work>(io_context_)) {
    http_client_ = std::make_unique<HttpClient>(io_context_);
    cloud_client_ = std::make_unique<CloudClient>(io_context_);
    server_ = std::make_unique<NetworkServer>(io_context_, server_port_);
}

NetworkManager::~NetworkManager() {
    stop();
}

void NetworkManager::start() {
    if (running_.load()) {
        return;
    }

    running_.store(true);

    // Start worker threads
    for (size_t i = 0; i < worker_thread_count_; ++i) {
        worker_threads_.emplace_back(&NetworkManager::run_io_context, this);
    }

    // Start server
    auto start_future = server_->start();
    start_future.wait();
}

void NetworkManager::stop() {
    if (!running_.load()) {
        return;
    }

    running_.store(false);

    // Stop server
    auto stop_future = server_->stop();
    stop_future.wait();

    // Stop io_context
    work_guard_.reset();
    io_context_.stop();

    // Join worker threads
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
}

bool NetworkManager::is_running() const {
    return running_.load();
}

std::unique_ptr<WebSocketConnection> NetworkManager::create_websocket_connection() {
    return std::make_unique<WebSocketConnection>(io_context_);
}

void NetworkManager::set_server_port(uint16_t port) {
    server_port_ = port;
    server_ = std::make_unique<NetworkServer>(io_context_, port);
}

void NetworkManager::set_worker_threads(size_t thread_count) {
    worker_thread_count_ = thread_count;
}

void NetworkManager::run_io_context() {
    while (running_.load()) {
        try {
            io_context_.run();
            break; // io_context stopped normally
        } catch (const std::exception& e) {
            std::cerr << "NetworkManager IO context error: " << e.what() << std::endl;
            // Continue running unless explicitly stopped
        }
    }
}

} // namespace RoboQuant::Trading
