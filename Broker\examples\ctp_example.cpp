#include <iostream>
#include <thread>
#include <chrono>

// 包含 CTP 相关头文件
#include "brokers/CtpBroker.h"
#include "brokers/CtpConfigManager.h"
#include "brokers/CtpOptimizer.h"
#include "core/EventSystem.h"
#include "orders/Order.h"

using namespace RoboQuant::Broker;

int main() {
    std::cout << "=== CTP 接口示例程序 ===\n\n";
    
    try {
        // 1. 注册 CTP 交易接口
        register_ctp_broker();
        std::cout << "? CTP 交易接口已注册\n";
        
        // 2. 启动事件系统
        auto& event_dispatcher = get_event_dispatcher();
        event_dispatcher.start();
        std::cout << "? 事件系统已启动\n";
        
        // 3. 配置管理示例
        std::cout << "\n--- 配置管理示例 ---\n";
        
        auto& config_manager = CtpConfigManager::instance();
        
        // 添加服务器配置
        auto server_config = CtpConfigManager::create_production_server_template();
        server_config.name = "SimNow_Server";
        server_config.trade_front = "tcp://180.168.146.187:10201";
        server_config.md_front = "tcp://180.168.146.187:10211";
        server_config.broker_id = "9999";
        server_config.environment = CtpEnvironment::Simulation;
        config_manager.add_server_config(server_config);
        std::cout << "? 服务器配置已添加: " << server_config.name << "\n";
        
        // 添加账户配置
        auto account_config = CtpConfigManager::create_account_template();
        account_config.account_id = "test_account_001";
        account_config.user_id = "123456";
        account_config.password = "password123";
        account_config.app_id = "simnow_client_test";
        account_config.auth_code = "****************";
        account_config.server_name = "SimNow_Server";
        account_config.is_active = true;
        config_manager.add_account_config(account_config);
        std::cout << "? 账户配置已添加: " << account_config.account_id << "\n";
        
        // 设置交易配置
        auto trading_config = CtpConfigManager::create_trading_template();
        trading_config.request_interval_ms = 500;
        trading_config.auto_query_account = true;
        trading_config.auto_query_position = true;
        config_manager.set_trading_config(trading_config);
        std::cout << "? 交易配置已设置\n";
        
        // 4. 创建 CTP 交易接口
        std::cout << "\n--- 创建 CTP 交易接口 ---\n";
        
        auto ctp_config = config_manager.generate_ctp_config(account_config.account_id);
        auto ctp_broker = std::make_shared<CtpBroker>(ctp_config);
        std::cout << "? CTP 交易接口已创建\n";
        
        // 5. 设置回调函数
        ctp_broker->set_order_callback([](const Order& order) {
            std::cout << "? 订单更新: " << order.order_id() 
                     << " 状态: " << to_string(order.status())
                     << " 已成交: " << order.filled_quantity() << "/" << order.quantity() << "\n";
        });
        
        ctp_broker->set_trade_callback([](const Trade& trade) {
            std::cout << "? 交易回报: " << trade.trade_id() 
                     << " " << to_string(trade.side())
                     << " " << trade.quantity() 
                     << " @ " << trade.price() << "\n";
        });
        
        ctp_broker->set_account_callback([](const Account& account) {
            std::cout << "? 账户更新: " << account.account_id() 
                     << " 余额: " << account.balance().total_balance << "\n";
        });
        
        std::cout << "? 回调函数已设置\n";
        
        // 6. 连接到 CTP 服务器
        std::cout << "\n--- 连接到 CTP 服务器 ---\n";
        
        ConnectionInfo conn_info(BrokerType::CTP, "180.168.146.187", 10201);
        conn_info.username = ctp_config.user_id;
        conn_info.password = ctp_config.password;
        conn_info.extra_params["broker_id"] = ctp_config.broker_id;
        conn_info.extra_params["app_id"] = ctp_config.app_id;
        conn_info.extra_params["auth_code"] = ctp_config.auth_code;
        
        auto connect_result = ctp_broker->connect(conn_info);
        if (!connect_result) {
            std::cerr << "? 连接失败: " << connect_result.error().message << "\n";
            return 1;
        }
        std::cout << "? 已连接到 CTP 服务器\n";
        
        // 7. 认证和登录
        auto auth_result = ctp_broker->authenticate(ctp_config.user_id, ctp_config.password);
        if (!auth_result) {
            std::cerr << "? 认证失败: " << auth_result.error().message << "\n";
            return 1;
        }
        std::cout << "? 认证和登录成功\n";
        
        // 8. 性能优化示例
        std::cout << "\n--- 性能优化示例 ---\n";
        
        auto optimizer = std::make_unique<CtpOptimizer>(ctp_broker);
        optimizer->enable_optimization(true);
        std::cout << "? 性能优化已启用\n";
        
        // 设置监控告警
        optimizer->get_monitor().set_alert_callback([](const std::string& alert_type, const std::string& message) {
            std::cout << "? 告警: [" << alert_type << "] " << message << "\n";
        });
        
        optimizer->get_monitor().start_monitoring(ctp_broker);
        std::cout << "? 性能监控已启动\n";
        
        // 9. 查询账户信息
        std::cout << "\n--- 查询账户信息 ---\n";
        
        auto accounts_result = ctp_broker->query_accounts();
        if (accounts_result) {
            auto accounts = accounts_result.value();
            std::cout << "? 查询到 " << accounts.size() << " 个账户:\n";
            for (const auto& account : accounts) {
                std::cout << "  - " << account.to_string() << "\n";
            }
        } else {
            std::cout << "? 账户查询失败: " << accounts_result.error().message << "\n";
        }
        
        // 10. 查询持仓信息
        std::cout << "\n--- 查询持仓信息 ---\n";
        
        auto positions_result = ctp_broker->query_positions();
        if (positions_result) {
            auto positions = positions_result.value();
            std::cout << "? 查询到 " << positions.size() << " 个持仓:\n";
            for (const auto& position : positions) {
                std::cout << "  - " << position.asset_id() 
                         << " 数量: " << position.quantity()
                         << " 均价: " << position.average_price() << "\n";
            }
        } else {
            std::cout << "? 持仓查询失败: " << positions_result.error().message << "\n";
        }
        
        // 11. 订阅行情数据
        std::cout << "\n--- 订阅行情数据 ---\n";
        
        std::vector<std::string> instruments = {"rb2501", "hc2501", "i2501"};
        for (const auto& instrument : instruments) {
            auto subscribe_result = ctp_broker->subscribe_market_data(instrument);
            if (subscribe_result) {
                std::cout << "? 已订阅行情: " << instrument << "\n";
            } else {
                std::cout << "? 行情订阅失败: " << instrument << " - " 
                         << subscribe_result.error().message << "\n";
            }
        }
        
        // 12. 提交测试订单
        std::cout << "\n--- 提交测试订单 ---\n";
        
        if (!accounts_result.value().empty()) {
            OrderRequest request;
            request.asset_id = "rb2501";
            request.side = OrderSide::Buy;
            request.type = OrderType::Limit;
            request.quantity = 1;
            request.price = 3500.0;
            request.account_id = accounts_result.value()[0].account_id();
            request.strategy_id = "ctp_example";
            
            Order order(request);
            
            auto submit_result = ctp_broker->submit_order(order);
            if (submit_result) {
                std::cout << "? 订单已提交: " << order.order_id() << "\n";
                
                // 等待订单处理
                std::this_thread::sleep_for(std::chrono::seconds(2));
                
                // 查询订单状态
                auto order_result = ctp_broker->query_order(order.order_id());
                if (order_result) {
                    const auto& updated_order = order_result.value();
                    std::cout << "? 订单状态: " << to_string(updated_order.status()) << "\n";
                    if (updated_order.filled_quantity() > 0) {
                        std::cout << "? 成交数量: " << updated_order.filled_quantity() << "\n";
                        std::cout << "? 成交均价: " << updated_order.average_fill_price() << "\n";
                    }
                }
                
                // 如果订单未成交，尝试撤销
                if (order_result && order_result.value().is_active()) {
                    std::cout << "\n--- 撤销订单 ---\n";
                    auto cancel_result = ctp_broker->cancel_order(order);
                    if (cancel_result) {
                        std::cout << "? 撤单请求已发送\n";
                        std::this_thread::sleep_for(std::chrono::seconds(1));
                    }
                }
                
            } else {
                std::cout << "? 订单提交失败: " << submit_result.error().message << "\n";
            }
        }
        
        // 13. 查询交易记录
        std::cout << "\n--- 查询交易记录 ---\n";
        
        auto trades_result = ctp_broker->query_trades();
        if (trades_result) {
            auto trades = trades_result.value();
            std::cout << "? 查询到 " << trades.size() << " 笔交易:\n";
            for (const auto& trade : trades) {
                std::cout << "  - " << trade.trade_id() 
                         << " " << trade.asset_id()
                         << " " << to_string(trade.side())
                         << " " << trade.quantity() 
                         << " @ " << trade.price() << "\n";
            }
        }
        
        // 14. 性能统计报告
        std::cout << "\n--- 性能统计报告 ---\n";
        
        auto performance_report = optimizer->generate_performance_report();
        std::cout << "? 性能报告:\n" << performance_report.dump(2) << "\n";
        
        // 15. 优化建议
        auto suggestions = optimizer->get_optimization_suggestions();
        if (!suggestions.empty()) {
            std::cout << "\n? 优化建议:\n";
            for (const auto& suggestion : suggestions) {
                std::cout << "  - " << suggestion << "\n";
            }
        }
        
        // 16. 保存配置
        std::cout << "\n--- 保存配置 ---\n";
        
        auto save_result = config_manager.save_config("./config/ctp_config.json");
        if (save_result) {
            std::cout << "? 配置已保存到文件\n";
        } else {
            std::cout << "? 配置保存失败: " << save_result.error().message << "\n";
        }
        
        // 17. 清理资源
        std::cout << "\n--- 清理资源 ---\n";
        
        // 停止监控
        optimizer->get_monitor().stop_monitoring();
        
        // 退订行情
        for (const auto& instrument : instruments) {
            ctp_broker->unsubscribe_market_data(instrument);
        }
        
        // 断开连接
        ctp_broker->disconnect();
        
        // 停止事件系统
        event_dispatcher.stop();
        
        std::cout << "? 资源清理完成\n";
        std::cout << "\n? CTP 接口示例程序执行完成\n";
        
    } catch (const std::exception& e) {
        std::cerr << "? 异常: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}
