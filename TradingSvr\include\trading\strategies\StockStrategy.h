/**
 * @file StockStrategy.h
 * @brief Modern stock trading strategy implementation
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "../Strategy.h"
#include "../Types.h"
#include <unordered_set>

namespace RoboQuant::Trading::Strategies {

/**
 * @brief Stock strategy configuration
 */
struct StockStrategyConfig {
    // Entry/Exit criteria
    double long_entry_threshold{0.02};
    double long_exit_threshold{0.01};
    double stop_loss_pct{0.08};
    double take_profit_pct{0.15};
    
    // Position sizing
    double max_position_value{100000.0};
    double max_portfolio_weight{0.05};  // Max 5% per stock
    double risk_per_trade{0.01};        // 1% risk per trade
    
    // Holding period
    Duration min_hold_time{std::chrono::hours(1)};
    Duration max_hold_time{std::chrono::days(30)};
    Duration hold_timeout_check{std::chrono::hours(24)};
    
    // Market filters
    bool avoid_earnings_season{true};
    bool avoid_ex_dividend_date{true};
    std::unordered_set<std::string> avoided_industries;
    
    // Technical filters
    double min_volume{1000000.0};      // Minimum daily volume
    double min_price{5.0};             // Minimum stock price
    double max_price{1000.0};          // Maximum stock price
    double min_market_cap{100000000.0}; // Minimum market cap
    
    // Execution settings
    bool use_limit_orders{true};
    double limit_order_offset{0.002};  // 0.2% offset
    Duration order_timeout{std::chrono::minutes(10)};
    
    // Model settings
    std::string alpha_model;
    std::string risk_model;
    double min_alpha_score{0.6};
    
    // Risk management
    bool enable_sector_limits{true};
    double max_sector_weight{0.20};    // Max 20% per sector
    bool enable_correlation_limits{true};
    double max_correlation{0.7};       // Max correlation between holdings
};

/**
 * @brief Stock strategy data for each asset
 */
struct StockStrategyData {
    AssetId asset_id;
    
    // Alpha signals
    double alpha_score{0.0};
    double momentum_score{0.0};
    double value_score{0.0};
    double quality_score{0.0};
    
    // Risk metrics
    double beta{1.0};
    double volatility{0.0};
    double correlation_to_market{0.0};
    double risk_score{0.0};
    
    // Fundamental data
    double market_cap{0.0};
    double pe_ratio{0.0};
    double book_value{0.0};
    std::string sector;
    std::string industry;
    
    // Position tracking
    std::optional<TimePoint> entry_time;
    std::optional<Price> entry_price;
    std::optional<Price> stop_loss_price;
    std::optional<Price> take_profit_price;
    
    // Performance
    double unrealized_pnl{0.0};
    double realized_pnl{0.0};
    uint32_t trade_count{0};
    double win_rate{0.0};
    
    // Flags
    bool earnings_soon{false};
    bool ex_dividend_soon{false};
    bool high_correlation{false};
    
    [[nodiscard]] bool has_position() const noexcept {
        return entry_time.has_value();
    }
    
    [[nodiscard]] Duration holding_time() const {
        if (!entry_time) return Duration::zero();
        return std::chrono::duration_cast<Duration>(
            std::chrono::system_clock::now() - *entry_time
        );
    }
    
    [[nodiscard]] bool is_hold_timeout() const {
        return holding_time() > std::chrono::days(30); // Default max hold time
    }
};

/**
 * @brief Modern stock trading strategy
 */
class StockStrategy : public Strategy {
public:
    explicit StockStrategy(StrategyConfig config);
    ~StockStrategy() override = default;

    // Strategy identification
    static std::string strategy_name() { return "StockStrategy"; }
    
    // Lifecycle methods
    std::future<bool> initialize() override;
    std::future<void> start() override;
    std::future<void> stop() override;
    void shutdown() override;

    // Event handlers
    void on_market_data(const QuoteData& quote) override;
    void on_bar_data(const BarData& bar) override;

    // Public interface methods
    std::vector<AssetId> get_stock_universe() const;
    std::unordered_map<std::string, double> get_sector_exposures() const;
    void rebalance_portfolio();
    void on_order_fill(const OrderFill& fill) override;
    void on_order_update(const Order& order) override;
    void on_timer(const std::string& timer_id) override;

    // Configuration
    void set_stock_config(const StockStrategyConfig& config);
    [[nodiscard]] const StockStrategyConfig& get_stock_config() const noexcept;

    // Strategy data access
    [[nodiscard]] std::optional<StockStrategyData> get_strategy_data(const AssetId& asset_id) const;
    [[nodiscard]] std::vector<StockStrategyData> get_all_strategy_data() const;

    // Manual operations
    std::future<bool> manual_open_position(const AssetId& asset_id, Quantity quantity = 0, bool force = false);
    std::future<bool> manual_close_position(const AssetId& asset_id);
    std::future<bool> close_all_positions();

    // Portfolio analysis
    [[nodiscard]] std::unordered_map<std::string, double> get_sector_weights() const;
    [[nodiscard]] double get_portfolio_beta() const;
    [[nodiscard]] std::vector<std::pair<AssetId, double>> get_correlation_matrix() const;

private:
    // Strategy logic
    void process_asset(const AssetId& asset_id, const QuoteData& quote);
    void process_asset(const AssetId& asset_id, const BarData& bar);
    
    // Signal generation
    [[nodiscard]] bool should_enter_long(const AssetId& asset_id) const;
    [[nodiscard]] bool should_exit_position(const AssetId& asset_id) const;
    [[nodiscard]] bool should_add_to_position(const AssetId& asset_id) const;
    [[nodiscard]] bool should_reduce_position(const AssetId& asset_id) const;

    // Additional helper methods needed by implementation
    void initialize_universe();
    void initialize_models();
    void screen_universe();
    bool generate_long_signal(const AssetId& asset_id) const;
    bool generate_exit_signal(const AssetId& asset_id) const;
    bool is_asset_in_universe(const AssetId& asset_id) const;
    bool check_position_risk(const AssetId& asset_id, Quantity quantity) const;
    bool check_position_constraints(const AssetId& asset_id, Quantity quantity) const;
    bool check_sector_constraints(const AssetId& asset_id, Quantity quantity) const;
    bool check_correlation_constraints(const AssetId& asset_id, Quantity quantity) const;
    
    // Alpha model integration
    [[nodiscard]] double calculate_alpha_score(const AssetId& asset_id) const;
    [[nodiscard]] double get_momentum_score(const AssetId& asset_id) const;
    [[nodiscard]] double get_value_score(const AssetId& asset_id) const;
    [[nodiscard]] double get_quality_score(const AssetId& asset_id) const;
    
    // Risk management
    [[nodiscard]] bool check_sector_limits(const AssetId& asset_id, Quantity quantity) const;
    [[nodiscard]] bool check_correlation_limits(const AssetId& asset_id) const;
    [[nodiscard]] bool check_fundamental_filters(const AssetId& asset_id) const;
    [[nodiscard]] bool is_earnings_season(const AssetId& asset_id) const;
    [[nodiscard]] bool is_ex_dividend_soon(const AssetId& asset_id) const;
    
    // Position sizing
    [[nodiscard]] Quantity calculate_position_size(const AssetId& asset_id, Price price) const;
    [[nodiscard]] Price calculate_stop_loss_price(const AssetId& asset_id, Price entry_price) const;
    [[nodiscard]] Price calculate_take_profit_price(const AssetId& asset_id, Price entry_price) const;
    
    // Order management
    std::future<std::optional<OrderId>> submit_buy_order(const AssetId& asset_id, Quantity quantity);
    std::future<std::optional<OrderId>> submit_sell_order(const AssetId& asset_id, Quantity quantity);
    
    // Position management
    std::future<bool> open_long_position(const AssetId& asset_id, Quantity quantity = 0);
    std::future<bool> close_position(const AssetId& asset_id, const std::string& reason = "");
    
    // Data management
    void update_strategy_data(const AssetId& asset_id, const QuoteData& quote);
    void update_strategy_data(const AssetId& asset_id, const BarData& bar);
    void update_fundamental_data(const AssetId& asset_id);
    void initialize_strategy_data(const AssetId& asset_id);
    
    // Timer handlers
    void setup_timers();
    void on_hold_timeout_check();
    void on_market_close_check();
    void on_risk_check();
    void on_fundamental_update();
    
    // Utility methods
    [[nodiscard]] bool is_trading_time() const;
    [[nodiscard]] bool is_market_open() const;
    [[nodiscard]] Price get_current_price(const AssetId& asset_id) const;
    [[nodiscard]] std::string get_sector(const AssetId& asset_id) const;
    [[nodiscard]] double get_correlation(const AssetId& asset1, const AssetId& asset2) const;

private:
    StockStrategyConfig stock_config_;

    mutable std::shared_mutex strategy_data_mutex_;
    std::unordered_map<AssetId, StockStrategyData> strategy_data_;

    mutable std::shared_mutex price_cache_mutex_;
    std::unordered_map<AssetId, QuoteData> price_cache_;

    // Stock universe tracking
    mutable std::shared_mutex universe_mutex_;
    std::vector<AssetId> stock_universe_;

    // Sector exposure tracking
    mutable std::shared_mutex sector_mutex_;
    std::unordered_map<std::string, double> sector_exposures_;
    
    // Sector tracking
    mutable std::shared_mutex sector_weights_mutex_;
    std::unordered_map<std::string, double> sector_weights_;
    
    // Correlation matrix
    mutable std::shared_mutex correlation_mutex_;
    std::unordered_map<std::pair<AssetId, AssetId>, double> correlation_matrix_;
    
    // Timers
    std::vector<std::string> active_timers_;
    
    // Performance tracking
    mutable std::mutex performance_mutex_;
    PerformanceMetrics overall_performance_;
};

/**
 * @brief Stock strategy factory
 */
class StockStrategyFactory : public StrategyFactory {
public:
    UniquePtr<Strategy> create_strategy(const StrategyConfig& config) override;
    std::vector<std::string> get_supported_strategies() const override;
};

} // namespace RoboQuant::Trading::Strategies
